<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.isoftstone.hig</groupId>
        <artifactId>ailogistics</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <groupId>com.wanlianyida</groupId>
    <artifactId>lgi-orch-user</artifactId>
    <version>1.0-SNAPSHOT</version>
    <description>3pl用户端编排服务</description>

    <properties>
        <xxl.job.version>2.3.1</xxl.job.version>
        <swagger2.version>2.9.2</swagger2.version>
        <swagger.version>1.6.6</swagger.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-httpclient</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida.framework</groupId>
            <artifactId>lgi-auth</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>lgi-core-gps-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>lgi-core-mms-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>lgi-core-tms-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>lgi-core-tcs-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>lgi-core-eval-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>lgi-core-platform-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>fss-base-file-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>lgi-base-bi-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>fss-base-cont-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>fss-base-log-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>${swagger.version}</version>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>fss-base-aiadv-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <!--            <version>2.0.53</version>-->
        </dependency>
        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>lgi-core-lsds-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>lgi-core-woa-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>lgi-core-crm-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida.framework</groupId>
            <artifactId>lgi-common</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida.framework</groupId>
            <artifactId>lgi-core</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>lgi-core-oms-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>lgi-core-qrs-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>lgi-core-cmd-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>gtsp-base-its-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>lgi-base-support-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>lgi-core-bms-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>lgi-core-rms-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>fss-base-mdm-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.plumelog</groupId>
            <artifactId>plumelog-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>com.plumelog</groupId>
            <artifactId>plumelog-trace</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wanlianyida</groupId>
            <artifactId>lgi-base-dms-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
