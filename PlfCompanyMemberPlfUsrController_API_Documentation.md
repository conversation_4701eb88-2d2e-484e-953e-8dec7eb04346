# PlfCompanyMemberPlfUsrController API 接口文档

## 概述
企业员工与平台用户关系管理接口，提供企业员工列表查询、平台用户关联管理、绑定/解绑等功能。

**Base URL**: `/plat-company-member-plf-usr`

## 接口列表

### 1. 获取企业员工列表
**接口路径**: `POST /plat-company-member-plf-usr/company-member-page`

**接口描述**: 分页查询企业员工列表

**请求参数**:
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "filterModel": {
    "employeeUserName": "员工姓名",
    "employeeAccountName": "员工账户名", 
    "employeeMobile": "员工手机号",
    "userStatus": "员工状态",
    "companyId": "企业ID",
    "userBaseId": "用户基础ID"
  }
}
```

**请求参数说明**:
- `pageNum`: 页码（必填）
- `pageSize`: 每页大小（必填）
- `filterModel.employeeUserName`: 员工姓名（可选）
- `filterModel.employeeAccountName`: 员工账户名（可选）
- `filterModel.employeeMobile`: 员工手机号（可选）
- `filterModel.userStatus`: 员工状态（可选）

**响应结果**:
```json
{
  "succeed": true,
  "errCode": null,
  "errMsg": null,
  "model": [
    {
      // CompanyMemberDTO 字段（具体字段需要查看平台API定义）
    }
  ],
  "total": 100
}
```

### 2. 获取平台个人关联的企业员工列表
**接口路径**: `POST /plat-company-member-plf-usr/plf-usr-company-member-page`

**接口描述**: 分页查询平台个人用户关联的企业员工列表

**请求参数**:
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "filterModel": {
    "plfUserId": "平台用户ID",
    "companyId": "企业ID",
    "userBaseId": "用户基础ID"
  }
}
```

**请求参数说明**:
- `pageNum`: 页码（必填）
- `pageSize`: 每页大小（必填）
- `filterModel.plfUserId`: 平台用户ID（可选）

**响应结果**:
```json
{
  "succeed": true,
  "errCode": null,
  "errMsg": null,
  "model": [
    {
      // PlfUsrCompanyMemberDTO 字段（具体字段需要查看平台API定义）
    }
  ],
  "total": 50
}
```

### 3. 获取当前企业员工绑定的个人账户信息
**接口路径**: `POST /plat-company-member-plf-usr/find-plf-usr-by-user-base-id`

**接口描述**: 获取当前企业的员工绑定的个人账户信息

**请求参数**: 无请求体

**响应结果**:
```json
{
  "succeed": true,
  "errCode": null,
  "errMsg": null,
  "model": [
    {
      // FindPlfUsrByUserBaseIdDTO 字段（具体字段需要查看平台API定义）
    }
  ]
}
```

### 4. 通过手机号获取中台个人账户信息
**接口路径**: `POST /plat-company-member-plf-usr/find-plf-usr-by-mobile`

**接口描述**: 根据手机号查询中台个人账户信息

**请求参数**:
```json
{
  "mobile": "***********",
  "companyId": "企业ID",
  "userBaseId": "用户基础ID"
}
```

**请求参数说明**:
- `mobile`: 手机号（必填，不能为空）

**响应结果**:
```json
{
  "succeed": true,
  "errCode": null,
  "errMsg": null,
  "model": [
    {
      "plfUserId": "平台用户ID",
      "plfUserLoginId": "平台用户登录ID",
      "plfUserName": "平台用户姓名",
      "plfUserAccountName": "平台用户账户名",
      "plfUserIdCardNo": "平台用户身份证号",
      "plfUserMobile": "平台用户手机号",
      "employeeUserName": "员工姓名",
      "employeeAccountName": "员工账户名",
      "employeeMobile": "员工手机号"
    }
  ]
}
```

### 5. 绑定接口-发起邀请
**接口路径**: `POST /plat-company-member-plf-usr/bind`

**接口描述**: 发起企业员工与平台用户的绑定邀请

**请求参数**:
```json
{
  "companyMemberId": "企业员工ID",
  "plfUserId": "平台用户ID",
  "plfUserLoginId": "平台用户登录ID",
  "plfUserName": "平台用户姓名",
  "plfUserAccountName": "平台用户账户名",
  "plfUserIdCardNo": "平台用户身份证号",
  "plfUserMobile": "平台用户手机号",
  "companyId": "企业ID",
  "userBaseId": "用户基础ID"
}
```

**请求参数说明**:
- `companyMemberId`: 企业员工ID（必填，不能为空）
- `plfUserId`: 平台用户ID（必填，不能为空）
- `plfUserLoginId`: 平台用户登录ID（必填，不能为空）
- `plfUserName`: 平台用户姓名（必填，不能为空）
- `plfUserAccountName`: 平台用户账户名（必填，不能为空）
- `plfUserIdCardNo`: 平台用户身份证号（必填，不能为空）
- `plfUserMobile`: 平台用户手机号（必填，不能为空）

**响应结果**:
```json
{
  "succeed": true,
  "errCode": null,
  "errMsg": null,
  "model": null
}
```

### 6. 解绑
**接口路径**: `POST /plat-company-member-plf-usr/unbind`

**接口描述**: 解除企业员工与平台用户的绑定关系

**请求参数**:
```json
{
  "companyMemberId": "企业员工ID",
  "lastUpdaterId": "最后更新人ID",
  "lastUpdaterName": "最后更新人姓名",
  "companyId": "企业ID",
  "userBaseId": "用户基础ID"
}
```

**请求参数说明**:
- `companyMemberId`: 企业员工ID（必填，不能为空）
- `lastUpdaterId`: 最后更新人ID（必填，不能为空）
- `lastUpdaterName`: 最后更新人姓名（必填，不能为空）

**响应结果**:
```json
{
  "succeed": true,
  "errCode": null,
  "errMsg": null,
  "model": null
}
```

### 7. 同意/拒绝绑定
**接口路径**: `POST /plat-company-member-plf-usr/agree-or-refuse-bind`

**接口描述**: 同意或拒绝绑定邀请

**请求参数**:
```json
{
  "companyMemberId": "企业员工ID",
  "bindType": "10",
  "lastUpdaterId": "最后更新人ID", 
  "lastUpdaterName": "最后更新人姓名",
  "companyId": "企业ID",
  "userBaseId": "用户基础ID"
}
```

**请求参数说明**:
- `companyMemberId`: 企业员工ID（必填，不能为空）
- `bindType`: 绑定类型（必填，不能为空）
  - `10`: 同意
  - `20`: 拒绝
- `lastUpdaterId`: 最后更新人ID（必填，不能为空）
- `lastUpdaterName`: 最后更新人姓名（必填，不能为空）

**响应结果**:
```json
{
  "succeed": true,
  "errCode": null,
  "errMsg": null,
  "model": null
}
```

## 绑定状态说明

根据常量类 `PlfCompanyMemberPlfUsrRelConstants.BindStatus`：

- `10`: 未绑定 (UNBOUND)
- `20`: 待确定 (PENDING)
- `30`: 绑定成功 (BOUND)
- `40`: 已解除 (DISCONNECTED)
- `50`: 拒绝绑定 (REJECTED)

## 通用响应格式

所有接口都使用统一的响应格式 `ResultMode<T>`：

```json
{
  "succeed": true,        // 是否成功
  "errCode": "错误码",     // 错误码（成功时为null）
  "errMsg": "错误信息",    // 错误信息（成功时为null）
  "model": [],           // 返回数据（列表或对象）
  "total": 100           // 总数（分页查询时返回）
}
```

## 错误处理

当接口调用失败时，会返回相应的错误码和错误信息：

- 参数校验失败：返回具体的校验错误信息
- 业务逻辑错误：返回对应的业务错误码和信息
- 系统异常：返回系统级错误信息

## 注意事项

1. 所有接口都使用 POST 方法
2. 请求和响应的 Content-Type 为 `application/json`
3. 分页查询的页码从1开始
4. 所有必填参数都有相应的校验注解
5. 绑定操作需要按照业务流程进行：发起邀请 → 同意/拒绝 → 绑定成功/失败
