spring:
  cloud:
    nacos:
      username: nacos
      password: wlyd2018
      server-addr: 172.31.45.168:8848
      config:
        name: lgi-orch-user.yaml
        enabled: false
        file-extension: yaml
        namespace: lgi_settlement
        refresh-enabled: true
        # 多服务间共享的配置列表
        shared-configs:
          # 要共享的配置文件id
          - data-id: common.yaml
            # 是否动态刷新，默认为false
            refresh: true
logging:
  config: classpath:log4j2.xml
