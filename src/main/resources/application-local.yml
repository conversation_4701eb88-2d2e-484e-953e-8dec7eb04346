register-center-ip-port: eureka.delta.10000da.vip:9000
eureka:
  client:
    fetch-registry: true
    register-with-eureka: false
    service-url:
      defaultZone: http://${register-center-ip-port}/eureka/
  instance:
    instance-id: ${spring.cloud.client.ip-address}:${spring.application.name}:${server.port}
    lease-expiration-duration-in-seconds: 10
    lease-renewal-interval-in-seconds: 5
    metadata-map:
      management:
        context-path: ${server.servlet.context-path}/actuator
    prefer-ip-address: true
jwt:
  loginPath: /login
  expiration: 10080
  header: Authorization
  secret: aaHR0cHM6Ly9teS5vc2NoaW5hLm5ldC91LzM2ODE4Njg

bi:
  workbench:
    issueGoods: http://wlyd-bigdata-bj-dev.wanlianyida.com/dev/goods/source/today/release
    runningOrder: http://wlyd-bigdata-bj-dev.wanlianyida.com/dev/order/perform
    waitingReceiveOrder: http://wlyd-bigdata-bj-dev.wanlianyida.com/dev/order/pending
    assigningVehicleOrder: http://wlyd-bigdata-bj-dev.wanlianyida.com/dev/order/dispatching
    transportingWaybill: http://wlyd-bigdata-bj-dev.wanlianyida.com/dev/order/transporting
    waitingSettleWaybill: http://wlyd-bigdata-bj-dev.wanlianyida.com/dev/order/settlement/wait
    waitingAuditWaybill: http://wlyd-bigdata-bj-dev.wanlianyida.com/dev/order/approval/wait
    auditRejectWaybill: http://wlyd-bigdata-bj-dev.wanlianyida.com/dev/order/notapproval
    waitingPaymentWaybill: http://wlyd-bigdata-bj-dev.wanlianyida.com/dev/order/payment/wait
    waitingInvoiceWaybill: http://wlyd-bigdata-bj-dev.wanlianyida.com/dev/order/invoice/wait
    noUnloadExpire: http://wlyd-bigdata-bj-dev.wanlianyida.com/dev/transport/nounload
    noSignExpire: http://wlyd-bigdata-bj-dev.wanlianyida.com/dev/transport/unreceipt
    noSettleExpire: http://wlyd-bigdata-bj-dev.wanlianyida.com/dev/order/unsettled/timeout
    noPaymentExpire: http://wlyd-bigdata-bj-dev.wanlianyida.com/dev/order/unpayment/timeout
    noInvoiceExpire: http://wlyd-bigdata-bj-dev.wanlianyida.com/dev/order/uninvoice/timeout
    waitingPreAudit: http://wlyd-bigdata-bj-dev.wanlianyida.com/dev/examine/preaudit/wait
    paymentWaitingAudit: http://wlyd-bigdata-bj-dev.wanlianyida.com/dev/examine/payment/wait
    invoiceWaitingAudit: http://wlyd-bigdata-bj-dev.wanlianyida.com/dev/examine/invoice/wait
    newShipper: http://wlyd-bigdata-bj-dev.wanlianyida.com/dev/customer/supplier/new
    newCustomer: http://wlyd-bigdata-bj-dev.wanlianyida.com/dev/customer/carrier/new
    newCarrier: http://wlyd-bigdata-bj-dev.wanlianyida.com/dev/tcs/dirver/sign/new
    newVehicle: http://wlyd-bigdata-bj-dev.wanlianyida.com/dev/tcs/car/sign/new
    collectData: http://wlyd-bigdata-bj-dev.wanlianyida.com/dev/transport/summary
spring:
  redis:
    host: *************
    port: 6379
    maxActive: 200
    maxIdle: 20
    maxWait: 3000
    minIdle: 5
    password: wlyd2019
    testOnBorrow: true
    testOnReturn: true
    timeout: 3000

gtsp:
  api:
    url:
      its: http://wlyd-hw-base-api.10000da.vip
      cont: http://wlyd-hw-base-api.10000da.vip
its:
  appCode: 57d296770aaa4abcb299cc532279890d