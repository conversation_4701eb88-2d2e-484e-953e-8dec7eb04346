register-center-ip-port: *************:21000
eureka:
  client:
    fetch-registry: true
    register-with-eureka: true
    service-url:
      defaultZone: http://${register-center-ip-port}/eureka/
  instance:
    instance-id: ${spring.cloud.client.ip-address}:${spring.application.name}:${server.port}
    lease-expiration-duration-in-seconds: 10
    lease-renewal-interval-in-seconds: 5
    metadata-map:
      management:
        context-path: ${server.servlet.context-path}/actuator
    prefer-ip-address: true
jwt:
  loginPath: /login
  expiration: 10080
  header: Authorization
  secret: aaHR0cHM6Ly9teS5vc2NoaW5hLm5ldC91LzM2ODE4Njg

bi:
  workbench:
    issueGoods: https://wlyd-bigdata-bj.wanlianyida.com/goods/source/today/release
    runningOrder: https://wlyd-bigdata-bj.wanlianyida.com/order/perform
    waitingReceiveOrder: https://wlyd-bigdata-bj.wanlianyida.com/order/pending
    assigningVehicleOrder: https://wlyd-bigdata-bj.wanlianyida.com/order/dispatching
    transportingWaybill: https://wlyd-bigdata-bj.wanlianyida.com/order/transporting
    waitingSettleWaybill: https://wlyd-bigdata-bj.wanlianyida.com/order/settlement/wait
    waitingAuditWaybill: https://wlyd-bigdata-bj.wanlianyida.com/order/approval/wait
    auditRejectWaybill: https://wlyd-bigdata-bj.wanlianyida.com/order/notapproval
    waitingPaymentWaybill: https://wlyd-bigdata-bj.wanlianyida.com/order/payment/wait
    waitingInvoiceWaybill: https://wlyd-bigdata-bj.wanlianyida.com/order/invoice/wait
    noUnloadExpire: https://wlyd-bigdata-bj.wanlianyida.com/transport/nounload
    noSignExpire: https://wlyd-bigdata-bj.wanlianyida.com/transport/unreceipt
    noSettleExpire: https://wlyd-bigdata-bj.wanlianyida.com/order/unsettled/timeout
    noPaymentExpire: https://wlyd-bigdata-bj.wanlianyida.com/order/unpayment/timeout
    noInvoiceExpire: https://wlyd-bigdata-bj.wanlianyida.com/order/uninvoice/timeout
    waitingPreAudit: https://wlyd-bigdata-bj.wanlianyida.com/examine/preaudit/wait
    paymentWaitingAudit: https://wlyd-bigdata-bj.wanlianyida.com/examine/payment/wait
    invoiceWaitingAudit: https://wlyd-bigdata-bj.wanlianyida.com/examine/invoice/wait
    newShipper: https://wlyd-bigdata-bj.wanlianyida.com/customer/supplier/new
    newCustomer: https://wlyd-bigdata-bj.wanlianyida.com/customer/carrier/new
    newCarrier: https://wlyd-bigdata-bj.wanlianyida.com/tcs/dirver/sign/new
    newVehicle: https://wlyd-bigdata-bj.wanlianyida.com/tcs/car/sign/new
    collectData: https://wlyd-bigdata-bj.wanlianyida.com/transport/summary
