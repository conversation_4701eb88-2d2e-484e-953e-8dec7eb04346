package com.wanlianyida.user.domain.service;

import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.dto.analysis.WaybillSummaryDTO;
import com.wanlianyida.user.application.model.query.analysis.WaybillSummaryQuery;
import com.wanlianyida.user.infrastructure.exchange.BiExchangeService;
import com.wanlianyida.user.infrastructure.exchange.DmsExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

@Slf4j
@Service
public class WaybillSummaryDomain {

    @Resource
    private BiExchangeService biExchangeService;

    @Resource
    private DmsExchangeService dmsExchangeService;
    /**
     * 托运人运单数据汇总
     * @param pagingInfo
     * @return
     */
    public ResultMode<Map<String, Object>> shipperWaybillList(PagingInfo<Map<String, Object>> pagingInfo) {
        return biExchangeService.shipperWaybillList(pagingInfo);
    }

    /**
     *
     * @param queryPagingInfo
     * @return
     */
    public ResultMode<WaybillSummaryDTO> waybillList(PagingInfo<WaybillSummaryQuery> queryPagingInfo) {
        return dmsExchangeService.waybillList(queryPagingInfo);
    }

}
