package com.wanlianyida.user.application.validator;

import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.command.lsds.publish.GoodsSourcePublishCommand;
import com.wanlianyida.user.application.model.dto.cont.CheckContractSignDTO;
import com.wanlianyida.user.application.model.query.cont.CheckContractSignQuery;
import com.wanlianyida.user.application.service.cont.ContContractAppService;
import com.wanlianyida.user.infrastructure.enums.ContractTypeEnum;
import com.wanlianyida.user.infrastructure.enums.FreightTypeEnum;
import com.wanlianyida.user.infrastructure.enums.GoodsPublisherTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 合同校验器
 * 负责货源发布时的合同校验逻辑
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ContractValidator {

    @Resource
    private ContContractAppService contContractAppService;

    /**
     * 校验合同签署情况
     *
     * @param command   货源发布命令
     * @param companyId 公司ID
     * @return 校验结果，包含合同ID（如果存在）
     */
    public ContractValidationResult validateContract(GoodsSourcePublishCommand command, String companyId) {
        // 只有司机货源且为网络货运模式才需要校验合同
        if (!needContractValidation(command)) {
            return ContractValidationResult.success();
        }

        try {
            CheckContractSignQuery query = buildContractQuery(command, companyId);
            ResultMode<CheckContractSignDTO> resultMode = contContractAppService.checkContractSign(query);
            
            if (!resultMode.isSucceed()) {
                return ContractValidationResult.failure(resultMode.getErrMsg());
            }

            CheckContractSignDTO contractSignDTO = IterUtil.getFirst(resultMode.getModel());
            String contractId = extractContractId(contractSignDTO);
            
            return ContractValidationResult.success(contractId);
            
        } catch (Exception e) {
            log.error("合同校验异常, companyId: {}, command: {}", companyId, command, e);
            return ContractValidationResult.failure("合同校验异常，请稍后重试");
        }
    }

    /**
     * 判断是否需要进行合同校验
     */
    private boolean needContractValidation(GoodsSourcePublishCommand command) {
        return StrUtil.equals(command.getPublisherType(), GoodsPublisherTypeEnum.DFQ.getCode()) 
                && StrUtil.equals(command.getFreightType(), FreightTypeEnum.NETWORK_MODE.getCode());
    }

    /**
     * 构建合同校验查询对象
     */
    private CheckContractSignQuery buildContractQuery(GoodsSourcePublishCommand command, String companyId) {
        CheckContractSignQuery query = new CheckContractSignQuery();
        query.setContractType(ContractTypeEnum.DISPATCH.getCode());
        query.setContractNature("20"); // 长期合同
        query.setPartyACompanyId(companyId);
        query.setPartyBCompanyId(command.getNetworkMainBodyId());
        
        // 设置时间范围，转换为日期格式
        query.setStartTime(DateUtil.parse(DateUtil.formatDate(command.getReleaseDate()), "yyyy-MM-dd"));
        query.setEndTime(DateUtil.parse(DateUtil.formatDate(command.getArriveDate()), "yyyy-MM-dd"));
        
        return query;
    }

    /**
     * 提取合同ID
     */
    private String extractContractId(CheckContractSignDTO contractSignDTO) {
        if (ObjUtil.isNotNull(contractSignDTO) && StrUtil.isNotBlank(contractSignDTO.getContractId())) {
            return contractSignDTO.getContractId();
        }
        return "";
    }

    /**
     * 合同校验结果
     */
    public static class ContractValidationResult {
        private final boolean success;
        private final String errorMessage;
        private final String contractId;

        private ContractValidationResult(boolean success, String errorMessage, String contractId) {
            this.success = success;
            this.errorMessage = errorMessage;
            this.contractId = contractId;
        }

        public static ContractValidationResult success() {
            return new ContractValidationResult(true, null, "");
        }

        public static ContractValidationResult success(String contractId) {
            return new ContractValidationResult(true, null, contractId);
        }

        public static ContractValidationResult failure(String errorMessage) {
            return new ContractValidationResult(false, errorMessage, "");
        }

        public boolean isSuccess() {
            return success;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public String getContractId() {
            return contractId;
        }

        public boolean hasContract() {
            return StrUtil.isNotBlank(contractId);
        }
    }
}
