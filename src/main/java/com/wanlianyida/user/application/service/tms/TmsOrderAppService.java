package com.wanlianyida.user.application.service.tms;

import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.dto.tms.OrderListQueryDTO;
import com.wanlianyida.user.application.model.query.tms.OrderListQuery;
import com.wanlianyida.user.infrastructure.exchange.TmsExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class TmsOrderAppService {

    @Resource
    private TmsExchangeService tmsExchangeService;

    /**
     * 查询订单列表
     * @param pageInfo
     * @return
     */
    public ResultMode<OrderListQueryDTO> orderListQuery(PagingInfo<OrderListQuery> pageInfo) {
        return tmsExchangeService.orderListQuery(pageInfo);
    }
}
