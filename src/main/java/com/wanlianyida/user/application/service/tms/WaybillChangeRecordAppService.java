package com.wanlianyida.user.application.service.tms;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.crm.api.model.dto.CrmCompanyLineAddressDTO;
import com.wanlianyida.crm.api.model.dto.CrmDisAndTimeDTO;
import com.wanlianyida.crm.api.model.query.CrmGetDisAndTimeQuery;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.framework.lgicore.utils.JwtUtil;
import com.wanlianyida.platform.api.model.dto.PlatformUmCompanyDTO;
import com.wanlianyida.platform.api.model.query.ParaValueByOperIdAndParaKeyQuery;
import com.wanlianyida.rms.api.inter.model.command.TransportDistanceCommand;
import com.wanlianyida.rms.api.inter.model.command.TransportDistanceParamCommand;
import com.wanlianyida.rms.api.inter.model.dto.RiskExecInfoDTO;
import com.wanlianyida.tms.api.model.command.CrmCompanyLineAddressCommand;
import com.wanlianyida.tms.api.model.command.EndAddressChangeCommand;
import com.wanlianyida.tms.api.model.dto.TmsWaybillAddressDTO;
import com.wanlianyida.tms.api.model.dto.TmsWaybillDetailDTO;
import com.wanlianyida.tms.api.model.dto.WaybillChangeRecordDTO;
import com.wanlianyida.user.application.model.command.tms.ExterEndAddressChangeCommand;
import com.wanlianyida.user.application.model.query.tms.ExterLatestChangeRecordQuery;
import com.wanlianyida.user.infrastructure.exception.UserErrorCode;
import com.wanlianyida.user.infrastructure.exchange.CrmExchangeService;
import com.wanlianyida.user.infrastructure.exchange.PlatformExchangeService;
import com.wanlianyida.user.infrastructure.exchange.RmsExchangeService;
import com.wanlianyida.user.infrastructure.exchange.TmsExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

import javax.annotation.Resource;

@Slf4j
@Service
public class WaybillChangeRecordAppService {

    @Resource
    private TmsExchangeService tmsExchangeService;

    @Resource
    private CrmExchangeService crmExchangeService;

    @Resource
    private PlatformExchangeService platformExchangeService;

    @Resource
    private RmsExchangeService rmsExchangeService;

    /**
     * 修改目的地
     * @param command
     * @return
     */
    public ResultMode<?> addEndAddressChange(ExterEndAddressChangeCommand command) {
        //获取货源详情
        TmsWaybillDetailDTO waybillDetail = tmsExchangeService.getWaybillDetail(command.getWaybillId());
        if (ObjUtil.isNull(waybillDetail)) {
            return ResultMode.fail(UserErrorCode.WAYBILL_NOT_EXIST.getCode(), UserErrorCode.WAYBILL_NOT_EXIST.getMsg());
        }
        String shipperCompanyId = "";
        if ("0".equals(waybillDetail.getGoodsSourceType())) {
            shipperCompanyId = waybillDetail.getRealityCompanyId();
        } else {
            shipperCompanyId = waybillDetail.getGoodsCompanyId();
        }
        PlatformUmCompanyDTO company = platformExchangeService.getCompanyByModel(shipperCompanyId);
        if (ObjUtil.isNull(company)) {
            return ResultMode.fail(UserErrorCode.COMPANY_NOT_EXIST.getCode(), UserErrorCode.COMPANY_NOT_EXIST.getMsg());
        }
        //校验风控运距
        TransportDistanceParamCommand transportDistanceParamCommand = createTransportDistanceParam(waybillDetail,command.getEndAddressId(),shipperCompanyId);
        ResultMode<RiskExecInfoDTO> riskResultMode = rmsExchangeService.distanceRuleCalculate(transportDistanceParamCommand);
        if (!riskResultMode.isSucceed()) {
            return ResultMode.fail(UserErrorCode.RISK_REQUEST_FAIL.getCode(), UserErrorCode.RISK_REQUEST_FAIL.getMsg());
        }
        if (CollectionUtil.isNotEmpty(riskResultMode.getModel())) {
            RiskExecInfoDTO riskExecInfoDTO = riskResultMode.getModel().get(0);
            if ("400".equals(riskExecInfoDTO.getRiskLevel())) {
                return ResultMode.fail(UserErrorCode.RISK_CALCULATE_FAIL.getCode(), riskExecInfoDTO.getRiskContent());
            }
        }
        //获取开关
        ParaValueByOperIdAndParaKeyQuery query = new ParaValueByOperIdAndParaKeyQuery();
        query.setParameterKey("allow_modify_waybill_end_addr_flag");
        query.setOperationMainBodyId(waybillDetail.getNetworkMainBodyId());
        String allowModifyWaybillEndAddrFlag = platformExchangeService.getParaValueByOperIdAndParaKey(query);
        //获取线路
        TmsWaybillAddressDTO waybillAddressDetail = tmsExchangeService.getWaybillAddressDetail(waybillDetail.getEndAddressId());
        CrmCompanyLineAddressDTO beforeLine = crmExchangeService.getCrmCompanyLine(waybillAddressDetail.getShorthandId());
        log.info("beforeLine:{}", JSONUtil.toJsonStr(beforeLine));
        CrmCompanyLineAddressDTO afterLine = crmExchangeService.getCrmCompanyLine(command.getEndAddressId());
        log.info("afterLine:{}", JSONUtil.toJsonStr(afterLine));
        EndAddressChangeCommand apiCommand = BeanUtil.toBean(command, EndAddressChangeCommand.class);
        apiCommand.setCreatorId(JwtUtil.getTokenInfo().getUserBaseId());
        apiCommand.setCreatorName(JwtUtil.getTokenInfo().getUsername());
        apiCommand.setCreatorLoginName(JwtUtil.getTokenInfo().getLoginName());
        apiCommand.setApplyCompanyId(JwtUtil.getTokenInfo().getCompanyId());
        apiCommand.setApplyCompanyName(JwtUtil.getTokenInfo().getCompanyName());
        apiCommand.setApplyCompanyShortName(JwtUtil.getTokenInfo().getCompanyShortName());
        apiCommand.setShipperCompanyId(shipperCompanyId);
        apiCommand.setShipperCompanyName(company.getCompanyName());
        apiCommand.setShipperCompanyShortName(company.getCompanyShortName());
        apiCommand.setAllowModifyWaybillEndAddrFlag(allowModifyWaybillEndAddrFlag);
        apiCommand.setEndCompanyLineAddressBefore(BeanUtil.toBean(beforeLine, CrmCompanyLineAddressCommand.class));
        apiCommand.setEndCompanyLineAddressAfter(BeanUtil.toBean(afterLine, CrmCompanyLineAddressCommand.class));
        return tmsExchangeService.addEndAddressChange(apiCommand);
    }

    private TransportDistanceParamCommand createTransportDistanceParam(TmsWaybillDetailDTO waybillDetailDTO, String endAddressId,String shipperCompanyId) {
        TransportDistanceParamCommand transportDistanceParamCommand = new TransportDistanceParamCommand();
        transportDistanceParamCommand.setBussSceneType("B1230");
        transportDistanceParamCommand.setRiskSceneType("R288");
        transportDistanceParamCommand.setBussId(waybillDetailDTO.getWaybillId());
        transportDistanceParamCommand.setRiskBatchNo(waybillDetailDTO.getWaybillId());
        transportDistanceParamCommand.setBizId(waybillDetailDTO.getWaybillId());
        TransportDistanceCommand transportDistanceCommand = new TransportDistanceCommand();
        TmsWaybillAddressDTO beginAddressDTO = tmsExchangeService.getWaybillAddressDetail(waybillDetailDTO.getBeginAddressId());
        CrmCompanyLineAddressDTO afterLine = crmExchangeService.getCrmCompanyLine(endAddressId);
        CrmGetDisAndTimeQuery query = new CrmGetDisAndTimeQuery();
        query.setStartLngLat(beginAddressDTO.getLng() + "," + beginAddressDTO.getLat());
        query.setEndLngLat(afterLine.getItem1() + "," + afterLine.getItem2());
        CrmDisAndTimeDTO disAndTime = crmExchangeService.getDisAndTime(query);
        transportDistanceCommand.setTransportMileage(new BigDecimal(String.valueOf(ObjUtil.defaultIfNull(disAndTime.getTransportMileage(), 0))));
        transportDistanceCommand.setFreightType(waybillDetailDTO.getFreightType());
        transportDistanceCommand.setNetworkMainBodyId(waybillDetailDTO.getNetworkMainBodyId());
        transportDistanceCommand.setShipperCompanyId(shipperCompanyId);
        transportDistanceCommand.setCarrierCompanyId(waybillDetailDTO.getRealityCompanyId());
        transportDistanceParamCommand.setTransportDistanceVO(transportDistanceCommand);
        return transportDistanceParamCommand;
    }

    /**
     * 查询最新修改记录-根据审核类型
     * @param query
     * @return
     */
    public ResultMode<WaybillChangeRecordDTO> getLatestChangeRecord(ExterLatestChangeRecordQuery query) {
        return tmsExchangeService.getLatestChangeRecord(query);
    }

}
