package com.wanlianyida.user.application.service.bi;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.framework.lgicore.utils.JwtUtil;
import com.wanlianyida.user.application.model.dto.bi.WorkbenchStatisticsDTO;
import com.wanlianyida.user.application.model.query.bi.BiMetadataQuery;
import com.wanlianyida.user.application.model.query.bi.WorkbenchStatisticsQuery;
import com.wanlianyida.user.infrastructure.exception.UserErrorCode;
import com.wanlianyida.user.infrastructure.exception.UserException;
import com.wanlianyida.user.infrastructure.exchange.ItsExchangeService;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年06月16日 16:49
 */
@Service
public class WorkbenchStatisticsAppService {

    @Resource
    private WorkbenchStatisticsHandler handler;
    @Resource
    private ItsExchangeService itsExchangeService;

    public WorkbenchStatisticsDTO statistics(BiMetadataQuery query) {
        WorkbenchStatisticsDTO result = new WorkbenchStatisticsDTO();
        remote(query);
        handler.handler(query, result);
        return result;
    }

    private void remote(BiMetadataQuery query) {
        String companyId = JwtUtil.getTokenInfo().getCompanyId();
        if (StrUtil.isBlank(companyId)) {
            throw new UserException(UserErrorCode.PT_USER_002);
        }
        switch (query.getRoleType()) {
            // 托运商
            case 10:
//                query.setShipperCompanyId(companyId);
                break;
            // 承运商
            case 20:
//                query.setCarrierCompanyId(companyId);
                break;
            // 网货主体
            case 30:
//                query.setNetworkMainBodyId(companyId);
                break;
            default:
                throw new UserException(UserErrorCode.UNKNOWN_USER_TYPE);
        }
    }

    private void remote(BiMetadataQuery query, Map<String, Object> param) {
        String companyId = JwtUtil.getTokenInfo().getCompanyId();
        if (StrUtil.isBlank(companyId)) {
            throw new UserException(UserErrorCode.PT_USER_002);
        }
        if (!validWeekDate(query.getStartTime(), query.getEndTime())) {
            throw new UserException(UserErrorCode.PT_USER_004);
        }
        param.put("startTime", query.getStartTime());
        param.put("endTime", query.getEndTime());
        switch (query.getRoleType()) {
            // 托运商
            case 10:
                param.put("companyId", companyId);
                break;
            // 承运商
            case 20:
                param.put("companyId", companyId);
                break;
            // 网货主体
            case 30:
                param.put("networkMainBodyId", companyId);
                break;
            default:
                throw new UserException(UserErrorCode.UNKNOWN_USER_TYPE);
        }
    }

    private boolean validWeekDate(String startDate, String endDate) {
        if (StrUtil.isBlank(startDate) || StrUtil.isBlank(endDate)) {
            return true;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(DateUtil.parse(endDate, "yyyy-MM-dd HH:mm:ss"));
        calendar.add(Calendar.DAY_OF_YEAR, -31);
        return calendar.getTime().before(DateUtil.parse(startDate, "yyyy-MM-dd HH:mm:ss"));
    }

    public ResultMode<?> statisticsV2(WorkbenchStatisticsQuery query) {
        Map<String, Map<String, Object>> request = new HashMap<>();
        query.getQueryData().forEach((k, v) -> {
            Map<String, Object> param = new HashMap<>();
            remote(v, param);
            request.put(k, param);
        });
        return itsExchangeService.callRequest(request);
    }
}
