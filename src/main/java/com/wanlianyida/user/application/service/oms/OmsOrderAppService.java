package com.wanlianyida.user.application.service.oms;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.basecont.api.model.dto.ContractEsignSignflowDTO;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.command.oms.ExternalSendOrderToTmsCommand;
import com.wanlianyida.user.application.model.dto.cont.CheckContractSignDTO;
import com.wanlianyida.user.application.model.dto.oms.CarrierOrderPageListDTO;
import com.wanlianyida.user.application.model.dto.oms.OmsOrderAddressDTO;
import com.wanlianyida.user.application.model.dto.oms.OrderDetailQueryDTO;
import com.wanlianyida.user.application.model.dto.oms.ShipperOrderPageListDTO;
import com.wanlianyida.user.application.model.query.cont.CheckContractSignQuery;
import com.wanlianyida.user.application.model.query.oms.CarrierOrderPageQuery;
import com.wanlianyida.user.application.model.query.oms.OrderAddressQuery;
import com.wanlianyida.user.application.model.query.oms.OrderDetailQuery;
import com.wanlianyida.user.application.model.query.oms.ShipperOrderPageQuery;
import com.wanlianyida.user.application.service.cont.ContContractAppService;
import com.wanlianyida.user.infrastructure.enums.ContractTypeEnum;
import com.wanlianyida.user.infrastructure.exception.UserErrorCode;
import com.wanlianyida.user.application.assembler.OmsThirdOrderAssemble;
import com.wanlianyida.user.application.assembler.WoaOrderClueAssemble;
import com.wanlianyida.user.application.model.command.oms.OrderPushThirdCommand;
import com.wanlianyida.user.application.model.command.oms.SaveThirdOrderCommand;
import com.wanlianyida.user.application.model.command.woa.GaiaOrderDetailCommand;
import com.wanlianyida.user.application.model.dto.oms.*;
import com.wanlianyida.user.application.model.query.oms.*;
import com.wanlianyida.user.infrastructure.exchange.ContExchangeService;
import com.wanlianyida.user.infrastructure.exchange.OmsExchangeService;
import com.wanlianyida.user.infrastructure.exchange.WoaExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Slf4j
@Service
public class OmsOrderAppService {


    @Resource
    private OmsExchangeService omsExchangeService;

    @Resource
    private ContExchangeService contExchangeService;

    @Resource
    private ContContractAppService contContractAppService;
    @Resource
    private WoaExchangeService woaExchangeService;

    /**
     * 查询订单详情
     * @param query
     * @return
     */
    public ResultMode<OrderDetailQueryDTO> orderDetailQuery(OrderDetailQuery query) {
        return omsExchangeService.orderDetailQuery(query);
    }

    /**
     * 我要发货-订单列表查询
     */
    public ResultMode<ShipperOrderPageListDTO> shipperOrderPageQuery(PagingInfo<ShipperOrderPageQuery> pagingInfo) {
        ResultMode<ShipperOrderPageListDTO> resultMode = omsExchangeService.shipperOrderPageQuery(pagingInfo);
        if (IterUtil.isEmpty(resultMode.getModel())) {
            return resultMode;
        }

        List<ShipperOrderPageListDTO> list = resultMode.getModel();
        List<String> orderIdList = list.stream().map(ShipperOrderPageListDTO::getOrderId).collect(Collectors.toList());
        Map<String, ContractEsignSignflowDTO> contractFlowMap = contExchangeService.queryEsignFlowMapByBizIds(orderIdList);
        if (MapUtil.isNotEmpty(contractFlowMap)) {
            list.stream().forEach(order -> {
                ContractEsignSignflowDTO contractFlow = contractFlowMap.get(order.getOrderId());
                if (contractFlow != null) {
                    //子单 或者 母单已支付
                    if (StrUtil.isNotBlank(order.getSplitType()) || StrUtil.equals(order.getOrderPayStatus(), "2")) {
                        order.setFlowId(contractFlow.getFlowId());
                        order.setSignFlowId(contractFlow.getId());
                        order.setFlowStatus(contractFlow.getFlowStatus());
                    }
                }
            });
        }

        return resultMode;
    }

    /**
     * 我要承运-订单列表查询
     */
    public ResultMode<CarrierOrderPageListDTO> carrierOrderPageQuery(PagingInfo<CarrierOrderPageQuery> pagingInfo) {
        ResultMode<CarrierOrderPageListDTO> resultMode = omsExchangeService.carrierOrderPageQuery(pagingInfo);
        if (IterUtil.isEmpty(resultMode.getModel())) {
            return resultMode;
        }

        List<CarrierOrderPageListDTO> list = resultMode.getModel();
        List<String> orderIdList = list.stream().map(CarrierOrderPageListDTO::getOrderId).collect(Collectors.toList());
        Map<String, ContractEsignSignflowDTO> contractFlowMap = contExchangeService.queryEsignFlowMapByBizIds(orderIdList);
        if (MapUtil.isNotEmpty(contractFlowMap)) {
            list.stream().forEach(order -> {
                ContractEsignSignflowDTO contractFlow = contractFlowMap.get(order.getOrderId());
                if (contractFlow != null) {
                    //子单 或者 母单已支付
                    if (StrUtil.isNotBlank(order.getSplitType()) || StrUtil.equals(order.getOrderPayStatus(), "2")) {
                        order.setFlowId(contractFlow.getFlowId());
                        order.setSignFlowId(contractFlow.getId());
                        order.setFlowStatus(contractFlow.getFlowStatus());
                    }
                }
            });
        }

        return resultMode;
    }

    /**
     * 根据orderId获取订单地址信息
     */
    public ResultMode<OmsOrderAddressDTO> orderAddressQuery(OrderAddressQuery query) {
        OmsOrderAddressDTO dto = omsExchangeService.orderAddressQuery(query.getOrderId());
        if (ObjUtil.isNull(dto)) {
            return ResultMode.success();
        }
        return ResultMode.success(dto);
    }

    /**
     * 发送订单到tms
     */
    public ResultMode<?> sendOrderToTms(ExternalSendOrderToTmsCommand command) {
        OrderDetailQuery orderDetailQuery = new OrderDetailQuery();
        orderDetailQuery.setOrderId(command.getOrderId());
        ResultMode<OrderDetailQueryDTO> queryDTOResultMode = omsExchangeService.orderDetailQuery(orderDetailQuery);
        if (!queryDTOResultMode.isSucceed() || CollectionUtil.isEmpty(queryDTOResultMode.getModel())) {
            return ResultMode.fail(UserErrorCode.TMS_ORDER_NOT_EXIST.getCode(), UserErrorCode.TMS_ORDER_NOT_EXIST.getMsg());
        }
        OrderDetailQueryDTO omsOrder = queryDTOResultMode.getModel().get(0);
        CheckContractSignQuery checkContractSignQuery = new CheckContractSignQuery();
        checkContractSignQuery.setContractType(ContractTypeEnum.DISPATCH.getCode());
        checkContractSignQuery.setContractNature("20");
        checkContractSignQuery.setPartyACompanyId(omsOrder.getShipperCompanyId());
        checkContractSignQuery.setPartyBCompanyId(omsOrder.getRealityCarrierCompanyId());
        checkContractSignQuery.setStartTime(new Date(DateUtil.parse(omsOrder.getReleaseDate(), "YYYY-MM-dd").getTime()));
        checkContractSignQuery.setEndTime(new Date(DateUtil.parse(omsOrder.getArriveDate(),"YYYY-MM-dd").getTime()));
        ResultMode<CheckContractSignDTO> resultMode = contContractAppService.checkContractSign(checkContractSignQuery);
        if (!resultMode.isSucceed()) {
            return resultMode;
        }
        String contractId = "";
        CheckContractSignDTO checkContractSignDTO = IterUtil.getFirst(resultMode.getModel());
        if (ObjUtil.isNotNull(checkContractSignDTO)) {
            contractId = checkContractSignDTO.getContractId();
        }
        ResultMode<String> sendOrderToTmsResultMode = omsExchangeService.sendOrderToTms(command);
        if (sendOrderToTmsResultMode.isSucceed()) {
            //保存合同与订单的关系
            if (StrUtil.isNotBlank(contractId)) {
                contExchangeService.syncLogisticsInfo(contractId, null, command.getOrderId());
            }
        }
        return sendOrderToTmsResultMode;
    }

    /**
     * 订单推送三方
     */
    public ResultMode orderPushThird(OrderPushThirdCommand command) {
        //校验订单
        OrderDetailQuery orderDetailQuery = new OrderDetailQuery();
        orderDetailQuery.setOrderId(command.getOrderId());
        ResultMode<OrderDetailQueryDTO> orderDetailQueryDTOResultMode = omsExchangeService.orderDetailQuery(orderDetailQuery);
        if (!orderDetailQueryDTOResultMode.isSucceed()) {
            return ResultMode.fail(orderDetailQueryDTOResultMode.getErrCode(), orderDetailQueryDTOResultMode.getErrMsg());
        }
        OrderDetailQueryDTO orderDetailQueryDTO = orderDetailQueryDTOResultMode.getModel().get(0);
        if (ObjUtil.isNull(orderDetailQueryDTO)) {
            return ResultMode.fail("订单不存在");
        }
        if (!StrUtil.equals(orderDetailQueryDTO.getOrderStatus(), "200")) {
            return ResultMode.fail("仅待调度订单可以推送三方");
        }
        //woa推送三方数据
        GaiaOrderDetailCommand gaiaOrderDetailCommand = WoaOrderClueAssemble
                .toGaiaOrderDetailCommand(command, orderDetailQueryDTO);
        String thirdOrderId = woaExchangeService.pushOrder(gaiaOrderDetailCommand);
        if (StrUtil.isBlank(thirdOrderId)) {
            return ResultMode.fail("推送三方未返回三方订单号");
        }
        //保存三方订单数据
        SaveThirdOrderCommand saveThirdOrderCommand = OmsThirdOrderAssemble
                .toSaveThirdOrderCommand(command, orderDetailQueryDTO, thirdOrderId);
        omsExchangeService.saveThirdOrder(saveThirdOrderCommand);
        return ResultMode.success();
    }
}
