package com.wanlianyida.user.application.service.tms;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.wanlianyida.bms.api.model.dto.BmsNetworkFreightPaymentDTO;
import com.wanlianyida.bms.api.model.dto.BmsWaybillAccountDTO;
import com.wanlianyida.bms.api.model.dto.BmsWaybillPoolDTO;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.tms.api.model.dto.FreightReminderDTO;
import com.wanlianyida.tms.api.model.dto.FreightReminderTimeDTO;
import com.wanlianyida.tms.api.model.query.FreightReminderQuery;
import com.wanlianyida.tms.api.model.query.FreightReminderTimeQuery;
import com.wanlianyida.user.application.model.command.tms.ExterFreightCommunicateCommand;
import com.wanlianyida.user.infrastructure.exchange.BmsExchangeService;
import com.wanlianyida.user.infrastructure.exchange.FreightReminderExchangeService;
import org.springframework.stereotype.Service;

import java.util.Date;

import javax.annotation.Resource;

@Service
public class FreightReminderAppService {
    @Resource
    private FreightReminderExchangeService freightReminderExchangeService;
    @Resource
    private BmsExchangeService bmsExchangeService;

    /**
     * 查询运费催缴和回复记录
     */
    public ResultMode<FreightReminderDTO> queryFreightReminder(FreightReminderQuery query) {
        return freightReminderExchangeService.queryFreightReminder(query);
    }

    /**
     * 催付回复
     */
    public ResultMode communicateFreight(ExterFreightCommunicateCommand command) {
        return freightReminderExchangeService.communicateFreight(command);
    }

    /**
     * 查询运费催付时间 结算->预审->对账->付款
     */
    public FreightReminderTimeDTO getFreightReminderTime(FreightReminderTimeQuery query) {
        FreightReminderTimeDTO dto = freightReminderExchangeService.getFreightReminderTime(query);
        if (dto == null) {
            return dto;
        }

        Date now = new Date();
        BmsWaybillPoolDTO pool = bmsExchangeService.getBmsWaybillPoolByWaybillId(query.getWaybillId());
        if (pool == null || pool.getCreateDate() == null) {
            return dto;
        }

        //结算持续时间
        if (pool.getPreExamTime() == null) {
            dto.setSettlementTime(DateUtil.between(pool.getCreateDate(), now, DateUnit.HOUR));
            return dto;
        } else {
            dto.setSettlementTime(DateUtil.between(pool.getCreateDate(), pool.getPreExamTime(), DateUnit.HOUR));
        }

        //预审持续时间
        if (pool.getExamTime() == null) {
            dto.setPreReviewTime(DateUtil.between(pool.getPreExamTime(), now, DateUnit.HOUR));
            return dto;
        } else {
            dto.setPreReviewTime(DateUtil.between(pool.getPreExamTime(), pool.getExamTime(), DateUnit.HOUR));
        }

        BmsWaybillAccountDTO account = bmsExchangeService.getBmsWaybillAccountByWaybillId(query.getWaybillId());
        if (account == null || account.getCreateDate() == null) {
            return dto;
        }

        dto.setAccountingTime(DateUtil.between(account.getCreateDate(), now, DateUnit.HOUR));

        BmsNetworkFreightPaymentDTO payment = bmsExchangeService.getBmsNetworkFreightPaymentByWaybillId(query.getWaybillId());

        if (payment == null || payment.getCreateDate() == null) {
            return dto;
        }

        //对账持续时间
        dto.setAccountingTime(DateUtil.between(account.getCreateDate(), payment.getCreateDate(), DateUnit.HOUR));
        //付款持续时间
        if (payment.getPaySuccessTime() == null) {
            dto.setPaymentTime(DateUtil.between(payment.getCreateDate(), now, DateUnit.HOUR));
        } else {
            dto.setPaymentTime(DateUtil.between(payment.getCreateDate(), payment.getPaySuccessTime(), DateUnit.HOUR));
        }

        return dto;
    }
}
