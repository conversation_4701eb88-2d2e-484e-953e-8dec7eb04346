package com.wanlianyida.user.application.service.tms;

import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.tms.api.model.dto.PlateNumberDTO;
import com.wanlianyida.tms.api.model.dto.TmsWaybillDetailDTO;
import com.wanlianyida.user.application.model.dto.tms.WaybillListQueryDTO;
import com.wanlianyida.user.application.model.query.tms.ExternalPlateNumberQuery;
import com.wanlianyida.user.application.model.query.tms.SignWaybillListQuery;
import com.wanlianyida.user.application.model.query.tms.WaybillListQuery;
import com.wanlianyida.user.infrastructure.exchange.TmsExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class TmsWaybillAppService {

    @Resource
    private TmsExchangeService tmsExchangeService;


    /**
     * 查询运单列表
     * @param pageInfo
     * @return
     */
    public ResultMode<WaybillListQueryDTO> waybillListQuery(PagingInfo<WaybillListQuery> pageInfo) {
        return tmsExchangeService.waybillListQuery(pageInfo);
    }

    /**
     * 签收运单列表
     * @param query
     * @return
     */
    public ResultMode<TmsWaybillDetailDTO> signWaybillList(SignWaybillListQuery query) {
        return tmsExchangeService.signWaybillList(query);
    }

    /**
     * 运单车牌号列表
     * @param externalQueryPageInfo
     * @return
     */
    public ResultMode<PlateNumberDTO> waybillPlateNumberPage(PagingInfo<ExternalPlateNumberQuery> externalQueryPageInfo) {
        return tmsExchangeService.waybillPlateNumberPage(externalQueryPageInfo);
    }
}
