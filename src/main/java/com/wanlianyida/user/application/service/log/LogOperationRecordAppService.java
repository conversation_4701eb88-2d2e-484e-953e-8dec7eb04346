package com.wanlianyida.user.application.service.log;

import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.dto.log.OperRecordDTO;
import com.wanlianyida.user.application.model.query.log.OperRecordQuery;
import com.wanlianyida.user.infrastructure.exchange.LogExchangeService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class LogOperationRecordAppService {

    @Resource
    private LogExchangeService logExchangeService;

    /**
     * 查询操作记录
     * @param query
     * @return
     */
    public ResultMode<OperRecordDTO> operRecordQuery(PagingInfo<OperRecordQuery> query) {
        return logExchangeService.operRecordQuery(query);
    }
}
