package com.wanlianyida.user.application.service.crm;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.crm.api.model.dto.CrmCompanyLineAddressDTO;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.framework.lgicommon.entity.TokenInfo;
import com.wanlianyida.framework.lgicore.utils.JwtUtil;
import com.wanlianyida.fssbaselog.api.model.command.LogOperationRecordCommand;
import com.wanlianyida.oms.api.model.dto.LineValidOrderDTO;
import com.wanlianyida.user.application.model.command.crm.AddressAddCommand;
import com.wanlianyida.user.application.model.command.crm.AddressDelAndAddCommand;
import com.wanlianyida.user.application.model.command.crm.AddressDelCommand;
import com.wanlianyida.user.application.model.command.crm.AddressEditCommand;
import com.wanlianyida.user.application.model.dto.crm.*;
import com.wanlianyida.user.application.model.dto.lsds.LineValidGoodsDTO;
import com.wanlianyida.user.application.model.query.crm.AddressCanEditCheckQuery;
import com.wanlianyida.user.application.model.query.crm.AddressListQuery;
import com.wanlianyida.user.application.model.query.crm.AssociationLineQuery;
import com.wanlianyida.user.infrastructure.constant.LogBizTypeConst;
import com.wanlianyida.user.infrastructure.enums.CrmLineChangeTagEnum;
import com.wanlianyida.user.infrastructure.exception.UserErrorCode;
import com.wanlianyida.user.infrastructure.exchange.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Service
@Slf4j
public class CrmAddressAppService {

    @Resource
    private CrmExchangeService crmExchangeService;

    @Resource
    private OmsExchangeService omsExchangeService;

    @Resource
    private LsdsExchangeService lsdsExchangeService;

    @Resource
    private LogExchangeService logExchangeService;

    @Resource
    private TmsExchangeService tmsExchangeService;

    @Resource
    private PlatformExchangeService platformExchangeService;


    /**
     * 地址是否可以编辑校验
     * @param query
     * @return
     */
    public ResultMode addressCanEditCheck(AddressCanEditCheckQuery query) {
        return crmExchangeService.addressCanEditCheck(query);
    }

    /**
     * 常用地址列表
     * @return
     */
    public ResultMode<AddressListDTO> addressListQuery(PagingInfo<AddressListQuery> query) {
        return crmExchangeService.addressListQuery(query);
    }


    /**
     * 常用地址新增
     * @return
     */
    public ResultMode<AddressAddDTO> addressAdd(AddressAddCommand command) {
        ResultMode<AddressAddDTO> resultMode = crmExchangeService.addressAdd(command);
        if(ObjUtil.isNotNull(resultMode) && resultMode.isSucceed()){
            //新增联系人
            platformExchangeService.addLinkman(command);
        }
        return resultMode;
    }


    /**
     * 地址编辑
     * @return
     */
    public ResultMode addressEdit(AddressEditCommand command) {
        command.setCompanyId(JwtUtil.getCompanyIdByToken());
        //参数校验
        ResultMode addressEditCheckResult = this.editCheck(command);
        if(!addressEditCheckResult.isSucceed()){
            return addressEditCheckResult;
        }
        //按照地址id查询线路
        List<LineInfoDTO> lineIds = crmExchangeService.lineListQuery(command.getLineId());
        if(CollUtil.isEmpty(lineIds)){
            log.info("没有对应的线路只变地址:{}",command.getLineId());
            crmExchangeService.addressEdit(command);
        }else {
            //变更地址联系人信息
            ResultMode resultMode = this.changeLineLinkman(command, lineIds);
            if(!resultMode.isSucceed()){
                return resultMode;
            }
        }
        //新增联系人
        platformExchangeService.updateLinkman(command);
        //添加日志
        this.addLog(command);
        return ResultMode.success();
    }

    /**
     * 变更地址联系人信息
     * @param command
     * @param lineIds
     * @return
     */
    private ResultMode changeLineLinkman(AddressEditCommand command, List<LineInfoDTO> lineIds) {
        Map<String,List<String>> lineGoodsMap = new HashMap<>();
        Map<String,List<LineValidOrderDTO>> lineOrderMap = new HashMap<>();
        //修改校验
        ResultMode checkIsCanPssResultMode = this.checkIsCanPss(command,lineIds,lineGoodsMap,lineOrderMap);
        if(!checkIsCanPssResultMode.isSucceed()){
            return checkIsCanPssResultMode;
        }
        crmExchangeService.addressEdit(command);
        //修改数据
        String changeTag = command.getChangeTag();
        if(StrUtil.equals(changeTag, CrmLineChangeTagEnum.CHANGE_TAG_20.getChangeTag())){
            log.info("变更业务数据#lineGoodsMap:{},lineOrderMap:{},lineIds:{},command:{}", JSONUtil.toJsonStr(lineGoodsMap)
                    , JSONUtil.toJsonStr(lineOrderMap), JSONUtil.toJsonStr(lineIds), JSONUtil.toJsonStr(command));
            lsdsExchangeService.lineGoodsAddressUpdate(lineGoodsMap,lineIds,command);
            omsExchangeService.lineOrderAddressUpdate(lineOrderMap,lineIds,command);
            tmsExchangeService.lineOrderAddressUpdate(lineOrderMap,lineIds,command);
        }
        return ResultMode.success();
    }

    /**
     * 参数校验
     * @param command
     * @return
     */
    private ResultMode editCheck(AddressEditCommand command) {
        CrmCompanyLineAddressDTO crmCompanyLine = crmExchangeService.getCrmCompanyLine(command.getLineId());
        if(ObjUtil.isNull(crmCompanyLine)){
            return ResultMode.fail("地址不存在");
        }
        command.setAddressLinkman(crmCompanyLine.getStartSendLinker());
        command.setAddressLinkmanPhone(crmCompanyLine.getStartSendPhoneNumber());
        if(StrUtil.equals(crmCompanyLine.getStartSendLinker(), command.getStartSendLinker()) &&
                StrUtil.equals(crmCompanyLine.getStartSendPhoneNumber(), command.getStartSendPhoneNumber()) &&
                StrUtil.equals(crmCompanyLine.getChangeTag(), command.getChangeTag()) ){
            log.info("修改前修改后一样不更新数据");
            return ResultMode.success();
        }
        return ResultMode.success();
    }

    /**
     * 添加日志
     * @param command
     */
    private void addLog(AddressEditCommand command) {
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        LogOperationRecordCommand addLog = new LogOperationRecordCommand();
        addLog.setBizId(command.getLineId());
        addLog.setBizType(LogBizTypeConst.LGI_USET_CHANEG_LINE);
        addLog.setOperateTime(new Date());
        addLog.setUserAccount(tokenInfo.getLoginName());
        addLog.setUserName(tokenInfo.getUsername());
        addLog.setUserBaseId(tokenInfo.getUserBaseId());
        addLog.setOperateType("修改");

        String prePhone = command.getAddressLinkmanPhone();
        String preLinker = command.getAddressLinkman();
        String tailMsg = StrUtil.equals(command.getChangeTag(),CrmLineChangeTagEnum.CHANGE_TAG_20.getChangeTag()) ? "向货源订单同步联系信息": "";
        addLog.setOperateContent(String.format(LogBizTypeConst.LGI_USER_CHANEG_LINE_MSG,preLinker,command.getStartSendLinker(),prePhone,command.getStartSendPhoneNumber(),tailMsg));
        logExchangeService.add(addLog);
    }

    /**
     * 校验是否能修改地址信息
     * @param command
     * @param lineIds
     * @return
     */
    private ResultMode checkIsCanPss(AddressEditCommand command,List<LineInfoDTO> lineIds,Map<String,List<String>> lineGoodsMap,Map<String,List<LineValidOrderDTO>> lineOrderMap) {
        List<String> lineShortNames = new ArrayList<>();
        List<String> lineIdList = lineIds.stream().map(l -> l.getLineId()).collect(Collectors.toList());
        //查询线路对应的有效货源
        List<LineValidGoodsDTO> lineValidGoodsDTOS = lsdsExchangeService.lineValidGoodsQuery(lineIdList);
        if(CollUtil.isNotEmpty(lineValidGoodsDTOS)){
            Map<String,List<String>> lineGoods = lineValidGoodsDTOS.stream().collect(Collectors.groupingBy(LineValidGoodsDTO::getLineId, Collectors.mapping(LineValidGoodsDTO::getGoodsId, Collectors.toList())));
            lineGoodsMap.putAll(lineGoods);
            List<LineValidGoodsDTO> sendNotPssGoods = lineValidGoodsDTOS.stream().filter(lg -> StrUtil.equals(command.getStartSendPhoneNumber(), lg.getStartSendPhoneNumber())).collect(Collectors.toList());
            for (LineValidGoodsDTO notPssGood : sendNotPssGoods) {
                lineShortNames.add(notPssGood.getSendAddrShortName());
            }
            List<LineValidGoodsDTO> endNotPssGoods = lineValidGoodsDTOS.stream().filter(lg -> StrUtil.equals(command.getStartSendPhoneNumber(), lg.getEndReceivePhoneNumber())).collect(Collectors.toList());
            for (LineValidGoodsDTO notPssGood : endNotPssGoods) {
                lineShortNames.add(notPssGood.getReceiveAddrShortName());
            }
        }
        List<LineValidOrderDTO> lineValidOrderDTOS = omsExchangeService.lineValidOrderQuery(lineIdList);
        if(CollUtil.isNotEmpty(lineValidOrderDTOS)){
            List<LineValidOrderDTO> NotDfqOrderIdList = lineValidOrderDTOS.stream().filter(l -> !StrUtil.startWith(l.getGoodsId(), "DFQ")).collect(Collectors.toList());
            Map<String,List<LineValidOrderDTO>> lineOrder= NotDfqOrderIdList.stream().collect(Collectors.groupingBy(LineValidOrderDTO::getLineId));
            lineOrderMap.putAll(lineOrder);

            List<LineValidOrderDTO> sendNotPssOrders = NotDfqOrderIdList.stream().filter(lg -> StrUtil.equals(command.getStartSendPhoneNumber(), lg.getStartSendPhoneNumber())).collect(Collectors.toList());
            for (LineValidOrderDTO notPssOrder : sendNotPssOrders) {
                lineShortNames.add(notPssOrder.getSendAddrShorthand());
            }

            List<LineValidOrderDTO> endNotPssOrders = NotDfqOrderIdList.stream().filter(lg ->  StrUtil.equals(command.getStartSendPhoneNumber(), lg.getEndReceivePhoneNumber())).collect(Collectors.toList());
            for (LineValidOrderDTO notPssOrder : endNotPssOrders) {
                lineShortNames.add(notPssOrder.getReceiveAddrShorthand());
            }
        }
        if(CollUtil.isNotEmpty(lineShortNames)){
            List<String> collect = lineShortNames.stream().distinct().collect(Collectors.toList());
            ResultMode fail = ResultMode.fail(UserErrorCode.PT_USER_003.getCode(),UserErrorCode.PT_USER_003.getMsg());
            fail.setModel(collect);
            return fail;
        }
        return ResultMode.success();
    }

    /**
     * 地址删除
     * @return
     */
    public ResultMode addressDel(AddressDelCommand command) {
        return crmExchangeService.addressDel(command);
    }

    /**
     * 地址删除并且新增
     * @return
     */
    public ResultMode<AddressAddDTO> addressDelAndAdd(AddressDelAndAddCommand command) {
        ResultMode<AddressAddDTO> resultMode = crmExchangeService.addressDelAndAdd(command);
        if(ObjUtil.isNotNull(resultMode) && resultMode.isSucceed()){
            //新增联系人
            AddressAddCommand addressAddCommand = new  AddressAddCommand();
            addressAddCommand.setStartSendPhoneNumber(command.getStartSendPhoneNumber());
            addressAddCommand.setStartSendLinker(command.getStartSendLinker());
            platformExchangeService.addLinkman(addressAddCommand);
        }
        return resultMode;
    }

    /**
     * 查看是否已经创建了线路
     * @return
     */
    public ResultMode<AssociationLineDTO> queryAssociationLine(AssociationLineQuery query) {
        return crmExchangeService.queryAssociationLine(query);
    }

    /**
     * 地址删除并且新增前检查
     * @return
     */
    public ResultMode<AddressDelAndAddCheckDTO> addressDelAndAddCheck(AddressDelCommand command) {
        return crmExchangeService.addressDelAndAddCheck(command);
    }
}
