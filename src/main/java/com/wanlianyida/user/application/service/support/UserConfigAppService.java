package com.wanlianyida.user.application.service.support;

import cn.hutool.core.bean.BeanUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.framework.lgicore.utils.JwtUtil;
import com.wanlianyida.support.api.model.query.UserConfigQuery;
import com.wanlianyida.user.application.model.command.support.WorkbenchDateScopeCommand;
import com.wanlianyida.user.application.model.dto.support.UserConfigDTO;
import com.wanlianyida.user.infrastructure.exchange.SupportExchangeService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年07月25日 13:08
 */
@Service
public class UserConfigAppService {

    @Resource
    private SupportExchangeService supportExchangeService;

    public ResultMode<?> saveWorkbenchDateScope(WorkbenchDateScopeCommand command) {
        com.wanlianyida.support.api.model.command.WorkbenchDateScopeCommand param = BeanUtil.toBean(command, com.wanlianyida.support.api.model.command.WorkbenchDateScopeCommand.class);
        param.setUserBaseId(JwtUtil.getTokenInfo().getUserBaseId());
        return supportExchangeService.saveWorkbenchDateScope(param);
    }

    public ResultMode<UserConfigDTO> getWorkbenchDateScope() {
        UserConfigQuery query = new UserConfigQuery();
        query.setType("30");
        query.setUserBaseId(JwtUtil.getTokenInfo().getUserBaseId());
        return supportExchangeService.getWorkbenchDateScope(query);
    }
}
