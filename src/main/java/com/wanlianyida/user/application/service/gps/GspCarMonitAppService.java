package com.wanlianyida.user.application.service.gps;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.framework.lgicore.utils.JwtUtil;
import com.wanlianyida.gps.api.model.query.GpsDeviceOwnerQuery;
import com.wanlianyida.mms.api.model.query.MmsCouponRuleQuery;
import com.wanlianyida.user.application.model.query.gps.GpsCarMonitViewQuery;
import com.wanlianyida.user.infrastructure.exchange.GpsExchangeService;
import com.wanlianyida.user.infrastructure.exchange.MmsExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import static com.wanlianyida.user.infrastructure.constant.UserConst.COUPON_MESSAGE;

@Slf4j
@Service
public class GspCarMonitAppService {

    @Resource
    private GpsExchangeService gpsExchangeService;

    @Resource
    private MmsExchangeService mmsExchangeService;

    /**
     * 车辆监控查看
     * @param query
     * @return
     */
    public ResultMode carMonitView(GpsCarMonitViewQuery query) {
        GpsDeviceOwnerQuery gpsDeviceOwnerQuery = new GpsDeviceOwnerQuery();
        gpsDeviceOwnerQuery.setDeviceNo(query.getDeviceNo());
        gpsDeviceOwnerQuery.setLicensePlateNumber(query.getLicensePlateNumber());
        String deviceOwner = gpsExchangeService.getDeviceOwner(gpsDeviceOwnerQuery);
        if(StrUtil.isBlank(deviceOwner) || !StrUtil.equals(deviceOwner, JwtUtil.getCompanyIdByToken())){
            MmsCouponRuleQuery mmsCouponRuleQuery = BeanUtil.toBean(query, MmsCouponRuleQuery.class);
            return mmsExchangeService.ruleInformation(mmsCouponRuleQuery);
        }
        //不需要优惠券
        ResultMode notNeedCoupon = ResultMode.fail("0",COUPON_MESSAGE);
        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("need", "0");
        notNeedCoupon.getModel().add(resultMap);
        return notNeedCoupon;
    }
}
