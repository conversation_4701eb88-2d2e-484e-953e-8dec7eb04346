package com.wanlianyida.user.application.service.platform;

import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.user.application.model.dto.platform.ProtocolBatchDTO;
import com.wanlianyida.user.application.model.query.platform.ProtocolBatchQuery;
import com.wanlianyida.user.infrastructure.exchange.PlatformExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

@Slf4j
@Service
public class PlatformProtocolAppService {

    @Resource
    private PlatformExchangeService platformExchangeService;

    /**
     * 批量查询
     * @param pageInfo
     * @return
     */
    public List<ProtocolBatchDTO> batchQuery(PagingInfo<ProtocolBatchQuery> pageInfo) {
        return platformExchangeService.batchQuery(pageInfo);
    }
}
