package com.wanlianyida.user.application.service.platform;

import cn.hutool.core.bean.BeanUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.platform.api.model.query.ParaValueByOperIdAndParaKeyQuery;
import com.wanlianyida.user.application.model.query.platform.ExterParaValueByOperIdAndParaKeyQuery;
import com.wanlianyida.user.infrastructure.exchange.PlatformExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class PlatformCmParameterAppService {


    @Resource
    private PlatformExchangeService platformExchangeService;

    /**
     * 根据操作主体id和参数字典key值查询参数值
     * @param query
     * @return
     */
    public ResultMode<String> getParaValueByOperIdAndParaKey(ExterParaValueByOperIdAndParaKeyQuery query) {
        String paraValue = platformExchangeService.getParaValueByOperIdAndParaKey(BeanUtil.toBean(query, ParaValueByOperIdAndParaKeyQuery.class));
        ResultMode<String> resultMode = new ResultMode<>();
        resultMode.getModel().add(paraValue);
        return resultMode;
    }

}
