package com.wanlianyida.user.application.service.tms;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.platform.api.model.dto.PlatformUmDriverReceiptInfoDTO;
import com.wanlianyida.tcs.api.model.dto.TcsCarLicenseVO;
import com.wanlianyida.tcs.api.model.query.TcsCarPlateNoAndColorFilter;
import com.wanlianyida.tms.api.model.command.TransportCarCommand;
import com.wanlianyida.tms.api.model.dto.PlatformUmTrailerCarDTO;
import com.wanlianyida.tms.api.model.dto.TmsRecommendedCapacityDTO;
import com.wanlianyida.tms.api.model.dto.TransportCarDTO;
import com.wanlianyida.tms.api.model.query.CarTransportCapacityQuery;
import com.wanlianyida.tms.api.model.query.TmsRecommendedCapacityQuery;
import com.wanlianyida.user.infrastructure.enums.LgiOrchUserEnum;
import com.wanlianyida.user.infrastructure.exchange.EvalExchangeService;
import com.wanlianyida.user.infrastructure.exchange.PlatformExchangeService;
import com.wanlianyida.user.infrastructure.exchange.TcsExchangeService;
import com.wanlianyida.user.infrastructure.exchange.TmsExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 运力查询
 */
@Slf4j
@Service
public class TransportCapacityQueryAppService {

    @Resource
    private TmsExchangeService tmsExchangeService;

    @Resource
    private TcsExchangeService tcsExchangeService;

    @Resource
    private EvalExchangeService evalExchangeService;

    @Resource
    private PlatformExchangeService platformExchangeService;

    /**
     * 运力推荐查询（派车、服务单派车）
     */
    public ResultMode<TmsRecommendedCapacityDTO> recommendPage(PagingInfo<TmsRecommendedCapacityQuery> pagingInfo) {
        //运力推荐数据
        ResultMode<TmsRecommendedCapacityDTO> resultMode = tmsExchangeService.tranCapacityRecommendDataPage(pagingInfo);
        if (!resultMode.isSucceed() || IterUtil.isEmpty(resultMode.getModel())) {
            return resultMode;
        }

        List<TmsRecommendedCapacityDTO> list = resultMode.getModel();

        //获取司机的注册状态 11-线上 、21-线下, 12-网络
        List<String> driverIdList = list.stream().filter(l -> StrUtil.isNotBlank(l.getDriverId())).map(TmsRecommendedCapacityDTO::getDriverId).collect(Collectors.toList());
        Map<String, String> driverMap = tcsExchangeService.getDriversLoginStatus(driverIdList);

        //获取车辆信息
        Map<String, TcsCarLicenseVO> carMap = getCarInfo(list);

        //列表数据补充
        list.stream().forEach(lt -> {
            //车辆信息
            if (carMap != null) {
                String carKey = lt.getCarPlateNo() + "_" + lt.getCarColor();
                if (StrUtil.isAllNotBlank(lt.getCarPlateNo(), lt.getCarColor()) && carMap.get(carKey) != null) {
                    TcsCarLicenseVO carInfo = carMap.get(carKey);
                    //车辆类型
                    lt.setCarType(carInfo.getCarType());
                    //是否入网
                    lt.setAccessNetworkStatus("1");
                }
                //挂车信息
                carKey = lt.getDefaultTrailer() + "_" + lt.getDefaultTrailerColor();
                if (StrUtil.isAllNotBlank(lt.getDefaultTrailer(), lt.getDefaultTrailerColor())
                        && lt.getDefaultTrailer().contains("挂") && carMap.get(carKey) != null) {
                    TcsCarLicenseVO trailerInfo = carMap.get(carKey);
                    //挂车类型
                    lt.setTrailerCarType(trailerInfo.getCarType());
                    lt.setPlatformUmTrailerCar(new PlatformUmTrailerCarDTO()
                            .setCarColor(Integer.valueOf(lt.getDefaultTrailerColor()))
                            .setCarPlateNo(lt.getDefaultTrailer()));
                }
            }

            //司机的注册状态
            if (driverMap != null && StrUtil.isNotBlank(lt.getDriverId()) && StrUtil.isNotBlank(driverMap.get(lt.getDriverId()))) {
                lt.setLoginStatus(driverMap.get(lt.getDriverId()));
            } else {
                lt.setLoginStatus(LgiOrchUserEnum.DriverLoginStatusEnum.ONLINE.getCode());
            }
        });

        return resultMode;
    }

    /**
     * 获取车辆信息
     */
    private Map<String, TcsCarLicenseVO> getCarInfo(List<TmsRecommendedCapacityDTO> dtoList) {
        if (IterUtil.isEmpty(dtoList)) {
            return null;
        }
        List<TcsCarPlateNoAndColorFilter> carPlateNoAndColorBOList = new ArrayList<>();
        for (TmsRecommendedCapacityDTO dto : dtoList) {
            if (StrUtil.isAllNotBlank(dto.getCarPlateNo(), dto.getCarColor())) {
                TcsCarPlateNoAndColorFilter filter = new TcsCarPlateNoAndColorFilter();
                filter.setPlateNo(dto.getCarPlateNo());
                filter.setPlateColor(Integer.valueOf(dto.getCarColor()));
                carPlateNoAndColorBOList.add(filter);
            }
            if (StrUtil.isAllNotBlank(dto.getDefaultTrailer(), dto.getDefaultTrailerColor())) {
                TcsCarPlateNoAndColorFilter filter = new TcsCarPlateNoAndColorFilter();
                filter.setPlateNo(dto.getDefaultTrailer());
                filter.setPlateColor(Integer.valueOf(dto.getDefaultTrailerColor()));
                carPlateNoAndColorBOList.add(filter);
            }
        }

        if (IterUtil.isEmpty(carPlateNoAndColorBOList)) {
            return null;
        }
        carPlateNoAndColorBOList = carPlateNoAndColorBOList.stream().distinct().collect(Collectors.toList());
        List<TcsCarLicenseVO> carList = tcsExchangeService.queryCarLicenseListByFilter(carPlateNoAndColorBOList);
        if (IterUtil.isEmpty(carList)) {
            return null;
        }

        return carList.stream().collect(Collectors.toMap(v -> (v.getPlateNo() + "_" + v.getPlateColor()), Function.identity()));
    }

    /**
     * 自有运力和平台运力车辆查询（派车、服务单派车）
     */
    public ResultMode<TransportCarDTO> carQueryPage(PagingInfo<CarTransportCapacityQuery> pagingInfo) {
        // tcs车辆数据查询
        ResultMode<TransportCarDTO> resultMode = tcsExchangeService.queryCompanyCarCapacity(pagingInfo);
        if (resultMode == null) {
            return ResultMode.success();
        }
        List<TransportCarDTO> list = resultMode.getModel();
        // 车辆是否入网isNetWork全部置为1-入网
        list.forEach(car -> car.setIsNetWork("1"));

        // 查非平台运力且是11-线上的再批量查platform司机收款信息：有的就置为网络（loginStatus：11-线上 21-线下 12-网络）
        List<String> userBaseIdList = list.stream().filter(item -> StrUtil.equals(item.getLoginStatus(), LgiOrchUserEnum.DriverLoginStatusEnum.ONLINE.getCode())).map(TransportCarDTO::getUserBaseId).collect(Collectors.toList());
        List<PlatformUmDriverReceiptInfoDTO> driverReceiptInfoList = platformExchangeService.getDriverReceiptInfoByUserBaseIdList(userBaseIdList);
        Map<String, PlatformUmDriverReceiptInfoDTO> driverReceiptInfoMap = driverReceiptInfoList.stream().collect(Collectors.toMap(PlatformUmDriverReceiptInfoDTO::getUserBaseId, Function.identity(), (v1, v2) -> v1));
        list.forEach(car -> {
            PlatformUmDriverReceiptInfoDTO driverReceiptInfo = driverReceiptInfoMap.get(car.getUserBaseId());
            if (ObjUtil.isNotNull(driverReceiptInfo)) {
                car.setLoginStatus(LgiOrchUserEnum.DriverLoginStatusEnum.NET.getCode());
            }
        });
        resultMode.setModel(list);
        //处理运力车辆数据
        List<TransportCarCommand> commands = BeanUtil.copyToList(list, TransportCarCommand.class);
        List<TransportCarDTO> transportCarDTOList = tmsExchangeService.handleCarTransport(commands);
        if (IterUtil.isEmpty(transportCarDTOList)) {
            return resultMode;
        }

        //处理司机评级
        List<String> driverIds = transportCarDTOList.stream().filter(car -> StrUtil.isNotBlank(car.getDriverId())).map(TransportCarDTO::getDriverId).collect(Collectors.toList());
        Map<String, Double> evalDriverMap = evalExchangeService.getEvalDriver(driverIds);
        if (MapUtil.isNotEmpty(evalDriverMap)) {
            transportCarDTOList.stream().filter(car -> StrUtil.isNotBlank(car.getDriverId())).forEach(car -> {
                if (ObjUtil.isNotNull(evalDriverMap.get(car.getDriverId()))) {
                    car.setDriverRating(evalDriverMap.get(car.getDriverId()));
                }
            });
        }
        resultMode.setModel(transportCarDTOList);
        return resultMode;
    }

}
