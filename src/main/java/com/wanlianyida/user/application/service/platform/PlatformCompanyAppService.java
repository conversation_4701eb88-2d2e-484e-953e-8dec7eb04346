package com.wanlianyida.user.application.service.platform;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.framework.lgicommon.entity.TokenInfo;
import com.wanlianyida.framework.lgicore.utils.JwtUtil;
import com.wanlianyida.platform.api.inter.PlatformCompanyConfigInter;
import com.wanlianyida.platform.api.inter.PlatformUmCompanyInter;
import com.wanlianyida.platform.api.model.dto.PlatformCompanyConfigDTO;
import com.wanlianyida.platform.api.model.dto.PlatformUmCompanyDTO;
import com.wanlianyida.platform.api.model.query.BatchNewConfigQuery;
import com.wanlianyida.platform.api.model.query.PlatformUmCompanyQuery;
import com.wanlianyida.user.application.model.command.platform.CompanyLinkmanInfoCommand;
import com.wanlianyida.user.application.model.dto.platform.BatchConfigDTO;
import com.wanlianyida.user.application.model.dto.platform.CompanyInfoDTO;
import com.wanlianyida.user.application.model.dto.platform.CompanyLinkmanInfoDTO;
import com.wanlianyida.user.application.model.dto.platform.PlatformMainBodyListDTO;
import com.wanlianyida.user.application.model.query.platform.BatchConfigQuery;
import com.wanlianyida.user.application.model.query.platform.CompanyInfoQuery;
import com.wanlianyida.user.application.model.query.platform.CompanyLinkmanInfoQuery;
import com.wanlianyida.user.application.model.query.platform.PlatformMainBodyListQuery;
import com.wanlianyida.user.infrastructure.exchange.PlatformExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

@Service
@Slf4j
public class PlatformCompanyAppService {


    @Resource
    private PlatformCompanyConfigInter platformCompanyConfigInter;

    @Resource
    private PlatformUmCompanyInter platformUmCompanyInter;

    @Resource
    private PlatformExchangeService platformExchangeService;

    /**
     * 企业详情
     * @param query
     * @return
     */
    public CompanyInfoDTO infoQuery(CompanyInfoQuery query) {
        log.info("infoQuery#查询企业信息参数:{}", JSONUtil.toJsonStr(query));

        PlatformUmCompanyQuery platformUmCompanyQuery = BeanUtil.toBean(query, PlatformUmCompanyQuery.class);
        ResultMode<PlatformUmCompanyDTO> resultMode = platformUmCompanyInter.getCompanyByModel(platformUmCompanyQuery);
        if(ObjUtil.isNull(resultMode) || !resultMode.isSucceed() || CollUtil.isEmpty(resultMode.getModel())){
            log.info("infoQuery#查询企业返回:{}", JSONUtil.toJsonStr(resultMode));
            return null;
        }
        return BeanUtil.toBean(resultMode.getModel().get(0),CompanyInfoDTO.class);
    }

    /**
     * 批量查询配置信息
     * @param query
     * @return
     */
    public Map<String, List<BatchConfigDTO>> batchConfigQuery(BatchConfigQuery query) {
        log.info("batchConfigQuery#查询企业配置参数:{}", JSONUtil.toJsonStr(query));

        BatchNewConfigQuery configQuery = BeanUtil.toBean(query, BatchNewConfigQuery.class);
        ResultMode<Map<String, List<PlatformCompanyConfigDTO>>> resultMode = platformCompanyConfigInter.batchSelectNewConfig(configQuery);
        if(ObjUtil.isNull(resultMode) || !resultMode.isSucceed() || CollUtil.isEmpty(resultMode.getModel())){
            log.info("batchConfigQuery#查询企业配置:{}", JSONUtil.toJsonStr(resultMode));
            return null;
        }
        return BeanUtil.toBean(resultMode.getModel().get(0), Map.class);
    }

    /**
     * 查询企业联系人
     * @param query
     * @return
     */
    public ResultMode<CompanyLinkmanInfoDTO> companyLinkmanListQuery(CompanyLinkmanInfoQuery query) {
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        query.setCompanyId(tokenInfo.getCompanyId());
        return platformExchangeService.companyLinkmanListQuery(query);
    }

    /**
     * 新增企业联系人
     * @param command
     * @return
     */
    public ResultMode companyLinkmanAdd(CompanyLinkmanInfoCommand command) {
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        command.setCompanyId(tokenInfo.getCompanyId());
        command.setCreatorId(tokenInfo.getUserBaseId());
        return platformExchangeService.companyLinkmanAdd(command);
    }

    /**
     * 查询固定类型的主体
     * @param query
     * @return
     */
    public ResultMode<PlatformMainBodyListDTO> mainBodyListQuery(PlatformMainBodyListQuery query) {
        return platformExchangeService.mainBodyListQuery(query);
    }
}
