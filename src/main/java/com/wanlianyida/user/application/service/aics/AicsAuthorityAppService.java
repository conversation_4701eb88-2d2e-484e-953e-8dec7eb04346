package com.wanlianyida.user.application.service.aics;

import com.alibaba.fastjson.JSONObject;
import com.wanlianyida.baseaiadv.api.inter.AicsAuthorityInter;
import com.wanlianyida.baseaiadv.api.model.command.LoginCommand;
import com.wanlianyida.baseaiadv.api.model.dto.ValidDTO;
import com.wanlianyida.framework.lgicommon.entity.TokenInfo;
import com.wanlianyida.framework.lgicore.config.JwtConfig;
import com.wanlianyida.user.application.model.dto.aics.AicsLoginDTO;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.springframework.stereotype.Service;

import java.util.Date;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025年05月15日 10:04
 */
@Service
public class AicsAuthorityAppService {

    @Resource
    private JwtConfig jwtConfig;
    @Resource
    private AicsAuthorityInter authorityInter;

    private final long expiration = 10080;

    /**
     * 模拟登录
     */
    public AicsLoginDTO login(LoginCommand command) {
        TokenInfo tokenInfo = new TokenInfo();
        tokenInfo.setUserBaseId(command.getUserId());
        String token = Jwts.builder().setHeaderParam("typ", "JWT").setSubject(JSONObject.toJSONString(tokenInfo)).setIssuedAt(new Date()).setExpiration(new Date(System.currentTimeMillis() + 60000L * expiration)).signWith(SignatureAlgorithm.HS256, jwtConfig.getSecret()).compact();
        return new AicsLoginDTO(token);
    }

    /**
     * 白名单校验
     */
    public ValidDTO experienceValid(LoginCommand command) {
        return authorityInter.experienceValid(command).getModel();
    }
}
