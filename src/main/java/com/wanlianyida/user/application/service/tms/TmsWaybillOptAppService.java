package com.wanlianyida.user.application.service.tms;

import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.tms.api.model.command.TmsWaybillUpdCommand;
import com.wanlianyida.user.infrastructure.exchange.TmsExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 运力查询
 */
@Slf4j
@Service
public class TmsWaybillOptAppService {

    @Resource
    private TmsExchangeService tmsExchangeService;

    /**
     * 更新运单信息
     */
    public ResultMode updateWaybillInfo(TmsWaybillUpdCommand command) {
        return tmsExchangeService.updateWaybillInfo(command);
    }
}
