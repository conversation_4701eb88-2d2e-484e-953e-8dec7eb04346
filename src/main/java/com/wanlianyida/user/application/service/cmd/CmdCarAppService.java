package com.wanlianyida.user.application.service.cmd;

import com.wanlianyida.cmd.api.model.dto.CmdCarDTO;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.query.cmd.ExterCmdCarQuery;
import com.wanlianyida.user.infrastructure.exchange.CmdExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class CmdCarAppService {

    @Resource
    private CmdExchangeService cmdExchangeService;

    public ResultMode<CmdCarDTO> cmdCarPage(PagingInfo<ExterCmdCarQuery> pagingInfo) {
        return cmdExchangeService.cmdCarPage(pagingInfo);
    }
}
