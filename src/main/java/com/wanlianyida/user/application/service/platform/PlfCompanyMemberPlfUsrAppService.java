package com.wanlianyida.user.application.service.platform;

import cn.hutool.core.util.ObjUtil;
import com.wanlianyida.basemdm.api.model.dto.MdmUserInfoDTO;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.platform.api.model.dto.CompanyMemberDTO;
import com.wanlianyida.platform.api.model.dto.FindComMemberDTO;
import com.wanlianyida.platform.api.model.dto.FindPlfUsrByUserBaseIdDTO;
import com.wanlianyida.platform.api.model.dto.PlfUsrCompanyMemberDTO;
import com.wanlianyida.user.application.model.command.platform.ExternalPlfUsrAgreeOrRefuseBindCommand;
import com.wanlianyida.user.application.model.command.platform.ExternalPlfUsrBindCommand;
import com.wanlianyida.user.application.model.command.platform.ExternalPlfUsrUnBindCommand;
import com.wanlianyida.user.application.model.dto.platform.PlfUsrByMobileDTO;
import com.wanlianyida.user.application.model.query.platform.ExternalCompanyMemberQuery;
import com.wanlianyida.user.application.model.query.platform.ExternalFindPlfUsrByMobileQuery;
import com.wanlianyida.user.application.model.query.platform.ExternalPlfUsrCompanyMemberQuery;
import com.wanlianyida.user.infrastructure.constant.PlfCompanyMemberPlfUsrRelConstants;
import com.wanlianyida.user.infrastructure.exception.UserErrorCode;
import com.wanlianyida.user.infrastructure.exchange.MdmExchangeService;
import com.wanlianyida.user.infrastructure.exchange.PlatformExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class PlfCompanyMemberPlfUsrAppService {

    @Resource
    private PlatformExchangeService platformExchangeService;

    @Resource
    private MdmExchangeService mdmExchangeService;


    /**
     * 获取企业员工列表
     */
    public ResultMode<CompanyMemberDTO> companyMemberPage(PagingInfo<ExternalCompanyMemberQuery> pageInfo) {
        return platformExchangeService.companyMemberPage(pageInfo);
    }

    /**
     * 获取平台个人关联的企业员工列表
     */
    public ResultMode<PlfUsrCompanyMemberDTO> plfUsrCompanyMemberPage(PagingInfo<ExternalPlfUsrCompanyMemberQuery> pageInfo) {
        return platformExchangeService.plfUsrCompanyMemberPage(pageInfo);
    }

    /**
     * 获取当前企业的员工绑定的个人账户信息
     */
    public ResultMode<FindPlfUsrByUserBaseIdDTO> findPlfUsrByUserBaseId() {
        return platformExchangeService.findPlfUsrByUserBaseId();
    }

    /**
     * 通过手机号获取中台个人账户信息
     * 入参手机号
     */
    public ResultMode<PlfUsrByMobileDTO> findPlfUsrByMobile(ExternalFindPlfUsrByMobileQuery query) {
        MdmUserInfoDTO mdmUserInfoDTO = mdmExchangeService.findPlfUsrByMobile(query.getMobile());
        if(ObjUtil.isNull(mdmUserInfoDTO)){
            return ResultMode.fail(UserErrorCode.PLF_USER_NOT_EXIST.getCode(),UserErrorCode.PLF_USER_NOT_EXIST.getMsg());
        }
        PlfUsrByMobileDTO plfUsrByMobileDTO = new PlfUsrByMobileDTO();
        plfUsrByMobileDTO.setPlfUserId(mdmUserInfoDTO.getId().toString());
        plfUsrByMobileDTO.setPlfUserLoginId(mdmUserInfoDTO.getLoginId());
        plfUsrByMobileDTO.setPlfUserName(mdmUserInfoDTO.getUserName());
        plfUsrByMobileDTO.setPlfUserAccountName(mdmUserInfoDTO.getLoginName());
        plfUsrByMobileDTO.setPlfUserIdCardNo(mdmUserInfoDTO.getIdCardNo());
        plfUsrByMobileDTO.setPlfUserMobile(query.getMobile());
        FindComMemberDTO findComMemberDTO = platformExchangeService.findComMemberByPlfUserCompanyId(mdmUserInfoDTO.getId().toString());
        if(ObjUtil.isNotNull(findComMemberDTO) && PlfCompanyMemberPlfUsrRelConstants.isOperableBindStatus(findComMemberDTO.getBindStatus())){
            plfUsrByMobileDTO.setEmployeeUserName(findComMemberDTO.getEmployeeUserName());
            plfUsrByMobileDTO.setEmployeeAccountName(findComMemberDTO.getEmployeeAccountName());
            plfUsrByMobileDTO.setEmployeeMobile(findComMemberDTO.getEmployeeMobile());
        }
        return ResultMode.success(plfUsrByMobileDTO);
    }

    /**
     * 绑定接口
     */
    public ResultMode<?> bind(ExternalPlfUsrBindCommand command) {
        return platformExchangeService.bind(command);
    }

    /**
     * 解绑接口
     */
    public ResultMode<?> unbind(ExternalPlfUsrUnBindCommand command) {
        return platformExchangeService.unbind(command);
    }

    /**
     * 同意绑定
     */
    public ResultMode<?> agreeOrRefuseBind(ExternalPlfUsrAgreeOrRefuseBindCommand command) {
        return platformExchangeService.agreeOrRefuseBind(command);
    }

}
