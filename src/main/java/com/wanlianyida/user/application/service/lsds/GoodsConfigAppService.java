package com.wanlianyida.user.application.service.lsds;

import com.wanlianyida.user.application.model.query.lsds.RoundingModeQuery;
import com.wanlianyida.user.infrastructure.exchange.LsdsExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

import javax.annotation.Resource;

@Slf4j
@Service
public class GoodsConfigAppService {

    @Resource
    private LsdsExchangeService lsdsExchangeService;

    public Map<String, Integer> roundingModeQuery(RoundingModeQuery query) {
        return lsdsExchangeService.roundingModeQuery(query);
    }
}
