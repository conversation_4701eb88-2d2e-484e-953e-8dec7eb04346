package com.wanlianyida.user.application.service.support;

import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.infrastructure.exchange.SupportExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class HolidaySchedulingApplicationService {

    @Resource
    private SupportExchangeService supportExchangeService;

    public ResultMode getScheduling() {
        return supportExchangeService.getScheduling();
    }
}
