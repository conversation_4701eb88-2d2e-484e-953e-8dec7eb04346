package com.wanlianyida.user.application.service.bi;

import com.wanlianyida.user.application.model.dto.bi.WorkbenchStatisticsDTO;
import com.wanlianyida.user.application.model.query.bi.BiMetadataQuery;
import com.wanlianyida.user.infrastructure.config.BiWorkbenchUrlConfig;
import com.wanlianyida.user.infrastructure.exchange.BiExchangeService;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年06月16日 17:01
 */
@Component
public class WorkbenchStatisticsHandler implements InitializingBean {

    @Resource
    private BiExchangeService biExchangeService;
    @Resource
    private BiWorkbenchUrlConfig biWorkbenchUrlConfig;

    private Map<String, Handler<BiMetadataQuery, WorkbenchStatisticsDTO>> handlerMapping;

    public void handler(BiMetadataQuery query, WorkbenchStatisticsDTO statistics) {
        for (String handlerName : query.getHandlerList()) {
            Handler<BiMetadataQuery, WorkbenchStatisticsDTO> handler = handlerMapping.get(handlerName);
            if (handler != null) {
                handler.apply(query, statistics);
            }
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        handlerMapping = new HashMap<>();
        // 初始化每个数据模块需要统计的数据项
        handlerMapping.put("businessData", (p, r) -> {
            r.getBusinessData().setTransportingWaybill(biExchangeService.workbenchStatistics(p, biWorkbenchUrlConfig.getTransportingWaybill()));
            r.getBusinessData().setWaitingSettleWaybill(biExchangeService.workbenchStatistics(p, biWorkbenchUrlConfig.getWaitingSettleWaybill()));
            if (p.getRoleType() == 10 || p.getRoleType() == 30) {
                r.getBusinessData().setIssueGoods(biExchangeService.workbenchStatistics(p, biWorkbenchUrlConfig.getIssueGoods()));
                r.getBusinessData().setRunningOrder(biExchangeService.workbenchStatistics(p, biWorkbenchUrlConfig.getRunningOrder()));
                r.getBusinessData().setWaitingPaymentWaybill(biExchangeService.workbenchStatistics(p, biWorkbenchUrlConfig.getWaitingPaymentWaybill()));
                r.getBusinessData().setWaitingInvoiceWaybill(biExchangeService.workbenchStatistics(p, biWorkbenchUrlConfig.getWaitingInvoiceWaybill()));
            } else if (p.getRoleType() == 20) {
                r.getBusinessData().setWaitingReceiveOrder(biExchangeService.workbenchStatistics(p, biWorkbenchUrlConfig.getWaitingReceiveOrder()));
                r.getBusinessData().setAssigningVehicleOrder(biExchangeService.workbenchStatistics(p, biWorkbenchUrlConfig.getAssigningVehicleOrder()));
                r.getBusinessData().setWaitingAuditWaybill(biExchangeService.workbenchStatistics(p, biWorkbenchUrlConfig.getWaitingAuditWaybill()));
                r.getBusinessData().setAuditRejectWaybill(biExchangeService.workbenchStatistics(p, biWorkbenchUrlConfig.getAuditRejectWaybill()));
            }
        });
        handlerMapping.put("exceptionData", (p, r) -> {
            // 超时未结算
            r.getExceptionData().setNoSettleExpire(biExchangeService.workbenchStatistics(p, biWorkbenchUrlConfig.getNoSettleExpire()));
            if (p.getRoleType() == 10 || p.getRoleType() == 30) {
                // 超时未支付
                r.getExceptionData().setNoPaymentExpire(biExchangeService.workbenchStatistics(p, biWorkbenchUrlConfig.getNoPaymentExpire()));
                // 超时未开票
                r.getExceptionData().setNoInvoiceExpire(biExchangeService.workbenchStatistics(p, biWorkbenchUrlConfig.getNoInvoiceExpire()));
            } else if (p.getRoleType() == 20){
                // 超时未卸车
                r.getExceptionData().setNoUnloadExpire(biExchangeService.workbenchStatistics(p, biWorkbenchUrlConfig.getNoUnloadExpire()));
                // 超时未签收
                r.getExceptionData().setNoSignExpire(biExchangeService.workbenchStatistics(p, biWorkbenchUrlConfig.getNoSignExpire()));
            }
        });
        handlerMapping.put("taskData", (p, r) -> {
            if (p.getRoleType() == 30) {
                r.getTaskData().setWaitingPreAudit(biExchangeService.workbenchStatistics(p, biWorkbenchUrlConfig.getWaitingPreAudit()));
                r.getTaskData().setPaymentWaitingAudit(biExchangeService.workbenchStatistics(p, biWorkbenchUrlConfig.getPaymentWaitingAudit()));
                r.getTaskData().setInvoiceWaitingAudit(biExchangeService.workbenchStatistics(p, biWorkbenchUrlConfig.getInvoiceWaitingAudit()));
            }
        });
        handlerMapping.put("signData", (p, r) -> {
            if (p.getRoleType() == 10) {
                r.getSignData().setNewCustomer(biExchangeService.workbenchStatistics(p, biWorkbenchUrlConfig.getNewCustomer()));
            } else if (p.getRoleType() == 20) {
                r.getSignData().setNewShipper(biExchangeService.workbenchStatistics(p, biWorkbenchUrlConfig.getNewShipper()));
            } else {
                return;
            }
            r.getSignData().setNewCarrier(biExchangeService.workbenchStatistics(p, biWorkbenchUrlConfig.getNewCarrier()));
            r.getSignData().setNewVehicle(biExchangeService.workbenchStatistics(p, biWorkbenchUrlConfig.getNewVehicle()));
        });
        handlerMapping.put("collectData", (p, r) -> {
            if (p.getRoleType() == 30) {
                r.setCollectData(biExchangeService.workbenchStatistics(p, biWorkbenchUrlConfig.getCollectData()));
            }
        });

    }

    interface Handler<P, R> {
        void apply(P p, R r);
    }
}
