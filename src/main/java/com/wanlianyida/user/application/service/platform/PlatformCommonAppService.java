package com.wanlianyida.user.application.service.platform;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.platform.api.inter.AuthorityNoLoginInter;
import com.wanlianyida.platform.api.inter.PlatformCmDictionaryInter;
import com.wanlianyida.platform.api.inter.PlatformCmPlatformParameterInter;
import com.wanlianyida.platform.api.inter.PlatformPricingTypeConfigInter;
import com.wanlianyida.platform.api.model.dto.PlatformCmCityDTO;
import com.wanlianyida.platform.api.model.dto.PlatformCmDictionaryDTO;
import com.wanlianyida.platform.api.model.dto.PlatformCmPlatformParameterDTO;
import com.wanlianyida.platform.api.model.dto.PlatformPointConfigDTO;
import com.wanlianyida.user.application.model.dto.platform.PlatformCityListDTO;
import com.wanlianyida.user.application.model.dto.platform.PlatformDictionaryDTO;
import com.wanlianyida.user.application.model.dto.platform.PlatformParameterDTO;
import com.wanlianyida.user.application.model.dto.platform.TonAfterPointDTO;
import com.wanlianyida.user.application.model.query.platform.DictQuery;
import com.wanlianyida.user.application.model.query.platform.PlatformCityListQuery;
import com.wanlianyida.user.application.model.query.platform.PlatformParameterQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

@Slf4j
@Service
public class PlatformCommonAppService {

    @Resource
    private PlatformCmPlatformParameterInter platformCmPlatformParameterInter;

    @Resource
    private PlatformPricingTypeConfigInter platformPricingTypeConfigInter;

    @Resource
    private PlatformCmDictionaryInter platformCmDictionaryInter;

    @Resource
    private AuthorityNoLoginInter authorityNoLoginInter;


    /**
     * 字典值查询
     * @param query
     * @return
     */
    public Map<String, List<PlatformDictionaryDTO>> dictQuery(DictQuery query) {
        List<String> dictionaryIdList = query.getDictionaryIdList();
        ResultMode<Map<String, List<PlatformCmDictionaryDTO>>> resultMode = platformCmDictionaryInter.platformCmDictionaryByIds(dictionaryIdList);
        if(ObjUtil.isNull(resultMode) || !resultMode.isSucceed() || CollUtil.isEmpty(resultMode.getModel())){
            log.info("query#查询返回结果:{}", JSONUtil.toJsonStr(resultMode));
            return null;
        }
        Map<String, List<PlatformCmDictionaryDTO>> listMap = IterUtil.getFirst(resultMode.getModel());
        return BeanUtil.toBean(listMap, Map.class);
    }

    /**
     * 数值保留小数后位位数
     * @return
     */
    public List<TonAfterPointDTO> tonAfterPointQuery() {
        ResultMode<PlatformPointConfigDTO> resultMode = platformPricingTypeConfigInter.getTonAfterPoint();
        if(ObjUtil.isNull(resultMode) || !resultMode.isSucceed() || CollUtil.isEmpty(resultMode.getModel())){
            log.info("tonAfterPointQuery#查询返回结果:{}", JSONUtil.toJsonStr(resultMode));
            return null;
        }
        return BeanUtil.copyToList(resultMode.getModel(), TonAfterPointDTO.class);
    }

    /**
     * 平台参数查询
     * @param query
     * @return
     */
    public List<PlatformParameterDTO> parameterQuery(PlatformParameterQuery query) {
        log.info("query#查询请求参数{}", JSONUtil.toJsonStr(query));
        List<String> paraCodes = query.getParaCodes();
        ResultMode<PlatformCmPlatformParameterDTO> resultMode = platformCmPlatformParameterInter.getByParaCodes(paraCodes);
        if(ObjUtil.isNull(resultMode) || !resultMode.isSucceed() || CollUtil.isEmpty(resultMode.getModel())){
            log.info("query#查询返回结果:{}", JSONUtil.toJsonStr(resultMode));
            return null;
        }
        return BeanUtil.copyToList(resultMode.getModel(), PlatformParameterDTO.class);
    }

    /**
     * 城市列表查询
     * @param query
     * @return
     */
    public List<PlatformCityListDTO> cityListQuery(PlatformCityListQuery query) {
        ResultMode<PlatformCmCityDTO> resultMode = authorityNoLoginInter.getCityData(query.getParentNode());
        if(ObjUtil.isNull(resultMode) || !resultMode.isSucceed() || CollUtil.isEmpty(resultMode.getModel())){
            return null;
        }
        return BeanUtil.copyToList(resultMode.getModel(), PlatformCityListDTO.class);
    }
}
