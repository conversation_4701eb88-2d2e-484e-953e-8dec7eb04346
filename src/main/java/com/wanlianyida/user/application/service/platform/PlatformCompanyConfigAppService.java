package com.wanlianyida.user.application.service.platform;

import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.platform.api.model.query.CompanyConfigQuery;
import com.wanlianyida.user.infrastructure.exchange.PlatformExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

import javax.annotation.Resource;

@Slf4j
@Service
public class PlatformCompanyConfigAppService {


    @Resource
    private PlatformExchangeService platformExchangeService;

    /**
     * 获取平台企业配置表
     */
    public ResultMode<Map<String, Object>> selectConfigByCompanyId(CompanyConfigQuery query) {

        return platformExchangeService.selectConfigByCompanyId(query);
    }
}
