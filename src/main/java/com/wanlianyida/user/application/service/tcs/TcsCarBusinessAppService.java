package com.wanlianyida.user.application.service.tcs;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.tcs.api.model.dto.TcsCarLicenseVO;
import com.wanlianyida.tcs.api.model.query.TcsCarTypeQuery;
import com.wanlianyida.user.application.model.dto.tcs.GuacheDTO;
import com.wanlianyida.user.application.model.query.tcs.GuacheQuery;
import com.wanlianyida.user.infrastructure.exchange.TcsExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Slf4j
@Service
public class TcsCarBusinessAppService {

    @Resource
    private TcsExchangeService tcsExchangeService;

    /**
     * 查询挂车列表
     * @param pagingInfo
     * @return
     */
    public ResultMode<GuacheDTO> queryCompanyCarPage(PagingInfo<GuacheQuery> pagingInfo) {
        int currentPage = pagingInfo.getCurrentPage();
        int pageLength = pagingInfo.getPageLength();
        String carPlateNo = pagingInfo.filterModel.getCarPlateNo();

        PagingInfo<TcsCarTypeQuery> pageInfo = new PagingInfo<>();
        pageInfo.setCurrentPage(currentPage);
        pageInfo.setPageLength(pageLength);
        TcsCarTypeQuery query = new TcsCarTypeQuery();
        query.setPlateNo(carPlateNo);
        query.setTrailerType("3");
        pageInfo.setFilterModel(query);
        pageInfo.setCountTotal(true);

        ResultMode<TcsCarLicenseVO> resultMode = tcsExchangeService.queryCompanyCarPage(pageInfo);

        List<GuacheDTO> collect = new ArrayList<>();
        if(ObjUtil.isNotNull(resultMode) && resultMode.isSucceed() && CollUtil.isNotEmpty(resultMode.getModel())){
            collect = resultMode.getModel().stream().map(e -> {
                GuacheDTO vo = new GuacheDTO().setAssignCarType(e.getCarType()).setCarColor(e.getPlateColor())
                        .setStandardTransWeight(e.getVerifyLoadWeight()).setTransCurbWeight(e.getCurbWeight()).setTrailerPlateNumber(e.getPlateNo()).setCarId(null);
                if (e.getCarLength() != null) {
                    vo.setCarLength(new BigDecimal(e.getCarLength()).divide(new BigDecimal(1000)));
                }
                return vo;
            }).collect(Collectors.toList());

        }
        ResultMode success = ResultMode.success(collect,resultMode.getTotal());
        return success;
    }
}
