package com.wanlianyida.user.application.service.qrs;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.framework.lgicore.utils.JwtUtil;
import com.wanlianyida.user.application.model.command.qrs.IdentifyCodeDisplayConfigCommand;
import com.wanlianyida.user.application.model.dto.qrs.IdentifyCodeDisplayConfigDTO;
import com.wanlianyida.user.application.model.query.qrs.IdentifyCodeDisplayConfigByShareCodeQuery;
import com.wanlianyida.user.application.model.query.qrs.IdentifyCodeDisplayConfigQuery;
import com.wanlianyida.user.infrastructure.exchange.QrsExchangeService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年07月03日 13:13
 */
@Service
public class IdentifyCodeDisplayConfigAppService {

    @Resource
    private QrsExchangeService qrsExchangeService;

    public ResultMode<IdentifyCodeDisplayConfigDTO> queryByShareCode(IdentifyCodeDisplayConfigByShareCodeQuery query) {
        com.wanlianyida.qrs.api.model.query.IdentifyCodeDisplayConfigByShareCodeQuery apiQuery = BeanUtil.toBean(query, com.wanlianyida.qrs.api.model.query.IdentifyCodeDisplayConfigByShareCodeQuery.class);
        apiQuery.setCompanyId(JwtUtil.getCompanyIdByToken());
        return qrsExchangeService.queryByShareCode(apiQuery);
    }

    public ResultMode<IdentifyCodeDisplayConfigDTO> query(IdentifyCodeDisplayConfigQuery query) {
        com.wanlianyida.qrs.api.model.query.IdentifyCodeDisplayConfigQuery apiQuery = new com.wanlianyida.qrs.api.model.query.IdentifyCodeDisplayConfigQuery();
        if (query != null && StrUtil.isNotEmpty(query.getCompanyId())) {
            apiQuery.setCompanyId(query.getCompanyId());
        } else {
            apiQuery.setCompanyId(JwtUtil.getCompanyIdByToken());
        }
        return qrsExchangeService.queryDisplayConfig(apiQuery);
    }


    public ResultMode<String> insert(IdentifyCodeDisplayConfigCommand command) {
        com.wanlianyida.qrs.api.model.command.IdentifyCodeDisplayConfigCommand apiCommand = BeanUtil.toBean(command, com.wanlianyida.qrs.api.model.command.IdentifyCodeDisplayConfigCommand.class);
        apiCommand.setCompanyId(JwtUtil.getCompanyIdByToken());
        return qrsExchangeService.insertDisplayConfig(apiCommand);
    }
}
