package com.wanlianyida.user.application.service.tcs;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.tcs.api.inter.TcsPartnerInter;
import com.wanlianyida.tcs.api.model.command.TcsAddPartnerInfoCommand;
import com.wanlianyida.tcs.api.model.command.TcsDeletePartnerInfoCommand;
import com.wanlianyida.tcs.api.model.dto.TcsPartnerInfoDTO;
import com.wanlianyida.tcs.api.model.dto.TcsServicePartnerSetInfoDTO;
import com.wanlianyida.tcs.api.model.query.TcsGetPartnerInfoQuery;
import com.wanlianyida.tcs.api.model.query.TcsGetServicePartnerInfoQuery;
import com.wanlianyida.user.application.model.command.tcs.TcsAddPartnerCommand;
import com.wanlianyida.user.application.model.command.tcs.TcsDeletePartnerCommand;
import com.wanlianyida.user.application.model.dto.tcs.PartnerListDTO;
import com.wanlianyida.user.application.model.dto.tcs.TcsPartnerConfigDTO;
import com.wanlianyida.user.application.model.query.tcs.PartnerListQuery;
import com.wanlianyida.user.application.model.query.tcs.TcsPartnerConfigQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

@Slf4j
@Service
public class TcsPartnerAppService {

    @Resource
    private TcsPartnerInter tcsPartnerInter;

    /**
     * 查询订单货源对应的服务伙伴
     * @param query
     * @return
     */
    public List<TcsPartnerConfigDTO> partnerConfigQuery(TcsPartnerConfigQuery query) {
        log.info("partnerConfigQuery#查询请求参数:{}", JSONUtil.toJsonStr(query));
        TcsGetPartnerInfoQuery partnerListQuery = BeanUtil.copyProperties(query, TcsGetPartnerInfoQuery.class);
        ResultMode<TcsServicePartnerSetInfoDTO> resultMode = tcsPartnerInter.getGoodsAndOrderServicePartnerInfo(partnerListQuery);
        if(ObjUtil.isNull(resultMode) || !resultMode.isSucceed() || CollUtil.isEmpty(resultMode.getModel())){
            return new ArrayList<>();
        }
        return BeanUtil.copyToList(resultMode.getModel(), TcsPartnerConfigDTO.class);
    }

    /**
     * 新增伙伴
     * @param command
     * @return
     */
    public void partnerAdd(TcsAddPartnerCommand command) {
        log.info("partnerAdd#查询请求参数:{}", JSONUtil.toJsonStr(command));
        TcsAddPartnerInfoCommand tcsAddPartnerInfoCommand = BeanUtil.copyProperties(command, TcsAddPartnerInfoCommand.class);
        tcsPartnerInter.addGoodsServicePartnerInfo(tcsAddPartnerInfoCommand);
    }

    /**
     * 删除伙伴
     * @param command
     * @return
     */
    public void partnerDelete(TcsDeletePartnerCommand command) {
        log.info("partnerAdd#查询请求参数:{}", JSONUtil.toJsonStr(command));
        TcsDeletePartnerInfoCommand tcsDeletePartnerInfoCommand = BeanUtil.copyProperties(command, TcsDeletePartnerInfoCommand.class);
        tcsPartnerInter.deleteGoodsAndOrderServicePartnerInfo(tcsDeletePartnerInfoCommand);
    }

    /**
     * 查询伙伴列表
     * @param query
     * @return
     */
    public List<PartnerListDTO> partnerListQuery(PartnerListQuery query) {
        log.info("partnerListQuery#查询请求参数:{}", JSONUtil.toJsonStr(query));
        TcsGetServicePartnerInfoQuery tcsGetServicePartnerInfoQuery = BeanUtil.copyProperties(query, TcsGetServicePartnerInfoQuery.class);
        ResultMode<TcsPartnerInfoDTO> resultMode = tcsPartnerInter.getServicePartnerInfo(tcsGetServicePartnerInfoQuery);
        if(ObjUtil.isNull(resultMode) || !resultMode.isSucceed() || CollUtil.isEmpty(resultMode.getModel())){
            return null;
        }
        return BeanUtil.copyToList(resultMode.getModel(), PartnerListDTO.class);
    }
}
