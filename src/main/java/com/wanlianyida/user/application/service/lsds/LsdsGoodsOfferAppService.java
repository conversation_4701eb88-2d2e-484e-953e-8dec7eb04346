package com.wanlianyida.user.application.service.lsds;

import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.command.lsds.OfferAddCommand;
import com.wanlianyida.user.application.model.dto.lsds.OfferDetailsQueryDTO;
import com.wanlianyida.user.application.model.dto.lsds.OfferGoodsListQueryDTO;
import com.wanlianyida.user.application.model.dto.lsds.OfferListQueryDTO;
import com.wanlianyida.user.application.model.query.lsds.OfferDetailsQuery;
import com.wanlianyida.user.application.model.query.lsds.OfferGoodsListQuery;
import com.wanlianyida.user.application.model.query.lsds.OfferListQuery;
import com.wanlianyida.user.infrastructure.exchange.LsdsExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class LsdsGoodsOfferAppService {

    @Resource
    private LsdsExchangeService lsdsExchangeService;

    /**
     * 报价单列表
     * @param pageInfo
     * @return
     */
    public ResultMode<OfferListQueryDTO> offerListQuery(PagingInfo<OfferListQuery> pageInfo) {
        return lsdsExchangeService.offerListQuery(pageInfo);
    }

    /**
     * 查询报价货源详情
     * @param query
     * @return
     */
    public ResultMode<OfferDetailsQueryDTO> offerDetailsQuery(OfferDetailsQuery query) {
        return lsdsExchangeService.offerDetailsQuery(query);
    }

    /**
     * 我要承运-货源列表
     * @param pageInfo
     * @return
     */
    public ResultMode<OfferGoodsListQueryDTO> offerGoodsListQuery(PagingInfo<OfferGoodsListQuery> pageInfo) {
        return lsdsExchangeService.offerGoodsListQuery(pageInfo);
    }

    /**
     * 新增报价单
     * @param command
     * @return
     */
    public void offerAdd(OfferAddCommand command) {
        lsdsExchangeService.offerAdd(command);
    }
}
