package com.wanlianyida.user.application.service.aics;

import com.wanlianyida.baseaiadv.api.inter.AicsConversationMessageInter;
import com.wanlianyida.baseaiadv.api.model.command.AicsConversationMessageCommand;
import com.wanlianyida.baseaiadv.api.model.command.AicsConversationMessageUpdateCommand;
import com.wanlianyida.baseaiadv.api.model.command.MessageEvaluateCommand;
import com.wanlianyida.baseaiadv.api.model.command.MessageFeedbackCommand;
import com.wanlianyida.baseaiadv.api.model.dto.AicsConversationMessageDTO;
import com.wanlianyida.baseaiadv.api.model.query.AicsConversationMessageQuery;
import com.wanlianyida.framework.lgicore.utils.JwtUtil;
import com.wanlianyida.fssmodel.PagingInfo;
import com.wanlianyida.fssmodel.ResponseMessage;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025年05月15日 10:18
 */
@Service
public class AicsConversationMessageAppService {

    @Resource
    private AicsConversationMessageInter conversationMessageInter;

    public ResponseMessage<AicsConversationMessageDTO> save(AicsConversationMessageCommand command) {
        command.setUserId(JwtUtil.getTokenInfo().getUserBaseId());
        return conversationMessageInter.save(command);
    }

    public ResponseMessage<?> upvote(MessageEvaluateCommand command) {
        return conversationMessageInter.upvote(command);
    }

    public ResponseMessage<?> trample(MessageEvaluateCommand command) {
        return conversationMessageInter.trample(command);
    }

    public ResponseMessage<?> feedback(MessageFeedbackCommand command) {
        command.setUserId(JwtUtil.getTokenInfo().getUserBaseId());
        return conversationMessageInter.feedback(command);
    }

    public ResponseMessage<List<AicsConversationMessageDTO>> queryMessageList(PagingInfo<AicsConversationMessageQuery> query) {
        query.getFilterModel().setUserId(JwtUtil.getTokenInfo().getUserBaseId());
        return conversationMessageInter.queryMessageList(query);
    }

    public ResponseMessage<AicsConversationMessageDTO> retry(AicsConversationMessageUpdateCommand command) {
        command.setUserId(JwtUtil.getTokenInfo().getUserBaseId());
        return conversationMessageInter.retry(command);
    }
}
