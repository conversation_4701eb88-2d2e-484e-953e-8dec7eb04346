package com.wanlianyida.user.application.service.analysis;

import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.dto.analysis.WaybillSummaryDTO;
import com.wanlianyida.user.application.model.query.analysis.WaybillSummaryQuery;
import com.wanlianyida.user.domain.service.WaybillSummaryDomain;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

import javax.annotation.Resource;

/**
 * 运单统计
 */
@Slf4j
@Service
public class WaybillSummaryAppService {

    @Resource
    private WaybillSummaryDomain waybillSummaryDomain;

    /**
     * 托运人运单数据汇总
     * @param pagingInfo
     * @return
     */
    public ResultMode<Map<String, Object>> shipperWaybillList(PagingInfo<Map<String, Object>> pagingInfo) {
        return waybillSummaryDomain.shipperWaybillList(pagingInfo);
    }


    public ResultMode<WaybillSummaryDTO> waybillList(PagingInfo<WaybillSummaryQuery> queryPagingInfo) {
        return waybillSummaryDomain.waybillList(queryPagingInfo);
    }
}
