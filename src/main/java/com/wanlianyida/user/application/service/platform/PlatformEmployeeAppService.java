package com.wanlianyida.user.application.service.platform;

import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.command.platform.ForgetPasswordCommand;
import com.wanlianyida.user.application.model.command.platform.ResetPasswordCommand;
import com.wanlianyida.user.application.model.command.platform.UpdatePasswordCommand;
import com.wanlianyida.user.infrastructure.exchange.AuthExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class PlatformEmployeeAppService {

    @Resource
    private AuthExchangeService authExchangeService;


    /**
     * 更新密码
     * @param command
     */
    public ResultMode updatePassword(UpdatePasswordCommand command) {
        return authExchangeService.updatePassword(command);
    }

    /**
     * 重置密码
     * @param command
     * @return
     */
    public ResultMode resetPassword(ResetPasswordCommand command) {
        //默认新密码
        String newPw = "666666";
        command.setNewPassword(newPw);
        return authExchangeService.resetPassword(command);
    }

    /**
     * 忘记密码
     * @param command
     * @return
     */
    public ResultMode forgetPassword(ForgetPasswordCommand command) {
        return authExchangeService.forgetPassword(command);
    }
}
