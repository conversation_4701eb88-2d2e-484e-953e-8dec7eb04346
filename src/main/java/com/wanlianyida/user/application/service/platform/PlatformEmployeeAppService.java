package com.wanlianyida.user.application.service.platform;

import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.framework.lgicore.utils.EncryptUtils;
import com.wanlianyida.framework.lgicore.utils.JwtUtil;
import com.wanlianyida.framework.lgicore.utils.PasswordUtil;
import com.wanlianyida.platform.api.model.dto.FindCompanyMemberDTO;
import com.wanlianyida.user.application.model.command.platform.ForgetPasswordCommand;
import com.wanlianyida.user.application.model.command.platform.ResetPasswordCommand;
import com.wanlianyida.user.application.model.command.platform.UpdatePasswordCommand;
import com.wanlianyida.user.application.model.query.platform.ExternalForgetByTelephoneCheckQuery;
import com.wanlianyida.user.infrastructure.exchange.MdmExchangeService;
import com.wanlianyida.user.infrastructure.exchange.PlatformExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class PlatformEmployeeAppService {

    @Resource
    private MdmExchangeService mdmExchangeService;

    @Resource
    private PlatformExchangeService platformExchangeService;

    /**
     * 更新密码
     * @param command
     */
    public ResultMode updatePassword(UpdatePasswordCommand command) {
        String loginId = JwtUtil.getTokenInfo().getLoginId();
        ResultMode<FindCompanyMemberDTO> resultMode = platformExchangeService.findCompanyMember(loginId, null, null);
        if (ObjUtil.isNull(resultMode) || ! resultMode.isSucceed() || IterUtil.isEmpty(resultMode.getModel())) {
            return resultMode;
        }
        FindCompanyMemberDTO findCompanyMemberDTO = IterUtil.getFirst(resultMode.getModel());
        String operatorId = findCompanyMemberDTO.getPlfCompanyUserId();
        return mdmExchangeService.changePassword(command.getPassword(),command.getNewPassword(),operatorId);
    }

    /**
     * 重置密码
     * @param command
     * @return
     */
    public ResultMode resetPassword(ResetPasswordCommand command) {
        //先通过登录账号名获取operatorId;
        try {
            ResultMode<FindCompanyMemberDTO> resultMode = platformExchangeService.findCompanyMember(null, command.getLoginName(), null);
            if (ObjUtil.isNull(resultMode) || ! resultMode.isSucceed() || IterUtil.isEmpty(resultMode.getModel())) {
                return resultMode;
            }
            FindCompanyMemberDTO findCompanyMemberDTO = IterUtil.getFirst(resultMode.getModel());
            String operatorId = findCompanyMemberDTO.getPlfCompanyUserId();
            String newPassword = PasswordUtil.generatePassword();
            String EncryptKey = "f#62($0!=4^F824.";
            String encryptNewPassword = EncryptUtils.aesEncrypt(newPassword, EncryptKey);
            return mdmExchangeService.changePassword(null,encryptNewPassword,operatorId);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultMode.fail("重置密码失败");
        }
    }

    /**
     * 忘记密码
     * @param command
     * @return
     */
    public ResultMode forgetPassword(ForgetPasswordCommand command) {
        log.info("forgetPassword#忘记密码请求参数:{}", JSONUtil.toJsonStr(command));
        ResultMode<FindCompanyMemberDTO> resultMode = platformExchangeService.findCompanyMember(null, null, command.getLoginMobile());
        if (ObjUtil.isNull(resultMode) || ! resultMode.isSucceed() || IterUtil.isEmpty(resultMode.getModel())) {
            return resultMode;
        }
        FindCompanyMemberDTO findCompanyMemberDTO = IterUtil.getFirst(resultMode.getModel());
        String operatorId = findCompanyMemberDTO.getPlfCompanyUserId();
        return mdmExchangeService.changePassword(null,command.getNewPassword(),operatorId);
    }

    /**
     * 忘记密码校验
     * @param query
     * @return
     */
    public ResultMode<Boolean> forgetPasswordByTelephoneCheck(ExternalForgetByTelephoneCheckQuery query) {
        return platformExchangeService.forgetPasswordByTelephoneCheck(query);
    }
}
