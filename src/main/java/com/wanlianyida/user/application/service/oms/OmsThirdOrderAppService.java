package com.wanlianyida.user.application.service.oms;

import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.dto.oms.ThirdOrderDetailQueryDTO;
import com.wanlianyida.user.application.model.dto.oms.ThirdOrderPageListDTO;
import com.wanlianyida.user.application.model.query.oms.ThirdOrderDetailQuery;
import com.wanlianyida.user.application.model.query.oms.ThirdOrderPageQuery;
import com.wanlianyida.user.infrastructure.exchange.OmsExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class OmsThirdOrderAppService {

    @Resource
    private OmsExchangeService omsExchangeService;

    public ResultMode<ThirdOrderPageListDTO> thirdOrderPageQuery(PagingInfo<ThirdOrderPageQuery> pagingInfo) {
        return omsExchangeService.thirdOrderPageQuery(pagingInfo);
    }

    public ResultMode<ThirdOrderDetailQueryDTO> thirdOrderDetailQuery(ThirdOrderDetailQuery query) {
        return omsExchangeService.thirdOrderDetailQuery(query);
    }

}
