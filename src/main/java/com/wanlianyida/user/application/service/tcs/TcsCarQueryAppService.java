package com.wanlianyida.user.application.service.tcs;

import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.tcs.api.model.dto.TcsBusinessAttachmentInfoVO;
import com.wanlianyida.tcs.api.model.dto.TcsCarLicenseVO;
import com.wanlianyida.tcs.api.model.query.TcsCarPlateNoAndColorFilter;
import com.wanlianyida.user.infrastructure.exchange.TcsExchangeService;
import com.wanlianyida.user.infrastructure.exchange.UploadExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Slf4j
@Service
public class TcsCarQueryAppService {

    @Resource
    private TcsExchangeService tcsExchangeService;

    @Resource
    private UploadExchangeService uploadExchangeService;

    /**
     * 车牌号和颜色 查询 行驶证信息- 包含照片附件信息
     */
    public ResultMode<TcsCarLicenseVO> carLicenseAttachment(TcsCarPlateNoAndColorFilter query) {
        TcsCarLicenseVO tcsCarLicenseVO = tcsExchangeService.queryCarLicenseAttachment(query);
        if (ObjUtil.isNull(tcsCarLicenseVO)) {
            return ResultMode.success();
        }

        //转换url
        List<TcsBusinessAttachmentInfoVO> attachmentList = tcsCarLicenseVO.getAttachmentes();
        if (IterUtil.isEmpty(attachmentList)) {
            return ResultMode.success(tcsCarLicenseVO);
        }

        List<String> urls = attachmentList.stream().filter(att -> StrUtil.isNotBlank(att.getAttachmentAddress()))
                .map(TcsBusinessAttachmentInfoVO::getAttachmentAddress).collect(Collectors.toList());
        Map<String, String> urlMap = uploadExchangeService.convertUrlsWLYD(urls);
        if (MapUtil.isEmpty(urlMap)) {
            return ResultMode.success(tcsCarLicenseVO);
        }

        attachmentList.stream().filter(att -> StrUtil.isNotBlank(att.getAttachmentAddress()))
                .forEach(att -> att.setAttachmentAddress(urlMap.get(att.getAttachmentAddress())));

        return ResultMode.success(tcsCarLicenseVO);
    }

}
