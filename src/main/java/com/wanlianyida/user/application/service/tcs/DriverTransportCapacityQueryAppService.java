package com.wanlianyida.user.application.service.tcs;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.platform.api.model.dto.PlatformUmDriverReceiptInfoDTO;
import com.wanlianyida.platform.api.model.dto.PlatformUmUserbaseinfoDTO;
import com.wanlianyida.user.application.model.dto.tcs.AssignDriverTransportCapacityDTO;
import com.wanlianyida.user.application.model.dto.tcs.DriverTransportCapacityDTO;
import com.wanlianyida.user.application.model.query.tcs.AssignDriverTransportCapacityQuery;
import com.wanlianyida.user.application.model.query.tcs.DriverTransportCapacityQuery;
import com.wanlianyida.user.infrastructure.enums.LgiOrchUserEnum;
import com.wanlianyida.user.infrastructure.exchange.EvalExchangeService;
import com.wanlianyida.user.infrastructure.exchange.PlatformExchangeService;
import com.wanlianyida.user.infrastructure.exchange.TcsExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 司机运力查询
 *
 * <AUTHOR>
 * @date 2025/03/04
 */
@Slf4j
@Service
public class DriverTransportCapacityQueryAppService {

    @Resource
    private TcsExchangeService tcsExchangeService;

    @Resource
    private EvalExchangeService evalExchangeService;

    @Resource
    private PlatformExchangeService platformExchangeService;

    /**
     * 3pl公路订单管理-派车-选车辆-待选司机分页+运单补录-派车-选车辆-待选司机分页
     *
     * @param pagingInfo
     * @return {@link ResultMode }<{@link DriverTransportCapacityDTO }>
     */
    public ResultMode<DriverTransportCapacityDTO> driversQueryPage(PagingInfo<DriverTransportCapacityQuery> pagingInfo) {
        ResultMode<DriverTransportCapacityDTO> resultMode = tcsExchangeService.queryDriversCapacity(pagingInfo);
        if (resultMode == null) {
            return ResultMode.success();
        }
        List<DriverTransportCapacityDTO> list = resultMode.getModel();
        // 11-线上的再批量查platform司机收款信息：有的就置为网络（loginStatus：11-线上 21-线下 12网络）
        List<String> userBaseIdList = list.stream().filter(item -> StrUtil.equals(item.getLoginStatus(), LgiOrchUserEnum.DriverLoginStatusEnum.ONLINE.getCode())).map(DriverTransportCapacityDTO::getUserBaseId).collect(Collectors.toList());
        List<PlatformUmDriverReceiptInfoDTO> driverReceiptInfoList = platformExchangeService.getDriverReceiptInfoByUserBaseIdList(userBaseIdList);
        Map<String, PlatformUmDriverReceiptInfoDTO> driverReceiptInfoMap = driverReceiptInfoList.stream().collect(Collectors.toMap(PlatformUmDriverReceiptInfoDTO::getUserBaseId, Function.identity(), (v1, v2) -> v1));
        if (CollUtil.isNotEmpty(driverReceiptInfoList)) {
            list.forEach(driver -> {
                PlatformUmDriverReceiptInfoDTO driverReceiptInfo = driverReceiptInfoMap.get(driver.getUserBaseId());
                if (ObjUtil.isNotNull(driverReceiptInfo)) {
                    driver.setLoginStatus(LgiOrchUserEnum.DriverLoginStatusEnum.NET.getCode());
                }
            });
        }
        resultMode.setModel(list);
        return resultMode;
    }

    /**
     * 3pl司机货源-发布-指定运力-待选司机分页
     *
     * @param pagingInfo
     * @return {@link ResultMode }<{@link AssignDriverTransportCapacityDTO }>
     */
    public ResultMode<AssignDriverTransportCapacityDTO> assignDriversQueryPage(PagingInfo<AssignDriverTransportCapacityQuery> pagingInfo) {
        ResultMode<AssignDriverTransportCapacityDTO> resultMode = tcsExchangeService.queryAssignDriverCapacity(pagingInfo);
        if (resultMode == null) {
            return ResultMode.success();
        }
        List<AssignDriverTransportCapacityDTO> list = resultMode.getModel();
        // 线上的再批量查platform司机收款信息：有的就置为网络（networkDriver：线上 线下 网络）
        List<String> userBaseIdList = list.stream().filter(item -> StrUtil.equals(item.getLoginStatus(), LgiOrchUserEnum.DriverLoginStatusEnum.ONLINE.getCode())).map(AssignDriverTransportCapacityDTO::getUserBaseId).collect(Collectors.toList());
        List<PlatformUmDriverReceiptInfoDTO> driverReceiptInfoList = platformExchangeService.getDriverReceiptInfoByUserBaseIdList(userBaseIdList);
        Map<String, PlatformUmDriverReceiptInfoDTO> driverReceiptInfoMap = driverReceiptInfoList.stream().collect(Collectors.toMap(PlatformUmDriverReceiptInfoDTO::getUserBaseId, Function.identity(), (v1, v2) -> v1));
        list.forEach(driver -> {
            PlatformUmDriverReceiptInfoDTO driverReceiptInfo = driverReceiptInfoMap.get(driver.getUserBaseId());
            if (ObjUtil.isNotNull(driverReceiptInfo)) {
                driver.setLoginStatus(LgiOrchUserEnum.DriverLoginStatusEnum.NET.getCode());
                driver.setNetworkDriver(LgiOrchUserEnum.DriverLoginStatusEnum.NET.getDesc());
            }
            if (StrUtil.equals(driver.getLoginStatus(), LgiOrchUserEnum.DriverLoginStatusEnum.ONLINE.getCode())) {
                driver.setNetworkDriver(LgiOrchUserEnum.DriverLoginStatusEnum.ONLINE.getDesc());
            }
            if (StrUtil.equals(driver.getLoginStatus(), LgiOrchUserEnum.DriverLoginStatusEnum.OFFLINE.getCode())) {
                driver.setNetworkDriver(LgiOrchUserEnum.DriverLoginStatusEnum.OFFLINE.getDesc());
            }
        });
        // 查evel司机评级
        List<String> driverIds = list.stream().filter(car -> StrUtil.isNotBlank(car.getDriverId())).map(AssignDriverTransportCapacityDTO::getDriverId).collect(Collectors.toList());
        Map<String, Double> evalDriverMap = evalExchangeService.getEvalDriver(driverIds);
        if (MapUtil.isNotEmpty(evalDriverMap)) {
            list.stream().filter(item -> StrUtil.isNotBlank(item.getDriverId())).forEach(driver -> {
                if (ObjUtil.isNotNull(evalDriverMap.get(driver.getDriverId()))) {
                    driver.setDriverRating(String.valueOf(evalDriverMap.get(driver.getDriverId())));
                }
            });
        }
        // 查platform司机实名认证状态
        if (CollUtil.isNotEmpty(userBaseIdList)) {
            List<PlatformUmUserbaseinfoDTO> userBaseInfoList = platformExchangeService.getDriverUserBaseByUserBaseIdList(userBaseIdList);
            Map<String, PlatformUmUserbaseinfoDTO> userBaseInfoMap = userBaseInfoList.stream().collect(Collectors.toMap(PlatformUmUserbaseinfoDTO::getUserBaseId, item -> item));
            if (CollUtil.isNotEmpty(userBaseInfoList)) {
                list.forEach(driver -> {
                    // verifyStatusDetail大于20的都是已认证
                    PlatformUmUserbaseinfoDTO userBaseInfo = userBaseInfoMap.get(driver.getUserBaseId());
                    if (ObjUtil.isNotNull(userBaseInfo) && StrUtil.isNotBlank(userBaseInfo.getVerifyStatusDetail()) && Integer.parseInt(userBaseInfo.getVerifyStatusDetail()) > 20) {
                        driver.setESiginStatus(LgiOrchUserEnum.DriverESiginStatusEnum.VERIFIED.getCode());
                    } else {
                        driver.setESiginStatus(LgiOrchUserEnum.DriverESiginStatusEnum.UNVERIFIED.getCode());
                    }
                });
            }
        }
        resultMode.setModel(list);
        return resultMode;
    }
}
