package com.wanlianyida.user.application.service.qrs;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.lsds.api.model.dto.GoodsSourceDetailDTO;
import com.wanlianyida.lsds.api.model.query.GoodsSourceQuery;
import com.wanlianyida.rms.api.inter.model.command.TransportDistanceCommand;
import com.wanlianyida.rms.api.inter.model.command.TransportDistanceParamCommand;
import com.wanlianyida.rms.api.inter.model.dto.RiskExecInfoDTO;
import com.wanlianyida.user.application.model.command.qrs.IdentifyCodeGoodsBindCommand;
import com.wanlianyida.user.application.model.dto.oms.OrderDetailQueryDTO;
import com.wanlianyida.user.application.model.query.oms.OrderDetailQuery;
import com.wanlianyida.user.infrastructure.exception.UserErrorCode;
import com.wanlianyida.user.infrastructure.exchange.LsdsExchangeService;
import com.wanlianyida.user.infrastructure.exchange.OmsExchangeService;
import com.wanlianyida.user.infrastructure.exchange.QrsExchangeService;
import com.wanlianyida.user.infrastructure.exchange.RmsExchangeService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年08月06日 15:50
 */
@Service
public class QrsIdentifyCodeGoodsAppService {

    @Resource
    private QrsExchangeService qrsExchangeService;
    @Resource
    private LsdsExchangeService lsdsExchangeService;
    @Resource
    private RmsExchangeService rmsExchangeService;
    @Resource
    private OmsExchangeService omsExchangeService;

    public ResultMode<String> bind(IdentifyCodeGoodsBindCommand command) {
        // R200 网货运输里程合规风控
        OrderDetailQuery orderQuery = new OrderDetailQuery();
        orderQuery.setOrderId(command.getBizOrderNo());
        ResultMode<OrderDetailQueryDTO> resultMode = omsExchangeService.orderDetailQuery(orderQuery);
        if (!resultMode.isSucceed() || CollectionUtil.isEmpty(resultMode.getModel())) {
            return ResultMode.fail(UserErrorCode.TMS_ORDER_NOT_EXIST.getCode(), UserErrorCode.TMS_ORDER_NOT_EXIST.getMsg());
        }
        OrderDetailQueryDTO omsOrder = resultMode.getModel().get(0);
        String shipperCompanyId = "";
        //pow订单,直接去订单的发货企业ID
        if(command.getBizOrderNo().startsWith("POW")){
            shipperCompanyId = omsOrder.getShipperCompanyId();
        }else {
            GoodsSourceQuery query = new GoodsSourceQuery();
            query.setGoodsId(omsOrder.getGoodsId());
            GoodsSourceDetailDTO goodsDetail = lsdsExchangeService.queryDetail(query).getModel().get(0);
            shipperCompanyId = goodsDetail.getGoodsSource().getCompanyId();
        }
        TransportDistanceParamCommand transportDistanceParamCommand = getTransportDistanceParamCommand(command, omsOrder, shipperCompanyId);
        // 调用风控
        ResultMode<RiskExecInfoDTO> riskResultMode = rmsExchangeService.distanceRuleCalculate(transportDistanceParamCommand);
        if (!riskResultMode.isSucceed()) {
            return ResultMode.fail(UserErrorCode.RISK_REQUEST_FAIL.getCode(), UserErrorCode.RISK_REQUEST_FAIL.getMsg());
        }
        if (CollectionUtil.isNotEmpty(riskResultMode.getModel())) {
            RiskExecInfoDTO riskExecInfoDTO = riskResultMode.getModel().get(0);
            if ("400".equals(riskExecInfoDTO.getRiskLevel())) {
                return ResultMode.fail(UserErrorCode.RISK_CALCULATE_FAIL.getCode(), riskExecInfoDTO.getRiskContent());
            }
        }
        return qrsExchangeService.bind(BeanUtil.toBean(command, com.wanlianyida.qrs.api.model.command.IdentifyCodeGoodsBindCommand.class));
    }

    private static TransportDistanceParamCommand getTransportDistanceParamCommand(IdentifyCodeGoodsBindCommand command, OrderDetailQueryDTO omsOrder, String shipperCompanyId) {
        TransportDistanceParamCommand transportDistanceParamCommand = new TransportDistanceParamCommand();
        transportDistanceParamCommand.setBussSceneType("B3010");
        transportDistanceParamCommand.setRiskSceneType("R288");
        transportDistanceParamCommand.setBussId(omsOrder.getOrderId());
        transportDistanceParamCommand.setRiskBatchNo(omsOrder.getOrderId());
        transportDistanceParamCommand.setBizId(omsOrder.getOrderId());
        TransportDistanceCommand transportDistanceCommand = new TransportDistanceCommand();
        transportDistanceCommand.setTransportMileage(new BigDecimal(omsOrder.getTransportMileage()));
        transportDistanceCommand.setFreightType(command.getFreightType());
        transportDistanceCommand.setNetworkMainBodyId(command.getNetworkFreightId());
        transportDistanceCommand.setShipperCompanyId(shipperCompanyId);
        transportDistanceCommand.setCarrierCompanyId(omsOrder.getRealityCarrierCompanyId());
        transportDistanceParamCommand.setTransportDistanceVO(transportDistanceCommand);
        return transportDistanceParamCommand;
    }
}
