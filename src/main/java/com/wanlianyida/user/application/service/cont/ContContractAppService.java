package com.wanlianyida.user.application.service.cont;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.basecont.api.model.dto.ContractDetailDTO;
import com.wanlianyida.basecont.api.model.query.ContractListQuery;
import com.wanlianyida.framework.lgicache.impl.RedisService;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.platform.api.model.dto.PlatformUmCompanyDTO;
import com.wanlianyida.platform.api.model.query.CompanyConfigQuery;
import com.wanlianyida.platform.api.model.query.PlatformUmCompanyQuery;
import com.wanlianyida.user.application.model.dto.cont.CheckContractSignDTO;
import com.wanlianyida.user.application.model.dto.cont.ContractTemplateBatchDTO;
import com.wanlianyida.user.application.model.query.cont.CheckContractSignQuery;
import com.wanlianyida.user.application.model.query.cont.ContractTemplateBatchQuery;
import com.wanlianyida.user.application.model.query.cont.NeedSignContractQuery;
import com.wanlianyida.user.infrastructure.constant.ParamConst;
import com.wanlianyida.user.infrastructure.constant.RedisKeyConstants;
import com.wanlianyida.user.infrastructure.enums.CompanyUserTypeEnum;
import com.wanlianyida.user.infrastructure.enums.ContractTypeEnum;
import com.wanlianyida.user.infrastructure.enums.SwitchStatusEnum;
import com.wanlianyida.user.infrastructure.exception.UserErrorCode;
import com.wanlianyida.user.infrastructure.exchange.ContExchangeService;
import com.wanlianyida.user.infrastructure.exchange.PlatformExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Slf4j
@Service
public class ContContractAppService {

    @Resource
    private ContExchangeService contExchangeService;

    @Resource
    private PlatformExchangeService platformExchangeService;

    @Resource
    private RedisService redisService;

    /**
     * 默认允许无合同发货天数
     */
    private static final String DEFAULT_NO_CONTRACT_DAYS = "5";


    /**
     * 批量查询协议模板
     * @param query
     * @return
     */
    public List<ContractTemplateBatchDTO> batchTemplateQuery(ContractTemplateBatchQuery query) {
        return contExchangeService.batchTemplateQuery(query);
    }

    /**
     * 需要签署的协议
     * @param query
     * @return
     */
    public Map<String, Object> needSignContract(NeedSignContractQuery query) {
        return contExchangeService.needSignContract(query);
    }

    /**
     * 校验合同是否允许签署
     * @param query
     * @return
     */
    public ResultMode<CheckContractSignDTO> checkContractSign(CheckContractSignQuery query) {
        // 1. 获取并验证企业信息
        Map<String, PlatformUmCompanyDTO> companyMap = getAndValidateCompanies(query);
        if (ObjUtil.isNull(companyMap)) {
            return ResultMode.fail(UserErrorCode.COMPANY_NOT_EXIST.getCode(), UserErrorCode.COMPANY_NOT_EXIST.getMsg());
        }

        // 2. 查询合同详情
        ContractDetailDTO contractDetail = queryContractDetail(query, companyMap);

        // 3. 如果存在有效合同，清理缓存并返回成功
        if (contractDetail != null) {
            clearEarliestNoContractDateIfNeeded(query);
            CheckContractSignDTO checkContractSignDTO = new CheckContractSignDTO();
            checkContractSignDTO.setContractId(contractDetail.getId().toString());
            return ResultMode.success(checkContractSignDTO);
        }

        // 4. 检查转调度业务且乙方为外部用户的特殊情况
        if (isDispatchBusinessWithExternalPartyB(query)) {
            return ResultMode.success();
        }

        // 5. 不存在有效合同时的处理
        return handleNoValidContract(query, companyMap);
    }

    /**
     * 获取并验证企业信息
     */
    private Map<String, PlatformUmCompanyDTO> getAndValidateCompanies(CheckContractSignQuery query) {
        PlatformUmCompanyQuery companyQuery = new PlatformUmCompanyQuery();
        List<String> exCompanyIdList = new ArrayList<>();
        exCompanyIdList.add(query.getPartyACompanyId());
        exCompanyIdList.add(query.getPartyBCompanyId());
        companyQuery.setExCompanyIdList(exCompanyIdList);

        List<PlatformUmCompanyDTO> companies = platformExchangeService.getCompanyListByModel(companyQuery);
        if (CollUtil.isEmpty(companies) || companies.size() != 2) {
            return null;
        }

        return companies.stream().collect(Collectors.toMap(PlatformUmCompanyDTO::getCompanyId, v -> v));
    }

    /**
     * 检查是否为转调度业务且乙方为外部用户
     */
    private boolean isDispatchBusinessWithExternalPartyB(CheckContractSignQuery query) {
        if (!ContractTypeEnum.DISPATCH.equals(query.getContractType())) {
            return false;
        }

        Map<String, Object> companyConfig = getCompanyConfig(query.getPartyBCompanyId());
        String companyUserType = (String) companyConfig.get("companyUserType");
        return !CompanyUserTypeEnum.INTERNAL.equals(companyUserType);
    }

    /**
     * 查询合同详情
     */
    private ContractDetailDTO queryContractDetail(CheckContractSignQuery query, Map<String, PlatformUmCompanyDTO> companyMap) {
        ContractListQuery contractQuery = new ContractListQuery();
        contractQuery.setContractType(query.getContractType());
        contractQuery.setContractNature(query.getContractNature());
        contractQuery.setPartyALicenseNo(companyMap.get(query.getPartyACompanyId()).getSocialCreditCode());
        contractQuery.setPartyBLicenseNo(companyMap.get(query.getPartyBCompanyId()).getSocialCreditCode());
        contractQuery.setStartTime(query.getStartTime());
        contractQuery.setEndTime(query.getEndTime());
        return contExchangeService.queryContractDetailByCondition(contractQuery);
    }

    /**
     * 清理最早无合同日期缓存（仅承运业务）
     */
    private void clearEarliestNoContractDateIfNeeded(CheckContractSignQuery query) {
        if (ContractTypeEnum.DISPATCH.equals(query.getContractType())) {
            String redisKey = buildNoContractRedisKey(query);
            redisService.remove(redisKey);
        }
    }

    /**
     * 处理不存在有效合同的情况
     */
    private ResultMode<CheckContractSignDTO> handleNoValidContract(CheckContractSignQuery query, Map<String, PlatformUmCompanyDTO> companyMap) {
        // 记录最早无合同日期（仅承运业务）
        recordEarliestNoContractDateIfNeeded(query);

        // 检查系统开关
        String paramValue = platformExchangeService.getParamValue(ParamConst.CONTRACT_ALLOW_SHIPMENT);
        if (!SwitchStatusEnum.ON.equals(paramValue)) {
            return createContractNotExistResult(query, companyMap);
        }

        // 网货业务直接允许
        if (ContractTypeEnum.FREIGHT.equals(query.getContractType())) {
            return ResultMode.success();
        }

        // 承运业务需要检查允许无合同发货的天数限制
        return checkDispatchTimeLimit(query, companyMap);
    }

    /**
     * 记录最早无合同日期（仅承运业务且不存在记录时）
     */
    private void recordEarliestNoContractDateIfNeeded(CheckContractSignQuery query) {
        if (!ContractTypeEnum.DISPATCH.equals(query.getContractType())) {
            return;
        }

        String redisKey = buildNoContractRedisKey(query);
        if (!redisService.hasKey(redisKey)) {
            redisService.set(redisKey, String.valueOf(System.currentTimeMillis()));
        }
    }

    /**
     * 构建无合同Redis缓存Key
     */
    private String buildNoContractRedisKey(CheckContractSignQuery query) {
        return StrUtil.format(RedisKeyConstants.COMPANY_EARLIEST_NO_CONTRACT_DATE,
                query.getPartyACompanyId() + "_" + query.getContractType());
    }

    /**
     * 创建合同不存在的错误结果
     */
    private ResultMode<CheckContractSignDTO> createContractNotExistResult(CheckContractSignQuery query, Map<String, PlatformUmCompanyDTO> companyMap) {
        if (ContractTypeEnum.FREIGHT.equals(query.getContractType())) {
            PlatformUmCompanyDTO partyBCompany = companyMap.get(query.getPartyBCompanyId());
            String msg = String.format(UserErrorCode.DELIVER_CONTRACT_NOT_EXIST.getMsg(), partyBCompany.getCompanyName());
            return ResultMode.fail(UserErrorCode.DELIVER_CONTRACT_NOT_EXIST.getCode(), msg);
        } else {
            PlatformUmCompanyDTO partyACompany = companyMap.get(query.getPartyACompanyId());
            String msg = String.format(UserErrorCode.DELIVER_CONTRACT_NOT_EXIST.getMsg(), partyACompany.getCompanyName());
            return ResultMode.fail(UserErrorCode.DISPATCH_CONTRACT_NOT_EXIST.getCode(), msg);
        }
    }

    /**
     * 检查承运业务的时间限制
     */
    private ResultMode<CheckContractSignDTO> checkDispatchTimeLimit(CheckContractSignQuery query, Map<String, PlatformUmCompanyDTO> companyMap) {
        Map<String, Object> companyConfig = getCompanyConfig(query.getPartyACompanyId());
        String allowedDays = getNoContractAllowShipmentTime(companyConfig);

        String redisKey = buildNoContractRedisKey(query);

        long currentTime = System.currentTimeMillis();
        long earliestTime = Long.parseLong(redisService.get(redisKey));
        long daysDiff = (currentTime - earliestTime) / (1000 * 60 * 60 * 24);

        PlatformUmCompanyDTO partyACompany = companyMap.get(query.getPartyACompanyId());

        if (daysDiff > Long.parseLong(allowedDays)) {
            return ResultMode.fail(UserErrorCode.DISPATCH_CONTRACT_NOT_EXIST.getCode(),
                    String.format(UserErrorCode.DISPATCH_CONTRACT_NOT_EXIST.getMsg(), partyACompany.getCompanyShortName()));
        }
        CheckContractSignDTO checkContractSignDTO = new CheckContractSignDTO();
        checkContractSignDTO.setDesc(String.format(UserErrorCode.DISPATCH_CONTRACT_EXPIRED.getMsg(),
                partyACompany.getCompanyShortName()));
        return ResultMode.success(checkContractSignDTO);
    }

    /**
     * 获取企业允许无合同发货天数
     */
    public String getNoContractAllowShipmentTime(Map<String, Object> companyConfigMap) {
        //开关打开,判断当前企业允许无合同发货日期是否在范围内,默认5天
        if (ObjUtil.isNull(companyConfigMap.get("noContractAllowShipmentTime"))) {
            return DEFAULT_NO_CONTRACT_DAYS;
        }
        return String.valueOf(companyConfigMap.get("noContractAllowShipmentTime"));
    }

    /**
     * 获取企业配置
     */
    public Map<String, Object> getCompanyConfig(String companyId) {
        CompanyConfigQuery companyConfigQuery = new CompanyConfigQuery();
        companyConfigQuery.setCompanyId(companyId);
        ResultMode<Map<String, Object>> resultMode = platformExchangeService.selectConfigByCompanyId(companyConfigQuery);
        if (!resultMode.isSucceed() || CollUtil.isEmpty(resultMode.getModel())) {
            return new HashMap<>();
        }
        return CollUtil.getFirst(resultMode.getModel());
    }

}
