package com.wanlianyida.user.application.service.cont;

import com.wanlianyida.user.application.model.dto.cont.ContractTemplateBatchDTO;
import com.wanlianyida.user.application.model.query.cont.ContractTemplateBatchQuery;
import com.wanlianyida.user.application.model.query.cont.NeedSignContractQuery;
import com.wanlianyida.user.infrastructure.exchange.ContExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

@Slf4j
@Service
public class ContContractAppService {

    @Resource
    private ContExchangeService contExchangeService;


    /**
     * 批量查询协议模板
     * @param query
     * @return
     */
    public List<ContractTemplateBatchDTO> batchTemplateQuery(ContractTemplateBatchQuery query) {
        return contExchangeService.batchTemplateQuery(query);
    }

    /**
     * 需要签署的协议
     * @param query
     * @return
     */
    public Map<String, Object> needSignContract(NeedSignContractQuery query) {
        return contExchangeService.needSignContract(query);
    }
}
