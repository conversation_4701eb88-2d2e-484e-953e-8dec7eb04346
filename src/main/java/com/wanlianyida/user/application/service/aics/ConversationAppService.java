package com.wanlianyida.user.application.service.aics;

import com.wanlianyida.baseaiadv.api.inter.ConversationInter;
import com.wanlianyida.baseaiadv.api.model.command.ConversationCommand;
import com.wanlianyida.baseaiadv.api.model.command.ConversationUpdateCommand;
import com.wanlianyida.baseaiadv.api.model.dto.ConversationDTO;
import com.wanlianyida.baseaiadv.api.model.enums.PlatformTypeEnum;
import com.wanlianyida.baseaiadv.api.model.query.ConversationQuery;
import com.wanlianyida.framework.lgicore.utils.JwtUtil;
import com.wanlianyida.fssmodel.IdCommand;
import com.wanlianyida.fssmodel.PagingInfo;
import com.wanlianyida.fssmodel.ResponseMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025年04月17日 11:28
 */
@Slf4j
@Service
public class ConversationAppService {

    @Resource
    private ConversationInter conversationInter;

    public ResponseMessage<ConversationDTO> create(ConversationCommand command) {
        command.setUserId(JwtUtil.getTokenInfo().getUserBaseId());
        command.setPlatformType(PlatformTypeEnum.LGI.getCode());
        return conversationInter.create(command);
    }

    public ResponseMessage<List<ConversationDTO>> queryConversationList(ConversationQuery query) {
        query.setUserId(JwtUtil.getTokenInfo().getUserBaseId());
        query.setPlatformType(PlatformTypeEnum.LGI.getCode());
        return conversationInter.queryConversationList(query);
    }

    public ResponseMessage<List<ConversationDTO>> pageConversationList(PagingInfo<ConversationQuery> pageQuery) {
        pageQuery.getFilterModel().setUserId(JwtUtil.getTokenInfo().getUserBaseId());
        pageQuery.getFilterModel().setPlatformType(PlatformTypeEnum.LGI.getCode());
        return conversationInter.pageConversationList(pageQuery);
    }

    public ResponseMessage<?> delete(IdCommand command) {
        return conversationInter.delete(command);
    }

    public ResponseMessage<?> updateTitle(ConversationUpdateCommand command) {
        command.setUserId(JwtUtil.getTokenInfo().getUserBaseId());
        return conversationInter.updateTitle(command);
    }
}
