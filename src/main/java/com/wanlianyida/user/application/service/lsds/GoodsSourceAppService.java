package com.wanlianyida.user.application.service.lsds;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.crm.api.model.dto.CrmCompanyLineAddressDTO;
import com.wanlianyida.crm.api.model.dto.CrmCustomerDTO;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.framework.lgicommon.entity.TokenInfo;
import com.wanlianyida.framework.lgicore.utils.JwtUtil;
import com.wanlianyida.lsds.api.model.command.GoodsIdCommand;
import com.wanlianyida.lsds.api.model.command.publish.PublishGoodsSourceCommand;
import com.wanlianyida.lsds.api.model.command.publish.PublishOneClickCommand;
import com.wanlianyida.lsds.api.model.command.publish.SplitLineCommand;
import com.wanlianyida.lsds.api.model.dto.*;
import com.wanlianyida.lsds.api.model.query.GoodsSourceListQuery;
import com.wanlianyida.lsds.api.model.query.GoodsSourceQuery;
import com.wanlianyida.platform.api.model.dto.*;
import com.wanlianyida.user.application.assembler.GoodsSourceAssembler;
import com.wanlianyida.user.application.model.command.lsds.publish.GoodsSourcePublishCommand;
import com.wanlianyida.user.application.model.dto.cont.CheckContractSignDTO;
import com.wanlianyida.user.application.model.query.cont.CheckContractSignQuery;
import com.wanlianyida.user.application.service.cont.ContContractAppService;
import com.wanlianyida.user.infrastructure.constant.ParamConst;
import com.wanlianyida.user.infrastructure.enums.*;
import com.wanlianyida.user.infrastructure.exchange.*;
import com.wanlianyida.woa.api.model.dto.WoaLogisticsPlanDTO;
import com.wanlianyida.woa.api.model.dto.WoaOfferPlanDTO;
import com.wanlianyida.woa.api.model.vo.WoaWaybillClueCntVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * 货源 App Service
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class GoodsSourceAppService {

    @Resource
    private LsdsExchangeService lsdsExchangeService;

    @Resource
    private WoaExchangeService woaExchangeService;
    @Resource
    private CrmExchangeService crmExchangeService;

    @Resource
    private UploadExchangeService uploadExchangeService;

    @Resource
    private PlatformExchangeService platformExchangeService;

    @Resource
    private ContContractAppService contContractAppService;

    @Resource
    private ContExchangeService contExchangeService;

    /**
     * 获取货源列表
     */
    public ResultMode<GoodsSourceListDTO> queryPage(PagingInfo<GoodsSourceListQuery> query) {
        //获取公司id
        query.getFilterModel().setCompanyId(JwtUtil.getTokenInfo().getCompanyId());
        ResultMode<GoodsSourceListDTO> resultMode = lsdsExchangeService.queryPage(query);
        if (ObjUtil.isNotNull(resultMode) && resultMode.isSucceed() && CollUtil.isNotEmpty(resultMode.getModel())) {
            List<GoodsSourceListDTO> goodsSourceListDTOS = resultMode.getModel();
            List<String> goodsIds = goodsSourceListDTOS.stream()
                    .filter(goods -> StrUtil.isNotBlank(goods.getGoodsId()))
                    .map(GoodsSourceListDTO::getGoodsId)
                    .collect(Collectors.toList());
            if (!IterUtil.isEmpty(goodsIds)) {
                //推荐运力增值服务数据组装
                List<WoaWaybillClueCntVO> cntList = woaExchangeService.cntByBizIds(goodsIds);
                if (!IterUtil.isEmpty(cntList)) {
                    //cntList按busId转map，按goodsList中货源号匹配cnt
                    Map<String, WoaWaybillClueCntVO> cntMap = cntList.stream()
                            .collect(Collectors.toMap(WoaWaybillClueCntVO::getBusId, Function.identity()));
                    for (GoodsSourceListDTO goodsSourceListDTO : goodsSourceListDTOS) {
                        WoaWaybillClueCntVO cntVO = cntMap.get(goodsSourceListDTO.getGoodsId());
                        if (cntVO == null) {
                            continue;
                        }
                        goodsSourceListDTO.setWoaChannelOrdercnt(cntVO.getCnt());
                    }
                }
            }
        }
        return resultMode;
    }

    /**
     * 发布货源
     */
    public ResultMode publish(GoodsSourcePublishCommand command) {
        try {
            TokenInfo tokenInfo = JwtUtil.getTokenInfo();
            String companyId = tokenInfo.getCompanyId();

            // 校验网货主体
            if (isNetworkMainBodyRequired(command)) {
                return ResultMode.fail("网络货运主体不能为空");
            }

            if (command.getPlanId() == null && StrUtil.equalsAny(command.getBusinessScenario(), BusinessScenarioEnum.YARD_PLAN_DRIVER.getCode(), BusinessScenarioEnum.YARD_PLAN_ENTERPRISE.getCode(), BusinessScenarioEnum.LOGISTICS_PLAN_DRIVER.getCode())) {
                return ResultMode.fail("物流计划单不能为空");
            }

            // 地址校验
            ResultMode addressCheckResult = addressCheck(command);
            if (!addressCheckResult.isSucceed()) {
                return addressCheckResult;
            }
            // 合同校验
            ResultMode<String> contractValidationResult = validateContract(command, companyId);
            if (!contractValidationResult.isSucceed()) {
                return contractValidationResult;
            }
            String contractId = IterUtil.getFirst(contractValidationResult.getModel());


            // 创建线路
            Integer releaseType = BusinessScenarioEnum.businessScenarioMap.get(command.getBusinessScenario()).getReleaseType();
            boolean isTransaction = ObjUtil.equal(releaseType, GoodsReleaseTypeEnum.TRANSFER_TRANSACTION.getType());
            ResultMode lineResult = crmExchangeService.createCrmCompanyLine(
                    companyId, command.getLineId(),
                    command.getSendAddrShortName(), command.getReceiveAddrShortName(),
                    command.getStartLineId(), command.getEndLineId(), isTransaction);
            if (!lineResult.isSucceed()) {
                return lineResult;
            }

            // 获取公司信息
            PlatformUmCompanyDTO company = platformExchangeService.getCompanyByModel(companyId);
            if (company == null) {
                return ResultMode.fail("公司信息不存在");
            }

            //取整类型校验
            if (command.getRoundingMode() == null) {
                String companyRoundMode = platformExchangeService.getCompanyRoundMode(companyId);
                log.info("publish#发布货源取整方式为空，查询企业配置。companyId:{}, companyRoundMode:{}", companyId, companyRoundMode);
                if (StrUtil.isBlank(companyRoundMode)) {
                    //默认向上取整
                    companyRoundMode = "0";
                }
                command.setRoundingMode(Integer.valueOf(companyRoundMode));
            }

            // 获取线路详情
            CrmCompanyLineAddressDTO line = getFirstLineFromResult(lineResult);
            if (line == null) {
                return ResultMode.fail("线路信息获取失败");
            }

            CrmCompanyLineAddressDTO startAddress = crmExchangeService.getCrmCompanyLine(line.getSendAddrId());
            if (startAddress == null) {
                return ResultMode.fail("出发地信息有误");
            }

            CrmCompanyLineAddressDTO endAddress = crmExchangeService.getCrmCompanyLine(line.getReceiveAddrId());
            if (endAddress == null) {
                return ResultMode.fail("目的地信息有误");
            }

            if(StrUtil.equals(startAddress.getStartSendPhoneNumber(), endAddress.getStartSendPhoneNumber())) {
                return ResultMode.fail("出发地目的地联系电话不能一致");
            }

            // 获取客户信息
            CrmCustomerDTO customer = crmExchangeService.getCustomerById(command.getCustomerId());
            if (customer == null) {
                return ResultMode.fail("客户信息有误");
            }

            // 客户校验
            ResultMode customerCheckResult = customerCheck(command, customer, companyId);
            if (!customerCheckResult.isSucceed()) {
                return customerCheckResult;
            }
            // 场站计划单校验
            if (StrUtil.equalsAny(command.getBusinessScenario(), BusinessScenarioEnum.YARD_PLAN_DRIVER.getCode(), BusinessScenarioEnum.YARD_PLAN_ENTERPRISE.getCode())) {
                WoaLogisticsPlanDTO logisticsPlanDTO = woaExchangeService.getLogisticsPlanByPlanId(command.getPlanId());
                if (logisticsPlanDTO == null) {
                    return ResultMode.fail("场站计划单不存在");
                }
            }
            // 物流计划单校验
            if (StrUtil.equalsAny(command.getBusinessScenario(), BusinessScenarioEnum.LOGISTICS_PLAN_DRIVER.getCode())) {
                WoaOfferPlanDTO logisticsPlanDTO = woaExchangeService.getOfferPlanByPlanId(command.getPlanId());
                if (logisticsPlanDTO == null) {
                    return ResultMode.fail("物流计划单不存在");
                }
            }

            //网络货运主体信息
            PlatformCmOperationMainBodyDetailDTO mainBody = platformExchangeService.getMainBodyById(command.getNetworkMainBodyId());

            // 数据组装
            PublishGoodsSourceCommand publishGoodsSourceCommand =
                    GoodsSourceAssembler.createPublishGoodsSourceCommand(command, line, startAddress, endAddress, customer, company, mainBody);

            // 补充司机/企业信息
            String publisherType = command.getPublisherType();
            if (GoodsPublisherTypeEnum.DFQ.getCode().equals(publisherType)) {
                appendDriverCommand(publishGoodsSourceCommand);
            } else if (GoodsPublisherTypeEnum.RFQ.getCode().equals(publisherType)) {
                appendEnterpriseCommand(publishGoodsSourceCommand, companyId);
            }

            // 发布货源
            ResultMode<PublishGoodsSourceDTO> resultMode = lsdsExchangeService.publish(publishGoodsSourceCommand, command.getGoodsId());
            if (!resultMode.isSucceed()) {
                return resultMode;
            }

            // 保存场站计划单
            if (StrUtil.equalsAny(command.getBusinessScenario(), BusinessScenarioEnum.YARD_PLAN_DRIVER.getCode(), BusinessScenarioEnum.YARD_PLAN_ENTERPRISE.getCode())) {
                PublishGoodsSourceDTO dto = CollUtil.getFirst(resultMode.getModel());
                if (dto != null) {
                    woaExchangeService.saveWoaLogisticsPlan(command.getPlanId(), dto.getGoodsId());
                }
            }

            // 保存物流计划单
            if (StrUtil.equalsAny(command.getBusinessScenario(), BusinessScenarioEnum.LOGISTICS_PLAN_DRIVER.getCode())) {
                PublishGoodsSourceDTO dto = CollUtil.getFirst(resultMode.getModel());
                if (dto != null) {
                    woaExchangeService.saveWoaOfferPlan(command.getPlanId(), dto.getGoodsId());
                }
            }

            //同步合同
            if (StrUtil.isNotBlank(contractId)) {
                PublishGoodsSourceDTO dto = CollUtil.getFirst(resultMode.getModel());
                if (dto != null) {
                    contExchangeService.syncLogisticsInfo(contractId, dto.getGoodsId(), null);
                }
            }
            return resultMode;

        } catch (Exception e) {
            log.error("发布货源发生异常", e);
            return ResultMode.fail("系统异常，请稍后重试");
        }
    }

    private boolean isNetworkMainBodyRequired(GoodsSourcePublishCommand command) {
        String bizModelType = BusinessScenarioEnum.businessScenarioMap.get(command.getBusinessScenario()).getBizModelType();
        return StrUtil.equals(bizModelType, BizModelTypeEnum.DFQ.getCode())
                && StrUtil.equals(command.getFreightType(), ParamConst.FREIGHT_TYPE_NETWORK)
                && StrUtil.isEmpty(command.getNetworkMainBodyId());
    }

    private CrmCompanyLineAddressDTO getFirstLineFromResult(ResultMode lineResult) {
        List<?> modelList = lineResult.getModel();
        if (CollUtil.isEmpty(modelList)) {
            return null;
        }
        Object first = modelList.get(0);
        if (!(first instanceof CrmCompanyLineAddressDTO)) {
            return null;
        }
        return (CrmCompanyLineAddressDTO) first;
    }

    /**
     * 合同校验
     *
     * @param command 货源发布命令
     * @param companyId 公司ID
     * @return 校验结果，成功时返回合同ID
     */
    private ResultMode<String> validateContract(GoodsSourcePublishCommand command, String companyId) {
        // 只有司机货源且为网络货运模式才需要校验合同
        if (!StrUtil.equals(command.getPublisherType(), GoodsPublisherTypeEnum.DFQ.getCode())
            || !StrUtil.equals(command.getFreightType(), FreightTypeEnum.NETWORK_MODE.getCode())) {
            return ResultMode.success("");
        }

        try {
            CheckContractSignQuery checkContractSignQuery = new CheckContractSignQuery();
            checkContractSignQuery.setContractType(ContractTypeEnum.FREIGHT.getCode());
            checkContractSignQuery.setContractNature("20");
            checkContractSignQuery.setPartyACompanyId(companyId);
            checkContractSignQuery.setPartyBCompanyId(command.getNetworkMainBodyId());
            checkContractSignQuery.setStartTime(DateUtil.parse(DateUtil.formatDate(command.getReleaseDate()), "yyyy-MM-dd"));
            checkContractSignQuery.setEndTime(DateUtil.parse(DateUtil.formatDate(command.getArriveDate()), "yyyy-MM-dd"));

            ResultMode<CheckContractSignDTO> resultMode = contContractAppService.checkContractSign(checkContractSignQuery);
            if (!resultMode.isSucceed()) {
                return ResultMode.fail(resultMode.getErrCode(), resultMode.getErrMsg());
            }

            CheckContractSignDTO checkContractSignDTO = IterUtil.getFirst(resultMode.getModel());
            String contractId = "";
            if (ObjUtil.isNotNull(checkContractSignDTO) && StrUtil.isNotBlank(checkContractSignDTO.getContractId())) {
                contractId = checkContractSignDTO.getContractId();
            }

            return ResultMode.success(contractId);

        } catch (Exception e) {
            log.error("合同校验异常, companyId: {}, command: {}", companyId, JSONUtil.toJsonStr(command), e);
            return ResultMode.fail("合同校验异常，请稍后重试");
        }
    }

    /**
     * 司机货源客户校验
     */
    private ResultMode customerCheck(GoodsSourcePublishCommand command, CrmCustomerDTO customer, String companyId) {
        if (StrUtil.equals(command.getPublisherType(), GoodsPublisherTypeEnum.RFQ.getCode())) {
            return ResultMode.success();
        }

        PlatformCmOperationMainBodyDTO mainBodyDTO = platformExchangeService.getMainBodyInfoByCompanyId(companyId);
        //发货方是网络货运主体
        if (ObjUtil.isNotNull(mainBodyDTO) && !StrUtil.equals(mainBodyDTO.getType(), "1")) {
            if (ObjUtil.isNotNull(customer) && StrUtil.isBlank(customer.getCustomerCompanyId())) {
                return ResultMode.fail("所选客户对应的企业ID为空，请重新维护客户信息后再试！");
            }
        }

        return ResultMode.success();
    }

    /**
     * 线路、地址校验
     */
    private ResultMode addressCheck(GoodsSourcePublishCommand command) {
        Integer releaseType = BusinessScenarioEnum.businessScenarioMap.get(command.getBusinessScenario()).getReleaseType();
        if (ObjUtil.equal(releaseType, GoodsReleaseTypeEnum.TRANSFER_TRANSACTION.getType()) && StrUtil.isBlank(command.getLineId())) {
            //转交易场景，LineId必传
            return ResultMode.fail("线路ID不能为空!");
        }
        if (StrUtil.isBlank(command.getLineId())) {
            if (StrUtil.isBlank(command.getStartLineId())) {
                return ResultMode.fail("发出地线路ID不能为空!");
            }
            if (StrUtil.isBlank(command.getEndLineId())) {
                return ResultMode.fail("目的地线路ID不能为空!");
            }
        }
        return ResultMode.success();
    }

    /**
     * 保存草稿箱后列表一键发布
     */
    public ResultMode publishOneClick(PublishOneClickCommand command) {
        return lsdsExchangeService.publishOneClick(command);

    }

    /**
     * 企业货源参数
     */
    private void appendEnterpriseCommand(PublishGoodsSourceCommand command, String companyId) {
        if (ObjUtil.isNull(command.getEnterpriseGoods())) {
            return;
        }
        //交易签约主体名称
        if (StrUtil.isNotBlank(command.getEnterpriseGoods().getTransactionContractingBodyId())) {
            PlatformCmOperationMainBodyDTO mainBody = platformExchangeService.getMainBodyInfoByCompanyId(command.getEnterpriseGoods().getTransactionContractingBodyId());
            Optional.ofNullable(mainBody).ifPresent(body -> command.getEnterpriseGoods().setBodyName(body.getBodyName()));
        }
        //地址拆分的需要填充地址信息
        List<SplitLineCommand> goodsSplitList = command.getEnterpriseGoods().getGoodsSplitList();
        if(CollUtil.isNotEmpty(goodsSplitList)){
            for (SplitLineCommand splitLine : goodsSplitList) {
                String sendAddrId = splitLine.getSendAddrId();
                CrmCompanyLineAddressDTO startAddressDTO = crmExchangeService.getCrmCompanyLine(sendAddrId);
                CrmCompanyLineAddressDTO notNullStartLineAddress = Opt.ofNullable(startAddressDTO).orElse(new CrmCompanyLineAddressDTO());

                splitLine.setSendLongitude(notNullStartLineAddress.getItem1());
                splitLine.setSendLatitude(notNullStartLineAddress.getItem2());
                splitLine.setSendLinker(notNullStartLineAddress.getStartSendLinker());
                splitLine.setSendPhoneNumber(notNullStartLineAddress.getStartSendPhoneNumber());

                splitLine.setSendAddrShortName(notNullStartLineAddress.getLineShortName());
                splitLine.setSendAddrProvince(notNullStartLineAddress.getSendAddrProvince());
                splitLine.setSendAddrCity(notNullStartLineAddress.getSendAddrCity());
                splitLine.setSendAddrArea(notNullStartLineAddress.getSendAddrArea());
                splitLine.setSendAddrStreet(notNullStartLineAddress.getSendAddrStreet());
                splitLine.setSendAddrProvinceName(notNullStartLineAddress.getSendAddrProvinceName());
                splitLine.setSendAddrCityName(notNullStartLineAddress.getSendAddrCityName());
                splitLine.setSendAddrAreaName(notNullStartLineAddress.getSendAddrAreaName());
                splitLine.setSendAddrStreetName(notNullStartLineAddress.getSendAddrStreetName());
                splitLine.setSendAddrDetail(notNullStartLineAddress.getSendAddrDetail());
                splitLine.setSendLinker(notNullStartLineAddress.getStartSendLinker());
                splitLine.setSendPhoneNumber(notNullStartLineAddress.getStartSendPhoneNumber());
                splitLine.setSendLongitude(notNullStartLineAddress.getItem1());
                splitLine.setSendLatitude(notNullStartLineAddress.getItem2());


                String receiveAddrId = splitLine.getReceiveAddrId();
                CrmCompanyLineAddressDTO endAddressDTO = crmExchangeService.getCrmCompanyLine(receiveAddrId);
                CrmCompanyLineAddressDTO notNullEndLineAddress = Opt.ofNullable(endAddressDTO).orElse(new CrmCompanyLineAddressDTO());

                //目的地信息
                splitLine.setReceiveAddrShortName(notNullEndLineAddress.getLineShortName());
                splitLine.setReceiveAddrProvince(notNullEndLineAddress.getSendAddrProvince());
                splitLine.setReceiveAddrCity(notNullEndLineAddress.getSendAddrCity());
                splitLine.setReceiveAddrArea(notNullEndLineAddress.getSendAddrArea());
                splitLine.setReceiveAddrStreet(notNullEndLineAddress.getSendAddrStreet());
                splitLine.setReceiveAddrProvinceName(notNullEndLineAddress.getSendAddrProvinceName());
                splitLine.setReceiveAddrCityName(notNullEndLineAddress.getSendAddrCityName());
                splitLine.setReceiveAddrAreaName(notNullEndLineAddress.getSendAddrAreaName());
                splitLine.setReceiveAddrStreetName(notNullEndLineAddress.getSendAddrStreetName());
                splitLine.setReceiveAddrDetail(notNullEndLineAddress.getSendAddrDetail());
                splitLine.setReceiveLinker(notNullEndLineAddress.getStartSendLinker());
                splitLine.setReceivePhoneNumber(notNullEndLineAddress.getStartSendPhoneNumber());
                splitLine.setReceiveLongitude(notNullEndLineAddress.getItem1());
                splitLine.setReceiveLatitude(notNullEndLineAddress.getItem2());
            }
        }

    }

    /**
     * 司机货源参数
     */
    private void appendDriverCommand(PublishGoodsSourceCommand command) {
        if (ObjUtil.isNull(command.getDriverGoods())) {
            return;
        }

        //查询履约保证金额
        if (StrUtil.equals(command.getDriverGoods().getPremiumServStatus(), PremiumServStatusEnum.USE.getCode())) {
            String paramValue = platformExchangeService.getParamValue(ParamConst.GUAR_AMOUNT_PARAM_CODE);
            command.getDriverGoods().setGuarAmount(StrUtil.isBlank(paramValue) ? BigDecimal.ZERO : new BigDecimal(paramValue));
        }

        //司机运费差价
        command.getDriverGoods().setFreightPriceDifferencePercentage(BigDecimal.ZERO);
        command.getDriverGoods().setFreightPriceDifference(BigDecimal.ZERO);
        if (StrUtil.equals(command.getFreightType(), FreightTypeEnum.NETWORK_MODE.getCode())) {
            PlatformUmCompanyMainDTO diffPrice = platformExchangeService.getDiffPrice(command.getNetworkMainBodyId(), JwtUtil.getTokenInfo().getCompanyId());
            Optional.ofNullable(diffPrice).ifPresent(price -> {
                command.getDriverGoods().setFreightPriceDifferencePercentage(price.getDiffPrice());
                command.getDriverGoods().setFreightPriceDifference(price.getDiffPriceRate());
            });
        }
    }

    /**
     * 查询货源详情
     */
    public ResultMode<GoodsSourceDetailDTO> queryDetail(GoodsSourceQuery query) {
        ResultMode<GoodsSourceDetailDTO> resultMode = lsdsExchangeService.queryDetail(query);
        log.info("查询货源详情 resultMode：{}", JSONUtil.toJsonStr(resultMode));
        if (!resultMode.isSucceed() || IterUtil.isEmpty(resultMode.getModel())) {
            return ResultMode.success();
        }

        appendDetailInfo(IterUtil.getFirst(resultMode.getModel()));
        return resultMode;
    }

    /**
     * 货源详情 跨服务数据
     */
    private void appendDetailInfo(GoodsSourceDetailDTO dto) {
        GoodsSourceDTO goodsSource = dto.getGoodsSource();
        //crm查询客户简称
        CrmCustomerDTO customer = crmExchangeService.getCustomerById(goodsSource.getCustomerId());
        Optional.ofNullable(customer).ifPresent(cust -> goodsSource.setCustomerName(customer.getShortName()));
        //crm查询货源线路
//        CrmCompanyLineAddressDTO line = crmExchangeService.getCrmCompanyLine(goodsSource.getLineId());
//        if (line != null) {
//            dto.setLineAddress(BeanUtil.toBean(line, GoodsLineAddressDTO.class));
//            //出发地经纬度
//            CrmCompanyLineAddressDTO sendAddr = crmExchangeService.getCrmCompanyLine(line.getSendAddrId());
//            Optional.ofNullable(sendAddr).ifPresent(addr -> goodsSource.setStartLngLat(addr.getItem1() + "," + addr.getItem2()));
//            //目的地经纬度
//            CrmCompanyLineAddressDTO receiveAddr = crmExchangeService.getCrmCompanyLine(line.getReceiveAddrId());
//            Optional.ofNullable(receiveAddr).ifPresent(addr -> goodsSource.setEndLngLat(addr.getItem1() + "," + addr.getItem2()));
//        }
        //附加url转换
        if (IterUtil.isNotEmpty(dto.getAttachmentList())) {
            List<String> urls = dto.getAttachmentList().stream().map(GoodsAttachmentDTO::getAttachmentUrl).collect(Collectors.toList());
            log.info("urls：{}", JSONUtil.toJsonStr(urls));
            Map<String, String> urlMap = uploadExchangeService.getUrls(urls);
            log.info("urlMap：{}", JSONUtil.toJsonStr(urlMap));
            if (MapUtil.isNotEmpty(urlMap)) {
                dto.getAttachmentList().stream().filter(att -> StrUtil.isNotBlank(att.getAttachmentUrl()))
                        .forEach(att -> att.setAttachmentUrl(urlMap.get(att.getAttachmentUrl())));
            }
        }

        //企业货源
        if (StrUtil.equals(goodsSource.getPublisherType(), GoodsPublisherTypeEnum.RFQ.getCode())) {
            //交易签约主体名称
            if (StrUtil.isNotBlank(goodsSource.getTransactionContractingBodyId())) {
                PlatformCmOperationMainBodyDTO mainBody = platformExchangeService.getMainBodyInfoByCompanyId(goodsSource.getTransactionContractingBodyId());
                Optional.ofNullable(mainBody).ifPresent(body -> goodsSource.setTransactionContractingBodyname(body.getBodyName()));
            }

            //platform查发货企业全称
            if (StrUtil.isNotBlank(goodsSource.getCompanyId())) {
                PlatformUmCompanyDTO platformUmCompanyDTO = platformExchangeService.getCompanyByModel(goodsSource.getCompanyId());
                Optional.ofNullable(platformUmCompanyDTO).ifPresent(company -> goodsSource.setCompanyName(company.getCompanyName()));
            }
        }
    }

    public ResultMode<?> deleteGoods(GoodsIdCommand command) {
        return lsdsExchangeService.deleteGoods(command);
    }

    public ResultMode<?> closeGoods(GoodsIdCommand command) {
        return lsdsExchangeService.closeGoods(command);
    }
}
