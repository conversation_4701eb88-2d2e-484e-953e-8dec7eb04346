package com.wanlianyida.user.application.model.command.oms;

import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 订单推送三方
 **/
@ApiModel(description = "订单推送三方命令对象")
@Data
public class OrderPushThirdCommand extends ExternalBaseRequest {

    @ApiModelProperty(value = "订单号", required = true)
    @NotBlank(message = "订单号不能为空")
    private String orderId;

    @ApiModelProperty(value = "三方网货主体", required = true)
    @NotBlank(message = "三方网货主体不能为空")
    private String networkMainBodyId;

    @ApiModelProperty(value = "三方网货主体名称", required = true)
    @NotBlank(message = "三方网货主体名称不能为空")
    private String networkMainBodyName;

    @ApiModelProperty(value = "不含税运费单价", required = true)
    @NotNull(message = "不含税运费单价不能为空")
    private BigDecimal exclTaxFreightPrice;

    @ApiModelProperty(value = "货物单价", required = true)
    @NotNull(message = "货物单价不能为空")
    private BigDecimal goodsPrice;

    @ApiModelProperty(value = "结算类型[10-延迟付 20-分次结算]", required = true)
    @NotNull(message = "结算类型不能为空")
    private Integer settlementType;

    @ApiModelProperty(value = "结算吨数类型[10-按照原发吨数 20-按照实收吨数 30-按照原发和实收吨数较小的]", required = true)
    @NotNull(message = "结算吨数类型不能为空")
    private Integer settlementWeightType;

    @ApiModelProperty(value = "结算天数", required = true)
    @NotNull(message = "结算天数不能为空")
    private Integer settlementDays;

    @ApiModelProperty(value = "是否尾款结算[0-否 1-是]", required = true)
    @NotNull(message = "是否尾款结算不能为空")
    private Integer isFinalPaymentSettled;

    @ApiModelProperty(value = "尾款结算金额", required = true)
    @NotNull(message = "尾款结算金额不能为空")
    private BigDecimal finalPaymentSettledAmount;

    @ApiModelProperty(value = "途耗类型[10-按重量(单位为kg) 20-按比例(单位为%)]", required = true)
    @NotNull(message = "途耗类型不能为空")
    private Integer transportLossType;

    @ApiModelProperty(value = "途耗数量", required = true)
    @NotNull(message = "途耗数量不能为空")
    private BigDecimal transportLossQuantity;

    @ApiModelProperty(value = "收货抹零类型[10-不抹零 20-个位抹零 30-十位抹零 40-小数抹零 50-五元取整]", required = true)
    @NotNull(message = "收货抹零类型不能为空")
    private Integer roundOffType;

    @ApiModelProperty(value = "固定扣款类型[10-无 20-固定金额 30-固定比例]", required = true)
    @NotNull(message = "固定扣款类型不能为空")
    private Integer fixedDeductionType;

    @ApiModelProperty(value = "固定扣款数量", required = true)
    @NotNull(message = "固定扣款数量不能为空")
    private BigDecimal fixedDeductionQuantity;

    @ApiModelProperty(value = "固定扣款原因", required = true)
    @NotBlank(message = "固定扣款原因不能为空")
    private String fixedDeductionReason;

    @ApiModelProperty(value = "亏吨扣款类型[10-不扣款 20-亏吨即扣款 30-超出指定重量 40-超出原发比例]", required = true)
    @NotNull(message = "亏吨扣款类型不能为空")
    private Integer shortfallDeductionType;

    @ApiModelProperty(value = "亏吨扣款数量", required = true)
    @NotNull(message = "亏吨扣款数量不能为空")
    private BigDecimal shortfallDeductionQuantity;

    @ApiModelProperty(value = "扣款金额保留[10-小数点四舍五入 20-小数点第三位舍弃 30-小数点第四位四舍五入]", required = true)
    @NotNull(message = "扣款金额保留不能为空")
    private Integer deductionPrecisionType;

}
