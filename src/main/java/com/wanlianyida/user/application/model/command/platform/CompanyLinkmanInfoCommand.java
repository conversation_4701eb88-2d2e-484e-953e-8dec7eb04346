package com.wanlianyida.user.application.model.command.platform;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class CompanyLinkmanInfoCommand {

    @NotBlank(message = "联系人名称不能为空")
    private String linkName;

    @NotBlank(message = "联系人电话不能为空")
    private String linkMobile;

    @NotNull(message = "联系人类型不能为空")
    private Integer linkType;

    /**
     * 企业id
     */
    private String companyId;

    /**
     * 创建人id
     */
    private String creatorId;
}
