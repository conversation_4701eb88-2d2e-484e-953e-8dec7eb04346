package com.wanlianyida.user.application.model.query.lsds;

import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OfferListQuery extends ExternalBaseRequest {

    @ApiModelProperty(value = "子货源编号 用于记录及查询显示关联用", name = "childGoodsId", example = "RFQ20201208000003")
    private String childGoodsId;

    @ApiModelProperty(value = "公司(3pL)id", name = "companyId", example = "2")
    private String companyId;

    @ApiModelProperty(value = "公司简称", name = "companyShortName", example = "万联易达")
    private String companyShortName;


    @ApiModelProperty(value = "截止发布时间", name = "endCreateDate", example = "2020-06-16 14:46:02")
    private String endAuditDate;

    @ApiModelProperty(value = "承运商订单号", name = "exOrdersId", example = "")
    private String exOrdersId;

    @ApiModelProperty(value = "货源编号  货源信息表", name = "goodsId", example = "RFQ20201208000006")
    private String goodsId;

    @ApiModelProperty(value = "询价单状态", name = "offerStatus", example = "")
    private Integer offerStatus;

    @ApiModelProperty(value = "目的地简称", name = "receiveAddrShortName", example = "北朝")
    private String receiveAddrShortName;

    @ApiModelProperty(value = "出发地简称", name = "sendAddrShortName", example = "广深")
    private String sendAddrShortName;

    @ApiModelProperty(value = "起始发布时间", name = "startAuditDate", example = "2020-06-16 14:46:02")
    private String startAuditDate;

    @ApiModelProperty(value = "运输类型【checkbox:1-公路集卡,2-公路整车,3-水运,4-铁路,5-多式联运,6-物流项目】", name = "transportationType", example = "150")
    private String transportationType;

    @ApiModelProperty(value = "10 创建时间倒序   20 审核时间降序  30  审核时间升序   40  创建时间降序 ")
    private Integer queryType;


}
