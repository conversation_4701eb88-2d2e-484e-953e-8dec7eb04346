package com.wanlianyida.user.application.model.query.tms;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class OrderListQuery extends ExternalBaseRequest {


    @ApiModelProperty(value = "订单号", name = "orderId")
    private String orderId;

    @ApiModelProperty(value = "是否使用预付费【0-是；1-否】", name = "advancePaymentFlag", example = "10")
    private String advancePaymentFlag;

    @ApiModelProperty(value = "起运地")
    private String sendAddrCity;

    @ApiModelProperty(value = "目的地")
    private String receiveAddrCity;

    @ApiModelProperty(value = "货源编号", name = "goodsId")
    private String goodsId;

    @ApiModelProperty(name = "orderAlias", value = "订单别名")
    private String orderAlias;

    @ApiModelProperty(value = "派车状态：【select:1-待派车,2-派车中,3-已派车,4-追加派车,5-已完成,6-已取消】")
    private String executingStatus;

    @ApiModelProperty(value = "派车状态：【select:1-待派车,2-派车中,3-已派车,4-追加派车,5-已完成,6-已取消】")
    private List<String> executingStatusList;

    @ApiModelProperty(value = "运单下单开始时间", name = "createDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    @ApiModelProperty(value = "运单下单结束时间", name = "createDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endCreateDate;

    @ApiModelProperty("发货方")
    private String shipperName;

}
