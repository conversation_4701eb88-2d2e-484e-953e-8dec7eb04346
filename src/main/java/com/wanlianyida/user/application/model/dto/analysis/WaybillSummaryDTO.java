package com.wanlianyida.user.application.model.dto.analysis;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> @Date 2025/8/26 11:25
 * @Description
 */
@Data
public class WaybillSummaryDTO implements Serializable {



    @ApiModelProperty(value = "运单号",name = "waybillId")
    private String waybillId;

    @ApiModelProperty(value = "货源号",name = "goodsId")
    private String goodsId;

    @ApiModelProperty(value = "订单号",name = "orderId")
    private String orderId;

    @ApiModelProperty(value = "订单吨数【吨】-下单数量",name = "orderQuantity")
    private BigDecimal orderQuantity;

    @ApiModelProperty(value = "托运企业",name = "realityShipperName")
    private String realityShipperName;

    /** 货运类型:1-传统模式(默认)、 2-网络模式 */
    @ApiModelProperty(value = "货运类型:1-传统模式(默认)、 2-网络模式",name = "freightType")
    private String freightType;


    @ApiModelProperty(value = "网络货运主体id",name = "networkMainBodyId")
    private String networkMainBodyId;
    /** 网络货运主体名称 */
    @ApiModelProperty(value = "网络货运主体名称",name = "networkMainBodyName")
    private String networkMainBodyName;

    /** 运输类型：【1-公路集卡(提重还空),2-公路集卡(提空还重),3-公路整车,4-铁路,5-水运,6-公路短倒】 */
    @ApiModelProperty(value = "运输类型：【1-公路集卡(提重还空),2-公路集卡(提空还重),3-公路整车,4-铁路,5-水运,6-公路短倒】",name = "transportType")
    private String transportType;


    @ApiModelProperty(value = "货物名称",name = "goodsName")
    private String goodsName;

    @ApiModelProperty(value = "业务归属",name = "busOwner")
    private String busOwner;

    @ApiModelProperty(value = "订单别名(要求支持模糊查询)", name = "orderAlias")
    private String orderAlias;
    /** 起始省份名称 */
    @ApiModelProperty(value = "出发地-起始省份名称",name = "beginProvinceName")
    private String beginProvinceName;

    /** 起始城市名称 */
    @ApiModelProperty(value = "出发地-起始城市名称",name = "beginCityName")
    private String beginCityName;

    /** 起始县区名称 */
    @ApiModelProperty(value = "出发地-起始县区名称",name = "beginAreaName")
    private String beginAreaName;

    @ApiModelProperty(value = "出发地详细地址",name = "beginAddressDetail")
    private String beginAddressDetail;

    /** 结束省份名称 */
    @ApiModelProperty(value = "目的地-结束省份名称",name = "endProvinceName")
    private String endProvinceName;


    @ApiModelProperty(value = "目的地-结束城市名称",name = "endCityName")
    private String endCityName;

    @ApiModelProperty(value = "目的地-结束县区名称",name = "endAreaName")
    private String endAreaName;


    @ApiModelProperty(value = "目的地详细地址",name = "endAddressDetail")
    private String endAddressDetail;


    @ApiModelProperty(value = "距离(公里)",name = "transportMileage")
    private String transportMileage;


    @ApiModelProperty(value = "运单创建时间",name = "waybillCreateDate")
    private String waybillCreateDate = "--";


    @ApiModelProperty(value = "运单接单时间",name = "orderCreateDate")
    private String orderCreateDate = "--";



    @ApiModelProperty(value = "计价类型(目前只有公路类型才有):【10-重量,20-柜,30-车】",name = "pricingMd")
    private String pricingMd;


    @ApiModelProperty(value = "计价类型(目前只有公路类型才有):【10-重量,20-柜,30-车】",name = "pricingMdCode")
    private String pricingMdCode;


    @ApiModelProperty(value = "运费单价，单位：元/吨",name = "price")
    private BigDecimal price = new BigDecimal(0);

    @ApiModelProperty(value = "下单数量",name = "placeOrderNumber")
    private BigDecimal placeOrderNumber = new BigDecimal(0);


    @ApiModelProperty(value = "车牌号",name = "plateNumber")
    private String plateNumber;


//    @ApiModelProperty(value = "挂车车牌",name = "trailerPlateNumber")
//    private String trailerPlateNumber;


    @ApiModelProperty(value = "车辆类型",name = "carType")
    private String carType = "--";

    @ApiModelProperty(value = "司机姓名",name = "driverName")
    private String driverName;

    @ApiModelProperty(value = "司机身份证号",name = "driverCardId")
    private String driverCardId;

    @ApiModelProperty(value = "司机手机号",name = "driverPhone")
    private String driverPhone;

    @ApiModelProperty(value = "收款人姓名",name = "payeeNames")
    private String payeeNames;

    @ApiModelProperty(value = "收款人开户行",name = "bankOfDeposit")
    private String bankOfDeposit;

    @ApiModelProperty(value = "收款人身份证号",name = "payeeIdCard")
    private String payeeIdCard;

    @ApiModelProperty(value = "收款人银行卡号",name = "payeeBankAccount")
    private String payeeBankAccount;

    @ApiModelProperty(value = "装货时间",name = "zcsj")
    private String zcsj = "--";

    @ApiModelProperty(value = "装货重量(吨)",name = "zcjz")
    private BigDecimal zcjz = new BigDecimal(0);

    @ApiModelProperty(value = "卸车时间",name = "xcsj")
    private String xcsj = "--";

    @ApiModelProperty(value = "卸车重量(吨)",name = "xcjz")
    private BigDecimal xcjz = new BigDecimal(0);

    @ApiModelProperty(value = "签收时间",name = "signTime")
    private String signTime = "--";

    @ApiModelProperty(value = "损耗[吨]",name = "poundDifference")
    private String poundDifference;

    @ApiModelProperty(value = "结算重量(吨)(按重量结算时)",name = "settlementWeight")
    private BigDecimal settlementWeight = new BigDecimal(0);

    @ApiModelProperty(value = "对账状态-【0:未对账,1:已对账】",name = "dzzt")
    private String dzzt = "--";

    @ApiModelProperty(value = "托运人应付运费【元】",name = "yfyf")
    private BigDecimal yfyf = new BigDecimal(0);

    /**
     * 应付运费差价
     */
    @ApiModelProperty(value = "应付留存运费【元】",name = "lcyf")
    private BigDecimal lcyf = new BigDecimal(0);

    /**
     * 运费差价率
     */
    @ApiModelProperty(value = "留存运费率(单位%)",name = "lcyfRate")
    private BigDecimal lcyfRate = new BigDecimal(0) ;


    @ApiModelProperty(value = "应付资金服务费(元)",name = "serviceCost")
    private BigDecimal serviceCost = new BigDecimal(0);

    /** 服务费率 */
    @ApiModelProperty(value = "资金服务费率",name = "serviceRate")
    private BigDecimal serviceRate = new BigDecimal(0);

    @ApiModelProperty(value = "应付司机运费【元】",name = "yfsjyf")
    private BigDecimal yfsjyf = new BigDecimal(0);

    @ApiModelProperty(value = "最新支付时间",name = "lastPayTime")
    private String lastPayTime="--";

    @ApiModelProperty(value = "支付状态：【select:100-待申请,200-待审核,300-待付款,400-审核不通过,500-付款中,600-支付成功,700-支付失败】",name = "payStatus")
    private String payStatus;



    @ApiModelProperty(value = "支付成功时间",name = "paySuccessTime")
    private String paySuccessTime = "--";


    @ApiModelProperty(value = "托运人实付运费",name = "paidAmount")
    private BigDecimal paidAmount = new BigDecimal(0);

    /**
     * 实付运费差价
     */
    @ApiModelProperty(value = "实付留存服务费(元)",name = "actualPaidlcyf")
    private BigDecimal actualPaidlcyf= new BigDecimal(0);

    @ApiModelProperty(value = "实付资金服务费(元)",name = "actualFundServiceAmount")
    private BigDecimal actualFundServiceAmount= new BigDecimal(0);

    @ApiModelProperty(value = "申请开票状态：【select:10待申请,20待审核,30审核不通过,40待开票,50开票成功,60部分开票】",name = "")
    private String kaipiaoStatus = "--";

    // TODO V12.7  运单数据汇总优化需求新增条件字段
    @ApiModelProperty(value = "开票时间", name = "invoicedDate")
    private Date invoicedDate;

    // TODO V12.7  运单数据汇总优化需求新增条件字段
    @ApiModelProperty(value = "开票时间文本", name = "invoicedDateText")
    private String invoicedDateText="--";

    // TODO V12.7  运单数据汇总优化需求新增展示字段
    @ApiModelProperty(value = "",name = "receiptStatus")
    private String receiptStatus;

    @ApiModelProperty(value = "已开发票金额(元)",name = "alreadyInvoiceAmount")
    private BigDecimal alreadyInvoiceAmount = new BigDecimal(0);

    @ApiModelProperty(value = "实付司机运费",name = "paidSjAmount")
    private String paidSjAmount;


    // TODO V12.7  运单数据汇总优化需求新增条件字段
    @ApiModelProperty(value = "是否有预付费(1-无，0-有)", name = "advancePayment")
    private String advancePaymentFlag;

    // TODO V12.7  运单数据汇总优化需求新增条件字段
    @ApiModelProperty(value = "预审状态", name = "preExamStatus")
    private String preExamStatus;

    // TODO V12.7  运单数据汇总优化需求新增条件字段,最后补全
    @ApiModelProperty(value = "保障状态", name = "insureStatus")
    private String guaranteeStatus;


    // TODO V12.7  运单数据汇总优化需求新增条件字段
    @ApiModelProperty(value = "客户名称", name = "customerName")
    private String customerName;

    // TODO V12.7  运单数据汇总优化需求新增条件字段
    @ApiModelProperty(value = "应付货物保障服务费(元)", name = "handleTotalGuaranteeAmount")
    private String handleTotalGuaranteeAmount;

    // TODO V12.7  运单数据汇总优化需求新增条件字段
    @ApiModelProperty(value = "实付货物保障服务费(元)", name = "handleGuaranteeAmount")
    private String handleGuaranteeAmount;

    /**
     * 运费差价率代码
     */
    @ApiModelProperty(value = "运费差价率代码",name = "freightDiffRateCode")
    private String freightDiffRateCode;

    /**
     * 运费差价率成本价
     */
    @ApiModelProperty(value = "运费差价率成本价",name = "costPrice")
    private BigDecimal costPrice;
}
