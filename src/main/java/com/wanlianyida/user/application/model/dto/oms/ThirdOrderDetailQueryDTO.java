package com.wanlianyida.user.application.model.dto.oms;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ThirdOrderDetailQueryDTO {

    @ApiModelProperty(value = "三方订单主键ID")
    private Long id;

    @ApiModelProperty(value = "万联易达订单号")
    private String omsOrderId;

    @ApiModelProperty(value = "三方订单号")
    private String thirdOrderId;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "推送时间")
    private Date pushDate;

    @ApiModelProperty(value = "发货人姓名")
    private String startSendLinker;

    @ApiModelProperty(value = "发货人电话")
    private String startSendPhoneNumber;

    @ApiModelProperty(value = "收货人姓名")
    private String endReceiveLinker;

    @ApiModelProperty(value = "收货人电话")
    private String endReceivePhoneNumber;

    @ApiModelProperty(value = "发货地-省-中文")
    private String sendAddrProvinceName;

    @ApiModelProperty(value = "发货地-市-中文")
    private String sendAddrCityName;

    @ApiModelProperty(value = "发货地-区-中文")
    private String sendAddrAreaName;

    @ApiModelProperty(value = "发货地-详细地址")
    private String sendAddrDetail;

    @ApiModelProperty(value = "收货地-省-中文")
    private String receiveAddrProvinceName;

    @ApiModelProperty(value = "收货地-市-中文")
    private String receiveAddrCityName;

    @ApiModelProperty(value = "收货地-区-中文")
    private String receiveAddrAreaName;

    @ApiModelProperty(value = "收货地-详细地址")
    private String receiveAddrDetail;

    @ApiModelProperty(value = "货物名称")
    private String goodsName;

    @ApiModelProperty(value = "货物类型")
    private String goodsType;

    @ApiModelProperty(value = "货物重量")
    private String goodsWeight;

    @ApiModelProperty(value = "货物单位")
    private String goodsUnit;

    @ApiModelProperty(value = "货物单价")
    private BigDecimal goodsPrice;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "装货开始日期")
    private Date loadingStartDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "装货结束日期")
    private Date loadingEndDate;

    @ApiModelProperty(value = "不含税运费单价")
    private BigDecimal exclTaxFreightPrice;

    @ApiModelProperty(value = "是否尾款结算[0-否 1-是]")
    private Integer isFinalPaymentSettled;

    @ApiModelProperty(value = "尾款结算金额")
    private BigDecimal finalPaymentSettledAmount;

    @ApiModelProperty(value = "收货抹零类型[10-不抹零 20-个位抹零 30-十位抹零 40-小数抹零 50-五元取整]")
    private Integer roundOffType;

    @ApiModelProperty(value = "收货抹零类型描述")
    private String roundOffTypeName;

    @ApiModelProperty(value = "亏吨扣款类型[10-不扣款 20-亏吨即扣款 30-超出指定重量 40-超出原发比例]")
    private String shortfallDeductionType;

    @ApiModelProperty(value = "途耗类型[10-按重量(单位为kg) 20-按比例(单位为%)]")
    private Integer transportLossType;

    @ApiModelProperty(value = "途耗类型描述")
    private String transportLossTypeName;

    @ApiModelProperty(value = "途耗数量")
    private BigDecimal transportLossQuantity;

    @ApiModelProperty(value = "扣款金额保留[10-小数点四舍五入 20-小数点第三位舍弃 30-小数点第四位四舍五入]")
    private Integer deductionPrecisionType;

    @ApiModelProperty(value = "扣款金额保留描述")
    private String deductionPrecisionTypeName;

    @ApiModelProperty(value = "结算类型[10-延迟付 20-分次结算]")
    private Integer settlementType;

    @ApiModelProperty(value = "结算类型描述")
    private String settlementTypeName;

    @ApiModelProperty(value = "结算天数")
    private Integer settlementDays;

    @ApiModelProperty(value = "固定扣款类型[10-无 20-固定金额 30-固定比例]")
    private Integer fixedDeductionType;

    @ApiModelProperty(value = "固定扣款类型描述")
    private String fixedDeductionTypeName;

    @ApiModelProperty(value = "'固定扣款数量'")
    private BigDecimal fixedDeductionQuantity;

    @ApiModelProperty(value = "固定扣款原因")
    private String fixedDeductionReason;

}
