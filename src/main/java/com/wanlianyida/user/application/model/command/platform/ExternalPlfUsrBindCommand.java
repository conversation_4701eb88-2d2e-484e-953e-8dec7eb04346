package com.wanlianyida.user.application.model.command.platform;

import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class ExternalPlfUsrBindCommand extends ExternalBaseRequest {

    @NotBlank(message = "企业员工id不能为空")
    private String companyMemberId;

    /**
     * 平台用户id
     */
    @NotBlank(message = "平台用户id不能为空")
    private String plfUserId;

    /**
     * 平台用户登录id
     */
    @NotBlank(message = "平台用户登录id不能为空")
    private String plfUserLoginId;

    /**
     * 平台用户姓名
     */
    @NotBlank(message = "平台用户姓名不能为空")
    private String plfUserName;

    /**
     * 平台用户账户名
     */
    @NotBlank(message = "平台用户账户名不能为空")
    private String plfUserAccountName;

    /**
     * 平台用户身份证号
     */
    @NotBlank(message = "平台用户身份证号不能为空")
    private String plfUserIdCardNo;

    /**
     * 平台用户手机号
     */
    @NotBlank(message = "平台用户手机号不能为空")
    private String plfUserMobile;


}
