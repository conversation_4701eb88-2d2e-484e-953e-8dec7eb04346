package com.wanlianyida.user.application.model.command.lsds;

import lombok.Data;

import java.io.Serializable;

@Data
public class OfferAddGoodsPlanCommand implements Serializable {

    /**
     * 方案ID
     */
    private String planId;
    /**
     * 标价表ID(关联offer表与split表的ID)
     */
    private String offerSplitId;
    /**
     * 地址ID
     */
    private String goodsAddressId;
    /**
     * 出发地城市ID存储城市code 最终的ID，上级省市向上推
     */
    private String startSiteCityCode;
    /**
     * 出发地址简称(冗余为了前端选择用于显示 用处不大）
     */
    private String startSiteCityName;
    /**
     * 出发地详细地址
     */
    private String startSiteAddress;
    /**
     * 目的地城市ID存储城市code 最终的ID，上级省市向上推
     */
    private String endSiteCityCode;
    /**
     * 出发地址简称(冗余为了前端选择用于显示 用处不大）
     */
    private String endSiteCityName;
    /**
     * 目的地详细地址
     */
    private String endSiteAddress;
    /**
     * 运输类型【checkbox:1-公路集卡,2-公路整车,3-水运,4-铁路,5-多式联运,6-物流项目】
     */
    private String transportationType;
    /**
     * 预计出发时间
     */
    private String expectStartDate;
    /**
     * 预计到达时间
     */
    private String expectEndDate;
    /**
     * 备注
     */
    private String remark;

    /**
     * 扩展字段
     */
    private String ex;

    /**
     * 资源ID(货源ID)
     */
    private String sourceId;
    /**
     * 出发地简称
     */
    private String sendAddrShortName;
    /**
     * 出发地省名称
     */
    private String sendAddrProvinceName;
    /**
     * 出发地市名称
     */
    private String sendAddrCityName;
    /**
     * 出发地区/县名称
     */
    private String sendAddrAreaName;
    /**
     * 出发地乡/镇/街道名称
     */
    private String sendAddrStreetName;
    /**
     * 出发地省code
     */
    private String sendAddrProvince;
    /**
     * 出发地市code
     */
    private String sendAddrCity;
    /**
     * 出发地区/县code
     */
    private String sendAddrArea;
    /**
     * 出发地乡/镇/街道code
     */
    private String sendAddrStreet;
    /**
     * 出发地详细地址
     */
    private String sendAddrDetail;
    /**
     * 出发地联系人
     */
    private String sendLinker;
    /**
     * 出发地联系人联系方式
     */
    private String sendPhoneNumber;
    /**
     * 目的地简称
     */
    private String receiveAddrShortName;
    /**
     * 目的地省
     */
    private String receiveAddrProvinceName;
    /**
     * 目的地市
     */
    private String receiveAddrCityName;
    /**
     * 目的地区/县
     */
    private String receiveAddrAreaName;
    /**
     * 出发地乡/镇/街道名称
     */
    private String receiveAddrStreetName;
    /**
     * 出发地省code
     */
    private String receiveAddrProvince;
    /**
     * 出发地市code
     */
    private String receiveAddrCity;
    /**
     * 出发地区/县code
     */
    private String receiveAddrArea;
    /**
     *
     */
    private String receiveAddrStreet;
    /**
     * 目的详细地址
     */
    private String receiveAddrDetail;
    /**
     * 目的地联系人
     */
    private String receiveLinker;
    /**
     * 目的地联系人联系方式
     */
    private String receivePhoneNumber;
    /**
     * 地址类型【1.货源地址 2.分段地址 3.运输方案地址 4.车源地址】
     */
    private String addressType;

}
