package com.wanlianyida.user.application.model.dto.lsds;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 货源拆单信息
 */
@Data
public class LsdsGoodsSplitDTO {

    /**
     * 货物拆单id
     */
    private String goodsSplitId;

    /**
     * 货源id
     */
    private String goodsId;

    /**
     * 地址Id
     */
    private String goodsAddressId;

    /**
     * 拆单类型-横拆段 --纵拆大小【1-横拆 默认,2-纵拆】
     */
    private Integer splitType;

    /**
     * 出发地城市ID存储城市code
     */
    private String startSiteCityCode;

    /**
     * 出发地址简称
     */
    private String startSiteCityName;

    /**
     * 出发地详细地址
     */
    private String startSiteAddress;

    /**
     * 出发地发货人联系称谓
     */
    private String startSendLinker;

    /**
     * 出发地发货人联系电话
     */
    private String startSendPhoneNumber;

    /**
     * 目的地城市ID存储城市code
     */
    private String endSiteCityCode;

    /**
     * 目的地址简称
     */
    private String endSiteCityName;

    /**
     * 目的地详细地址
     */
    private String endSiteAddress;

    /**
     * 目的地收货人联系称谓
     */
    private String endReceiveLinker;

    /**
     * 目的地收货人联系电话
     */
    private String endReceivePhoneNumber;

    /**
     * 运输类型
     */
    private String transportationType;

    /**
     * 拆单量
     */
    private String splitAmount;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 预计出发时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date releaseDate;

    /**
     * 要求到达时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date arriveDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 节点排序
     */
    private Integer sortNode;

    /**
     * 创建人id
     */
    private String createBy;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 最后更新人id
     */
    private String modifyBy;

    /**
     * 最后更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyDate;

    /**
     * 承运商简称
     */
    private String exCompanyName;

    /**
     * 税率
     */
    private BigDecimal exGoodsRate;

    /**
     * 总数量
     */
    private BigDecimal totalQuantity;

    /**
     * 总数量单位
     */
    private String totalQuantityUnits;

    /**
     * 出发地简称
     */
    private String sendAddrShortName;

    /**
     * 出发地省名称
     */
    private String sendAddrProvinceName;

    /**
     * 出发地市名称
     */
    private String sendAddrCityName;

    /**
     * 出发地区/县名称
     */
    private String sendAddrAreaName;

    /**
     * 出发地详细地址
     */
    private String sendAddrDetail;

    /**
     * 目的地简称
     */
    private String receiveAddrShortName;

    /**
     * 目的地省名称
     */
    private String receiveAddrProvinceName;

    /**
     * 目的地市名称
     */
    private String receiveAddrCityName;

    /**
     * 目的地区/县名称
     */
    private String receiveAddrAreaName;

    /**
     * 目的详细地址
     */
    private String receiveAddrDetail;
}
