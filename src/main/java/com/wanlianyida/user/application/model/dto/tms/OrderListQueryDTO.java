package com.wanlianyida.user.application.model.dto.tms;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wanlianyida.tms.api.model.dto.TmsOrderAddressDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class OrderListQueryDTO {

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号", name = "orderId")
    private String orderId;

    /**
     * 派车状态：【select:1-待派车,2-派车中,3-已派车,4-追加派车,5-已完成,6-已取消】
     */
    @ApiModelProperty(value = "派车状态", name = "executingStatus")
    private String executingStatus;

    /**
     * 起运地市
     */
    @ApiModelProperty(value = "起运地市", name = "sendAddrCity")
    private String sendAddrCity;

    /**
     * 目的地市
     */
    @ApiModelProperty(value = "目的地市", name = "receiveAddrCity")
    private String receiveAddrCity;

    /**
     * 货物名称
     */
    @ApiModelProperty(value = "货物名称", name = "goodsName")
    private String goodsName;

    /**
     * 剩余数量
     */
    @ApiModelProperty(value = "剩余数量", name = "remainingQuantity")
    private BigDecimal remainingQuantity;

    /**
     * 数量单位 10.吨 20.车 30.柜
     */
    @ApiModelProperty(value = "数量单位", name = "orderQuantityUnits")
    private String orderQuantityUnits;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", name = "createDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * POW手工订单地址信息
     */
    @ApiModelProperty(value = "订单地址信息", name = "omsOrderAddress")
    private TmsOrderAddressDTO omsOrderAddress;

    /**
     * 发货方用户的公司企业邀请码
     */
    @ApiModelProperty(value = "发货方企业邀请码", name = "shipperCompanyInvitationCode")
    private String shipperCompanyInvitationCode;

    /**
     * 实际承运方公司Id
     */
    @ApiModelProperty(value = "实际承运方公司Id", name = "realityCarrierCompanyId")
    private String realityCarrierCompanyId;

    /**
     * 车辆类型
     */
    @ApiModelProperty(value = "车辆类型", name = "assignCarType")
    private String assignCarType;

    /**
     * 车长
     */
    @ApiModelProperty(value = "车长", name = "assignCarLength")
    private BigDecimal assignCarLength;

    /**
     * 预计发货时间
     */
    @ApiModelProperty(value = "预计发货时间", name = "releaseDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date releaseDate;

    /**
     * 有效期
     */
    @ApiModelProperty(value = "有效期", name = "validityDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date validityDate;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", name = "otherRemark")
    private String otherRemark;

    /**
     * 询价类型 【select:1-公开询价,2-指定单价】
     */
    @ApiModelProperty(value = "询价类型", name = "enquiryType")
    private String enquiryType;

    /**
     * 订单过期时间
     */
    @ApiModelProperty(value = "订单过期时间", name = "orderExpireDate")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orderExpireDate;

    /**
     * 运输里程
     */
    @ApiModelProperty(value = "运输里程", name = "transportMileage")
    private Integer transportMileage;

    /**
     * 订单是否过期：0未过期，1:已过期
     */
    @ApiModelProperty(value = "订单过期标识", name = "orderIsExpired")
    private Integer orderIsExpired;

    /**
     * 指派类型:10-派车20-派承运商
     */
    @ApiModelProperty(value = "指派类型", name = "sendType")
    private String sendType;

    /**
     * 指定完成状态(11指定完成 21未指定完成)
     */
    @ApiModelProperty(value = "指定完成状态", name = "shippingStatus")
    private String shippingStatus;

    /**
     * 运输类型：【select:110 公路整车,111 公路集卡,112.公路短倒, 120 水运, 130 铁路, 150 多式联运, 160 物流项目】
     */
    @ApiModelProperty(value = "运输类型", name = "transportationType")
    private String transportationType;

    /**
     * 剩余货物数量，派车计算
     */
    @ApiModelProperty(value = "剩余货物数量", name = "surplusGoodsNum")
    private Integer surplusGoodsNum;

    /**
     * 剩余重量：派车计算
     */
    @ApiModelProperty(value = "剩余重量", name = "surplusWeight")
    private BigDecimal surplusWeight;

    /**
     * 订单状态：【select:1-待调度,2-已调度,3-执行中,4-已签收,5-已完成,6-已取消,7-交易中,8-已下单】
     */
    @ApiModelProperty(value = "订单状态", name = "orderStatus")
    private String orderStatus;

    @ApiModelProperty(value = "货源编号", name = "goodsId")
    private String goodsId;

    @ApiModelProperty(value = "发货方名称(公司名称)")
    private String shipperName;
}
