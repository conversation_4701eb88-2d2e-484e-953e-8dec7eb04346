package com.wanlianyida.user.application.model.command.woa;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单推送-成丰
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GaiaOrderDetailCommand {

    /**
     * 货主ID（托运企业ID）
     */
    @NotBlank(message = "货主ID不能为空")
    private String enterpriseId;

    /**
     * 三方订单唯一编号
     */
    @NotBlank(message = "订单编号不能为空")
    private String apiOrderId;

    /**
     * 货物名称（货物中文名称）
     */
    @NotBlank(message = "货物名称不能为空")
    private String cargoType;

    /**
     * 收货人姓名
     */
    @NotBlank(message = "收货人姓名不能为空")
    private String consigneeName;

    /**
     * 收货人电话
     */
    @NotBlank(message = "收货人电话不能为空")
    private String consigneePhone;

    /**
     * 发货人姓名
     */
    @NotBlank(message = "发货人姓名不能为空")
    private String consignerName;

    /**
     * 发货人电话
     */
    @NotBlank(message = "发货人电话不能为空")
    private String consignerPhone;

    /**
     * 途耗类型为按重量时：按千克 按比例时：为数值 系统根据 数值/100 计算
     */
    private BigDecimal consumeRatio;

    /**
     * 途耗类型，0：按重量，1： 按原发比例;
     */
    private Integer consumeRatioType;

    /**
     * 发货地点编码（出发地末级省市区划代码-6位）
     */
    @NotBlank(message = "发货地点编码不能为空")
    private String deliveryAreaCode;

    /**
     * 发货纬度坐标（出发地纬度-高德坐标系）
     */
    @NotNull(message = "发货纬度坐标不能为空")
    private Double deliveryLatitude;

    /**
     * 发货经度坐标（出发地经度-高德坐标系）
     */
    @NotNull(message = "发货经度坐标不能为空")
    private Double deliveryLongitude;

    /**
     * 发货地点（省市区+详细地址，上限150字符）
     */
    @NotBlank(message = "发货地点不能为空")
    private String deliveryPlace;

    /**
     * 收货抹零类型：0：不抹零，1：个位抹零，2：十位抹零，3：小数抹零 4：五元取整(四舍五不入)
     */
    @NotNull(message = "收货抹零类型不能为空")
    private Integer eraseType;

    /**
     * 尾款结算(元)
     */
    private BigDecimal finalSettleAmount;

    /**
     * 固定扣款金额/比例 例如按5%扣款 传5
     */
    private BigDecimal fixedDeduction;

    /**
     * 固定扣款类型 0无 1固定金额 2固定比例
     */
    private Integer fixedDeductionType;

    /**
     * 货物单位（仅支持计价类型为吨或车的订单推送成丰）
     */
    private String forwardingUnit;

    /**
     * 不含税运费单价，元/吨
     */
    @NotNull(message = "单价不能为空")
    private BigDecimal freight;

    /**
     * 0：按照原发吨数，1：按照实收吨数，2：按照原发和实收较小的
     */
    @NotNull(message = "运费吨数类型不能为空")
    private Integer freightTonType;

    /**
     * 货物类型（货物类型-一级（汉字））
     */
    @NotBlank(message = "货物类型不能为空")
    private String goodsType;

    /**
     * 结算单位，0为按吨结算  1 为按车结算 默认按吨
     */
    private Integer invoiceType;

    /**
     * 是否开启自动扣除亏吨扣款金额标识 ,0:未开启,1开启
     */
    private Integer isAutoSubShortTonAmt;

    /**
     * 1-是，0-否；备注：约定固定传1-投保
     */
    private Integer isInsure;

    /**
     * 是否平台单 0：否  1：是
     */
    private Integer isPlatformOrder;

    /**
     * 是否有回单(分次结算场景时，是否需要尾款结算) false:无   true:有  默认 无
     */
    private Boolean isReceipt;

    /**
     * 装货开始日期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date loadDateBegin;

    /**
     * 装货截止日期
     */
    @NotNull(message = "装货截止日期不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date loadDateEnd;

    /**
     * 装车结算(元)
     */
    private BigDecimal loadSettleAmount;

    /**
     * 亏吨扣款，false不扣款，true:扣款
     */
    @NotNull(message = "是否扣款不能为空")
    private Boolean lossCharge;

    /**
     * 按比例计算途耗时亏吨扣款金额保留：0、小数点四舍五入  1、小数点第三位舍弃 2小数点第四位四舍五入
     */
    private Integer lossTonMoneyType;

    /**
     * 备注（订单备注）
     */
    private String memo;

    /**
     * 订单昵称（订单号）
     */
    private String orderNickName;

    /**
     * 定向单结算类型 0：延迟付 1: 分次结算 2：到付 3:预付  默认 延迟付
     */
    private Integer paymentType;

    /**
     * 定向单是否发布到货源列表; 发布:1,不发布:0 默认不发布
     */
    private Integer publishResourceList;

    /**
     * 卸货地点编码
     */
    @NotBlank(message = "卸货地点编码不能为空")
    private String receiveAreaCode;

    /**
     * 卸货地址纬度坐标
     */
    @NotNull(message = "卸货地址纬度坐标不能为空")
    private Double receiveLatitude;

    /**
     * 卸货地址经度坐标
     */
    @NotNull(message = "卸货地址经度坐标不能为空")
    private Double receiveLongitude;

    /**
     * 卸货地点
     */
    @NotBlank(message = "卸货地点不能为空")
    private String receivePlace;

    /**
     * 结算天数，默认60
     */
    private Integer settleDay;

    /**
     * 亏吨选择设置 ,1，不扣款，2.亏吨即扣款 3.超出指定重量 4.超出原发比例
     */
    private Integer shortTonAmtChoiceSet;

    /**
     * 货物单价
     */
    @NotNull(message = "单价不能为空")
    private BigDecimal singlePrice;

    /**
     * 发货简称地址（从常用地址选择发货地址时必传）
     */
    private String slugDeliveryPlace;

    /**
     * 收货简称地址（从常用地址选择收货地址时必传）
     */
    private String slugReceivePlace;

    /**
     * 含税单价状态 0:非含税(默认);1:含税
     */
    private Integer taxFreightFlag;

    /**
     * 货物总量
     */
    @NotNull(message = "货物总量不能为空")
    private BigDecimal totalTon;

    /**
     * 货物总价值(元/每车)
     */
    private BigDecimal totalValueAmount;

    /**
     * 订单类型，1：定向单(经纪人); 2:定向单(货主单);
     */
    private Integer type;

    /**
     * 卸车费
     */
    private BigDecimal unloadFee;

    /**
     * 卸货单位
     */
    private String unloadingCompany;

    /**
     * 卸货结算(元)
     */
    private BigDecimal unLoadSettleAmount;
}
