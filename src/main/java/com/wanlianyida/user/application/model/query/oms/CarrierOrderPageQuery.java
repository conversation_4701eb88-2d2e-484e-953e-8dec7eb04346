package com.wanlianyida.user.application.model.query.oms;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 承运订单列表查询参数
 */
@Data
public class CarrierOrderPageQuery extends ExternalBaseRequest {

    @ApiModelProperty(value = "订单号", name = "orderId")
    private String orderId;

    @ApiModelProperty(value = "订单状态", name = "orderStatus")
    private String orderStatus;

    @ApiModelProperty(value = "运输类型", name = "transportationType")
    private String transportationType;

    @ApiModelProperty(value = "托运方全称", name = "shipperFullName")
    private String shipperFullName;

    @ApiModelProperty(value = "出发地简称", name = "exSendAddrShorthand")
    private String exSendAddrShorthand;

    @ApiModelProperty(value = "目的地简称", name = "exReceiveAddrShorthand")
    private String exReceiveAddrShorthand;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建开始时间", name = "exStartDate")
    private Date exStartDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建结束时间", name = "exEndDate")
    private Date exEndDate;

    @ApiModelProperty(value = "客户名称", name = "customerName")
    private String customerName;

    @ApiModelProperty(value = "订单来源", name = "orderSource")
    private String orderSource;

    @ApiModelProperty(value = "订单类型", name = "orderSourceType")
    private String orderSourceType;

    @ApiModelProperty(value = "客户类型", name = "customerType")
    private String customerType;

    @ApiModelProperty(value = "订单支付状态", name = "orderPayStatus")
    private String orderPayStatus;

    @ApiModelProperty(value = "承运商公司ID", name = "carrierCompanyId")
    private String carrierCompanyId;

}
