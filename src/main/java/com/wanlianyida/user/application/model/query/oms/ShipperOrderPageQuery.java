package com.wanlianyida.user.application.model.query.oms;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 托运订单列表查询参数
 */
@Data
public class ShipperOrderPageQuery extends ExternalBaseRequest {

    @ApiModelProperty(value = "订单号", name = "orderId")
    private String orderId;

    @ApiModelProperty(value = "订单状态", name = "orderStatus")
    private String orderStatus;

    @ApiModelProperty(value = "运输类型", name = "transportationType")
    private String transportationType;

    @ApiModelProperty(value = "承运商全称", name = "carrierFullName")
    private String carrierFullName;

    @ApiModelProperty(value = "出发地简称", name = "exSendAddrShorthand")
    private String exSendAddrShorthand;

    @ApiModelProperty(value = "目的地简称", name = "exReceiveAddrShorthand")
    private String exReceiveAddrShorthand;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建开始时间", name = "exStartDate")
    private Date exStartDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建结束时间", name = "exEndDate")
    private Date exEndDate;

    @ApiModelProperty(value = "发货完成状态", name = "orderShippingStatus")
    private String orderShippingStatus;

    @ApiModelProperty(value = "托运方公司ID", name = "shipperCompanyId")
    private String shipperCompanyId;

}
