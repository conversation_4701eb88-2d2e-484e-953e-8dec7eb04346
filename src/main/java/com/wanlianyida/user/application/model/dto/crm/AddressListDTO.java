package com.wanlianyida.user.application.model.dto.crm;

import lombok.Data;

@Data
public class AddressListDTO {

    /**
     * 线路id
     */
    private String lineId;

    /**
     * 线路简称
     */
    private String lineShortName;

    /**
     * 线路省
     */
    private String sendAddrProvince;

    /**
     * 线路省
     */
    private String sendAddrProvinceName;

    /**
     * 线路市
     */
    private String sendAddrCity;

    /**
     * 线路市
     */
    private String sendAddrCityName;

    /**
     * 线路区
     */
    private String sendAddrArea;

    /**
     * 线路区
     */
    private String sendAddrAreaName;


    /**
     * 线路区
     */
    private String sendAddrStreet;

    /**
     * 线路区
     */
    private String sendAddrStreetName;

    /**
     * 线路详细地址
     */
    private String sendAddrDetail;

    /**
     * 联系人
     */
    private String startSendLinker;

    /**
     * 联系人电话
     */
    private String startSendPhoneNumber;

    /**
     * 是否同步变更订单或者货源 10不同步 20需要同步
     */
    private String changeTag;

    /**
     * 经度
     */
    private String item1;

    /**
     * 纬度
     */
    private String item2;
}
