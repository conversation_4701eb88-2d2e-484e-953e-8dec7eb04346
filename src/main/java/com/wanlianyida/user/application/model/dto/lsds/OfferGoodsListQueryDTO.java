package com.wanlianyida.user.application.model.dto.lsds;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class OfferGoodsListQueryDTO {

    @ApiModelProperty(value = "货源编号", name = "goodsId", example = "RFQ20201208000006")
    private String goodsId;

    @ApiModelProperty(value = "当前报价轮次", name = "offerCurrentRounds", example = "1")
    private Integer offerCurrentRounds;

    @ApiModelProperty(value = "出发地简称", name = "sendAddrShortName", example = "广深")
    private String sendAddrShortName;

    @ApiModelProperty(value = "目的地简称", name = "receiveAddrShortName", example = "北朝")
    private String receiveAddrShortName;

    @ApiModelProperty(value = "货物名称", name = "goodsName", example = "大米")
    private String goodsName;

    @ApiModelProperty(value = "总数量", name = "totalQuantity", example = "100")
    private BigDecimal totalQuantity;

    @ApiModelProperty(value = "总数量单位", name = "totalQuantityUnits", example = "吨")
    private String totalQuantityUnits;

    @ApiModelProperty(value = "总体积（立方米）", name = "volumeSum", example = "50")
    private BigDecimal volumeSum;

    @ApiModelProperty(value = "运输类型【checkbox:1-公路集卡,2-公路整车,3-水运等】", name = "transportationType", example = "150")
    private String transportationType;

    @ApiModelProperty(value = "预计发货时间", name = "releaseDate", example = "2020-08-13 15:56:29")
    private String releaseDate;

    @ApiModelProperty(value = "要求到货时间", name = "arriveDate", example = "2020-09-26 00:00:00")
    private String arriveDate;

    @ApiModelProperty(value = "货源审核时间", name = "auditDate", example = "2020-08-12 14:47:14")
    private String auditDate;

    @ApiModelProperty(value = "货源有效期", name = "validityDate", example = "2020-08-08 00:00:00")
    private String validityDate;

    @ApiModelProperty(value = "公司ID", name = "companyId", example = "2")
    private String companyId;

    @ApiModelProperty(value = "开标状态:10-待开标,20-已开标", name = "bidOpeningStatus", example = "10")
    private String bidOpeningStatus;

    @ApiModelProperty(value = "当前轮次报价剩余分钟数", name = "exOfferLeftTime", example = "120")
    private Long exOfferLeftTime;


    @ApiModelProperty(value = "询价方式【radio:1-公开询价,2-指定单价】", name = "enquiryType", example = "1")
    private String enquiryType;

    @ApiModelProperty(value = "开票价", name = "enquiryTypeBaseOpenTicket", example = "")
    private BigDecimal enquiryTypeBaseOpenTicket;

    @ApiModelProperty(value = "公司简称", name = "companyShortName", example = "万联易达")
    private String companyShortName;

    @ApiModelProperty(value = "是否提供运输方式【checkbox:11-是,21-否】", name = "transportIsOptions", example = "21")
    private String transportIsOptions;
}
