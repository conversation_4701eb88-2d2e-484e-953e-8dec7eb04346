package com.wanlianyida.user.application.model.dto.bi;

import com.wanlianyida.framework.lgicommon.model.dto.ExternalBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年06月16日 16:43
 */
@Data
public class WorkbenchStatisticsDTO extends ExternalBaseDTO {


    @ApiModelProperty("业务信息")
    private BusinessData businessData;

    @ApiModelProperty("异常信息")
    private ExceptionData exceptionData;

    @ApiModelProperty("任务信息")
    private TaskData taskData;

    @ApiModelProperty("签约信息")
    private SignData signData;

    @ApiModelProperty("我的发货")
    private GoodsData goodsData;

    @ApiModelProperty("我的承运")
    private DeliveryData deliveryData;

    @ApiModelProperty("在途运单")
    private TransportWaybillData transportWaybillData;

    @ApiModelProperty("运单审核")
    private WaybillAuditData waybillAuditData;

    @ApiModelProperty("支付开票")
    private PaymentInvoiceData paymentInvoiceData;

    @ApiModelProperty("汇总信息")
    private MetadataDTO collectData;

    public WorkbenchStatisticsDTO() {
        this.businessData = new BusinessData();
        this.exceptionData = new ExceptionData();
        this.taskData = new TaskData();
        this.signData = new SignData();
    }

    /**
     * 业务数据统计
     */
    @Data
    public static class BusinessData {

        @ApiModelProperty("今日发布货源")
        private MetadataDTO issueGoods;

        @ApiModelProperty("进行中订单")
        private MetadataDTO runningOrder;

        @ApiModelProperty("待接单订单")
        private MetadataDTO waitingReceiveOrder;

        @ApiModelProperty("派车中订单")
        private MetadataDTO assigningVehicleOrder;

        @ApiModelProperty("运输中运单")
        private MetadataDTO transportingWaybill;

        @ApiModelProperty("待结算运单")
        private MetadataDTO waitingSettleWaybill;

        @ApiModelProperty("待审核运单")
        private MetadataDTO waitingAuditWaybill;

        @ApiModelProperty("审核未通过运单")
        private MetadataDTO auditRejectWaybill;

        @ApiModelProperty("待支付运单")
        private MetadataDTO waitingPaymentWaybill;

        @ApiModelProperty("待开票运单")
        private MetadataDTO waitingInvoiceWaybill;
    }

    /**
     * 异常统计
     */
    @Data
    public static class ExceptionData {

        @ApiModelProperty("预审不通过")
        private MetadataDTO preAuditReject;

        @ApiModelProperty("超时未卸货")
        private MetadataDTO noUnloadExpire;

        @ApiModelProperty("超时未签收")
        private MetadataDTO noSignExpire;

        @ApiModelProperty("超时未结算")
        private MetadataDTO noSettleExpire;

        @ApiModelProperty("超时未支付")
        private MetadataDTO noPaymentExpire;

        @ApiModelProperty("超时未开票")
        private MetadataDTO noInvoiceExpire;
    }

    /**
     * 任务统计
     */
    @Data
    public static class TaskData {

        @ApiModelProperty("待预审")
        private MetadataDTO waitingPreAudit;

        @ApiModelProperty("付款待审核")
        private MetadataDTO paymentWaitingAudit;

        @ApiModelProperty("开票待审核")
        private MetadataDTO invoiceWaitingAudit;
    }

    /**
     * 签约统计
     */
    @Data
    public static class SignData {

        @ApiModelProperty("新签约供应商")
        private MetadataDTO newShipper;

        @ApiModelProperty("新签约承运商")
        private MetadataDTO newCustomer;

        @ApiModelProperty("新签约司机")
        private MetadataDTO newCarrier;

        @ApiModelProperty("新签约车辆")
        private MetadataDTO newVehicle;
    }

    @Data
    @ApiModel("我的发货")
    public static class GoodsData {

        @ApiModelProperty("发布货源")
        private MetadataDTO issueGoods;

        @ApiModelProperty("传统货源")
        private MetadataDTO traditionalGoods;

        @ApiModelProperty("网络货源")
        private MetadataDTO networkGoods;

        @ApiModelProperty("发布中货源")
        private MetadataDTO issueGoodsIng;
    }

    @Data
    @ApiModel("我的承运")
    public static class DeliveryData {

        @ApiModelProperty("待报价")
        private MetadataDTO waitingQuote;

        @ApiModelProperty("待调度")
        private MetadataDTO waitingDispatch;

        @ApiModelProperty("待派车")
        private MetadataDTO waitingAssign;
    }

    @Data
    @ApiModel("在途运单")
    public static class TransportWaybillData {

        @ApiModelProperty("运单总数")
        private MetadataDTO totalWaybill;

        @ApiModelProperty("待装货")
        private MetadataDTO waitingLoad;

        @ApiModelProperty("待卸货")
        private MetadataDTO waitingUnload;

        @ApiModelProperty("已签收")
        private MetadataDTO sign;
    }

    @Data
    @ApiModel("运单审核")
    public static class WaybillAuditData {

        @ApiModelProperty("待结算")
        private MetadataDTO waitingSettlement;

        @ApiModelProperty("待预审")
        private MetadataDTO waitingPreAudit;

        @ApiModelProperty("待司机对账")
        private MetadataDTO waitingDriverReconciliation;
    }

    @Data
    @ApiModel("支付开票")
    public static class PaymentInvoiceData {

        @ApiModelProperty("待支付")
        private MetadataDTO waitingPayment;

        @ApiModelProperty("待开票")
        private MetadataDTO waitingInvoice;

        @ApiModelProperty("已支付")
        private MetadataDTO alreadyPayment;

        @ApiModelProperty("已开票")
        private MetadataDTO alreadyInvoice;
    }
}
