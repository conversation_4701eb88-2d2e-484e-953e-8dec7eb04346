/**
 * =========================================================
 * ===       此类是由代码工具生成，框架开发者
 * ===       框架开发者Create By: 李健华
 * ===       创建时间: 2019/10/19 18:07:08
 * =========================================================
 */

package com.wanlianyida.user.application.model.dto.platform;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 数据字典表管理
 */
@Data
public class PlatformDictionaryDTO implements Serializable {
    private static final long serialVersionUID = 7744959349390483819L;

    /**
     * 主键 数据字典表管理
     */
    @ApiModelProperty(value = "主键 数据字典表管理", name = "dictionaryId")
    private String dictionaryId;
    /**
     * 数据字典编码值
     */
    @ApiModelProperty(value = "数据字典编码值", name = "enumCode")
    private String enumCode;
    /**
     * 数据字黄英语描述
     */
    @ApiModelProperty(value = "数据字黄英语描述", name = "enName")
    private String enName;
    /**
     * 状态,1:启用 2:未启用
     */
    @ApiModelProperty(value = "状态,1:启用 2:未启用", name = "enable")
    private String enable;
    /**
     * 数据字典描述，名称
     */
    @ApiModelProperty(value = "数据字典描述，名称", name = "name")
    private String name;
    /**
     * 数据字典上级父节点
     */
    @ApiModelProperty(value = "数据字典上级父节点", name = "parentdicId")
    private String parentdicId;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", name = "remark")
    private String remark;
    /**
     * 字典简称
     */
    @ApiModelProperty(value = "字典简称", name = "shortName")
    private String shortName;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序", name = "sort")
    private int sort;
    /**
     * 扩展字段
     */
    @ApiModelProperty(value = "扩展字段", name = "ex")
    private String ex;
    /**
     * 子数据字典集合
     */
    @ApiModelProperty(value = "", name = "children")
    private List<PlatformDictionaryDTO> children;
    /**
     * 层数 固定两层
     * */
    @ApiModelProperty(value = "层数 固定两层", name = "level")
    private int level;
}
