package com.wanlianyida.user.application.model.query.crm;

import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import lombok.Data;

@Data
public class AddressListQuery extends ExternalBaseRequest {


    /**
     * 当前企业
     */
    private String companyId;


    /**
     * 地址类型
     */
    private String lineAddressType;

    /**
     * 简称
     */
    private String lineShortName;

    /**
     * 区
     */
    private String sendAddrArea;

    /**
     * 市
     */
    private String sendAddrCity;

    /**
     * 省份
     */
    private String sendAddrProvince;

    /**
     * 状态
     */
    private String status;
}
