package com.wanlianyida.user.application.model.dto.oms;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class OrderDetailQueryDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 地址表信息(扩展字段)
     */
    @ApiModelProperty(value = "地址表信息(扩展字段)")
    private OmsOrderAddressDTO exAddress;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号", example = "C23FB0D9D7D14941B6BC45D51A4DE16B")
    private String orderId;

    /**
     * 预计发货时间
     */
    @ApiModelProperty(value = "预计发货时间", example = "2020-12-29 11:36:26")
    private String releaseDate;

    /**
     * 预计送达时间
     */
    @ApiModelProperty(value = "预计送达时间", example = "2020-12-29 11:18:04")
    private String arriveDate;

    /**
     * 货源有效时间
     */
    @ApiModelProperty(value = "货源有效时间")
    private String validityDate;

    /**
     * 下单数量
     */
    @ApiModelProperty(value = "下单数量", example = "100.00")
    private BigDecimal orderQuantity;

    /**
     * 下单数量单位
     */
    @ApiModelProperty(value = "下单数量单位", example = "件")
    private String orderQuantityUnits;

    /**
     * 运输类型【1-公路集卡(提重还空),2-公路集卡(提空还重),3-公路整车,4-铁路,5-水运】
     */
    @ApiModelProperty(value = "运输类型", example = "3")
    private String transportType;

    /**
     * 车长，单位米(冗余)
     */
    @ApiModelProperty(value = "车长，单位米(冗余)", example = "150")
    private BigDecimal assignCarLength;

    /**
     * 签回单类型【select:11-不需要签回单,21-原件返回,31-签单返回】
     */
    @ApiModelProperty(value = "签回单类型", example = "11")
    private String otherReceiptType;

    /**
     * 货物类型
     */
    @ApiModelProperty(value = "货物类型", example = "1")
    private String goodsType;

    /**
     * 货物包装规格编码
     */
    @ApiModelProperty(value = "货物包装规格编码", example = "EA406763FF874818A6658FFEAEB8B54C")
    private String packType;

    /**
     * 运输类型【select:110 公路整车,111 公路集卡, 120 水运, 130 铁路, 150 多式联运, 160 物流项目】
     */
    @ApiModelProperty(value = "运输类型", example = "110")
    private String transportationType;

    /**
     * 指定柜型（集装箱类型）
     */
    @ApiModelProperty(value = "指定柜型（集装箱类型）", example = "20GP")
    private String containerType;

    /**
     * 实际承运方用户的公司(组织)Id
     */
    @ApiModelProperty(value = "实际承运方用户的公司(组织)Id", example = "03B875302C8E48D4931341795E866A24")
    private String realityCarrierCompanyId;

    /**
     * ToB业务类型【10：ToC业务，20：ToB业务】
     */
    @ApiModelProperty(value = "ToB业务类型", example = "20")
    private String tobBusiType;

    /**
     * 网络货运主体id(平台运营主体管理表)
     */
    @ApiModelProperty(value = "网络货运主体id", example = "NW001")
    private String networkMainBodyId;

    /**
     * 货运类型【1-传统模式(默认)、 2-网络模式】
     */
    @ApiModelProperty(value = "货运类型", example = "1")
    private String freightType;

    /**
     * 货物名称
     */
    @ApiModelProperty(value = "货物名称", example = "电子产品")
    private String goodsName;

    /**
     * 运输里程
     */
    @ApiModelProperty(value = "运输里程", example = "500")
    private Integer transportMileage;

    /**
     * 询价范围【询价范围8421码-(1-指定物流公司，2-面向物流公司,4-面向司机)】
     */
    @ApiModelProperty(value = "询价范围", example = "2")
    private Integer enquiryRange;

    /**
     * 支付类型【select: 20-月结, 10-现结, 99-其他】
     */
    @ApiModelProperty(value = "支付类型", example = "10")
    private String otherClearType;

    /**
     * 订单基价（不含税率后的价格）
     */
    @ApiModelProperty(value = "订单基价", example = "200.00")
    private BigDecimal enquiryTypeBasePrice;

    /**
     * 订单单价（包含税率后的开票价）
     */
    @ApiModelProperty(value = "订单单价", example = "234.00")
    private BigDecimal enquiryTypeBaseOpenTicket;

    /**
     * 订单总金额
     */
    @ApiModelProperty(value = "订单总金额", example = "10000.00")
    private BigDecimal orderAmount;

    /**
     * 是否使用预付费【0-是；1-否】
     */
    @ApiModelProperty(value = "是否使用预付费", example = "1")
    private String advancePaymentFlag;

    /**
     * 预付款
     */
    @ApiModelProperty(value = "预付款", example = "1000.00")
    private BigDecimal advancePayment;

    /**
     * 剩余货物数量
     */
    @ApiModelProperty(value = "剩余货物数量", example = "50")
    private Integer surplusGoodsNum;

    /**
     * 剩余重量
     */
    @ApiModelProperty(value = "剩余重量", example = "100.00")
    private BigDecimal surplusWeight;

    /**
     * 计费方式【radio:1-按柜,2-按重量】
     */
    @ApiModelProperty(value = "计费方式", example = "2")
    private String chargeType;

    /**
     * 货源编号
     */
    @ApiModelProperty(value = "货源编号", example = "RFQ12345")
    private String goodsId;

    /**
     * 手工订单的亏涨吨信息
     */
    @ApiModelProperty(value = "手工订单的亏涨吨信息")
    private OmsCompensationRulesDTO lsdsGoodsDeductible;

}
