package com.wanlianyida.user.application.model.dto.oms;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OmsOrderAddressDTO {

    @ApiModelProperty(value = "创建人id", name = "createBy")
    private String createBy;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", name = "createDate",example = "2020-12-29 11:18:04")
    private String createDate;
    /**
     * 收货人
     */
    @ApiModelProperty(value = "收货人", name = "endReceiveLinker",example = "收货人")
    private String endReceiveLinker;
    /**
     * 收货人联系方式
     */
    @ApiModelProperty(value = "收货人联系方式", name = "endReceivePhoneNumber",example = "13122223333")
    private String endReceivePhoneNumber;
    /**
     * 预留字段1
     */
    @ApiModelProperty(value = " 预留字段1", name = "item1",example = "预留字段")
    private String item1;
    /**
     * 预留字段2
     */
    @ApiModelProperty(value = " 预留字段2", name = "item2")
    private String item2;
    /**
     * 预留字段3
     */
    @ApiModelProperty(value = " 预留字段3", name = "item3")
    private String item3;
    /**
     * 预留字段4
     */
    @ApiModelProperty(value = " 预留字段4", name = "item4")
    private String item4;
    /**
     * 最后更新人
     */
    @ApiModelProperty(value = "最后更新人", name = "modifyBy")
    private String modifyBy;
    /**
     * 最后修改时间
     */
    @ApiModelProperty(value = "最后修改时间", name = "modifyDate",example = "2020-12-29 11:18:04")
    private String modifyDate;
    /**
     * 订单地址表主键
     */
    @ApiModelProperty(value = "订单地址表主键", name = "orderAddressId")
    private String orderAddressId;
    /**
     * 上门取货时间
     */
    @ApiModelProperty(value = "上门取货时间", name = "pickUpTime",example = "2020-12-29 11:18:04")
    private String pickUpTime;
    /**
     * 目的地（区/县）
     */
    @ApiModelProperty(value = "目的地（区/县） ", name = "receiveAddrArea")
    private String receiveAddrArea;
    /**
     * 目的地（市）
     */
    @ApiModelProperty(value = "目的地（市）", name = "receiveAddrCity")
    private String receiveAddrCity;
    /**
     * 目的详细地址
     */
    @ApiModelProperty(value = "目的详细地址 ", name = "receiveAddrDetail")
    private String receiveAddrDetail;
    /**
     * 收货地址经度
     */
    @ApiModelProperty(value = "收货地址经度", name = "receiveAddrLat")
    private String receiveAddrLat;
    /**
     * 收货地址经度
     */
    @ApiModelProperty(value = "收货地址经度", name = "receiveAddrLng")
    private String receiveAddrLng;
    /**
     * 目的地（省）
     */
    @ApiModelProperty(value = "目的地（省）", name = "receiveAddrProvince")
    private String receiveAddrProvince;
    /**
     * 目的地简称
     */
    @ApiModelProperty(value = "目的地简称", name = "receiveAddrShorthand",example = "目的地简称")
    private String receiveAddrShorthand;
    /**
     * 收货方式：【select:1-送货上门,2-自提,】
     */
    @ApiModelProperty(value = "收货方式：【select:1-送货上门,2-自提,】", name = "receiverWay",example = "2")
    private int receiverWay;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", name = "remark")
    private String remark;
    /**
     * 起运地(区/县)
     */
    @ApiModelProperty(value = "起运地(区/县)", name = "sendAddrArea")
    private String sendAddrArea;
    /**
     * 起运地(市)
     */
    @ApiModelProperty(value = "起运地(市)", name = "sendAddrCity")
    private String sendAddrCity;
    /**
     * 起运详细地址
     */
    @ApiModelProperty(value = "起运详细地址 ", name = "sendAddrDetail")
    private String sendAddrDetail;
    /**
     * 发货地址纬度
     */
    @ApiModelProperty(value = "发货地址纬度", name = "sendAddrLat")
    private String sendAddrLat;
    /**
     * 发货地址经度
     */
    @ApiModelProperty(value = "发货地址经度", name = "sendAddrLng")
    private String sendAddrLng;
    /**
     * 起运地(省)
     */
    @ApiModelProperty(value = "起运地(省)", name = "sendAddrProvince")
    private String sendAddrProvince;
    /**
     * 起运地简称
     */
    @ApiModelProperty(value = "起运地简称", name = "sendAddrShorthand",example = "起运地简称")
    private String sendAddrShorthand;
    /**
     * 发货方式：【select:1-上门取货,2-非上门取货】
     */
    @ApiModelProperty(value = "发货方式：【select:1-上门取货,2-非上门取货】", name = "sendWay",example = "1")
    private int sendWay;
    /**
     * 发货人
     */
    @ApiModelProperty(value = "发货人", name = "startSendLinker",example = "发货人")
    private String startSendLinker;
    /**
     * 发货人联系方式
     */
    @ApiModelProperty(value = "发货人联系方式", name = "startSendPhoneNumber",example = "18912345678")
    private String startSendPhoneNumber;

    /**
     * 起运地(镇)
     */
    @ApiModelProperty(value = "起运地(镇)", name = "sendAddrTown")
    private String sendAddrTown;

    /**
     * 起运地（街道）
     */
    @ApiModelProperty(value = "起运地（街道", name = "sendAddrStreet")
    private String sendAddrStreet;

    /**
     * 目的地（镇）
     */
    @ApiModelProperty(value = "目的地（镇", name = "receiveAddrTown")
    private String receiveAddrTown;

    /**
     * 目的地（街道）
     */
    @ApiModelProperty(value = "目的地（街道", name = "receiveAddrStreet")
    private String receiveAddrStreet;

    //---新增字段

    /** 起运地(省)(中文) */
    @ApiModelProperty(value = "起运地(省)(中文) ", name = "sendAddrProvinceName",example = "广东省")
    private String sendAddrProvinceName;

    /** 起运地(市)(中文) */
    @ApiModelProperty(value = " 起运地(市)(中文)", name = "sendAddrCityName",example = "深圳市")
    private String sendAddrCityName;

    /** 起运地(区/县)(中文) */
    @ApiModelProperty(value = "起运地(区/县)(中文)", name = "sendAddrAreaName",example = "罗湖区")
    private String sendAddrAreaName;

    /** 起运地（街道）(中文) */
    @ApiModelProperty(value = "起运地（街道）(中文)", name = "sendAddrStreetName",example = "中设广场")
    private String sendAddrStreetName;

    /** 目的地(省)(中文) */
    @ApiModelProperty(value = "目的地(省)(中文)", name = "receiveAddrProvinceName",example = "北京市")
    private String receiveAddrProvinceName;

    /** 目的地(市)(中文) */
    @ApiModelProperty(value = "目的地(市)(中文)", name = "receiveAddrCityName",example = "北京市")
    private String receiveAddrCityName;

    /** 目的地(区/县)(中文) */
    @ApiModelProperty(value = "目的地(区/县)(中文)", name = "receiveAddrAreaName",example = "东城区")
    private String receiveAddrAreaName;

    /** 目的地（街道）(中文) */
    @ApiModelProperty(value = "目的地（街道）(中文)", name = "receiveAddrStreetName",example = "长安大街")
    private String receiveAddrStreetName;

    /**
     * 出发地地址id
     */
    private String sendAddrId;

    /**
     * 目的地地址id
     */
    private String receiveAddrId;

    /**
     * 扩展字段
     */
    @ApiModelProperty(value = "扩展字段", name = "ex")
    private String ex;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 只为了新增订单和手工录入订单的传值,不入库
     */
    private String startLineId;
    private String endLineId;

}
