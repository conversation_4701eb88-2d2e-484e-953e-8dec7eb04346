package com.wanlianyida.user.application.model.query.gps;

import lombok.Data;

import java.io.Serializable;

@Data
public class GpsCarMonitViewQuery implements Serializable {
    private static final long serialVersionUID = 4841686994449265867L;

    /*
     * 订单类型  201定位服务  202轨迹服务
     * */
    private Integer orderType;

    /*用户id*/
    private String userId;

    /*用户名称*/
    private String userName;

    /*用户所属机构id*/
    private String companyId;

    /*企业全称*/
    private String companyName;

    /*企业简称*/
    private String companyShortName;

    /*查询方式10按车牌号查询 20按设备号查询*/
    private String queryMode;

    /*车牌号/设备号*/
    private String deviceNo;

    /*查询开始时间*/
    private String startTime;

    /*查询结束时间*/
    private String endTime;

    /**
     * 删除标识 0-否；
     * 1-是；
     */

    private Integer delFlag;
    /*使用状态*/
    private String status;

    private String queryType;

    private String licensePlateNumber;

    private String carPlateColor;

    private String beginDate;

    private String endDate;

    private String invokeType;

}
