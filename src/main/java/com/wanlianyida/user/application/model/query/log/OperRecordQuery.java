package com.wanlianyida.user.application.model.query.log;

import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class OperRecordQuery extends ExternalBaseRequest {

    /**
     * 业务对象id，（司机id、车辆id、商品id）
     */
    @NotBlank(message = "业务id不能为空")
    private String bizId;
    /**
     * 业务对象类型，（司机、车辆、商品）
     */
    @NotBlank(message = "业务类型不能为空")
    private String bizType;
    /**
     * 用户id
     */
    private String userBaseId;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 用户账号
     */
    private String userAccount;
    /**
     * 用户组id，（企业id，部门id）
     */
    private String userGroupId;
    /**
     * 用户组名
     */
    private String userGroupName;
}
