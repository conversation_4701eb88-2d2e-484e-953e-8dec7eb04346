package com.wanlianyida.user.application.model.dto.oms;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class OmsCompensationRulesDTO {

    private int showMsg;

    /**
     * 开关【1：开启 2：关闭】
     */
    private int enable;

    /**
     * 亏涨吨类型
     */
    private int deductibleType;

    /**
     * 免赔(系数/吨数)值
     */
    private BigDecimal deductibleValue;

    /**
     * 货损值
     */
    private BigDecimal goodsLostValue;

    /**
     * 业务ID【货源ID，订单ID】
     */
    private String bizId;

    /**
     * 自动计算标识
     * 0 - 不计算
     * 1 - 计算
     * 来源: V19.9 亏吨自动计算货损
     */
    @ApiModelProperty(value = "自动计算标识", name = "autoCompute")
    private String autoCompute = "0"; //默认值为0，保证存储有效数据

}
