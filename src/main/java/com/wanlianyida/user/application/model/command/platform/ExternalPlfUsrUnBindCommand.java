package com.wanlianyida.user.application.model.command.platform;

import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class ExternalPlfUsrUnBindCommand extends ExternalBaseRequest {


    @NotBlank(message = "企业员工id不能为空")
    private String companyMemberId;

    /**
     * 最后更新人id
     */
    @NotBlank(message = "最后更新人id不能为空")
    private String lastUpdaterId;

    /**
     * 最后更新人姓名
     */
    @NotBlank(message = "最后更新人姓名不能为空")
    private String lastUpdaterName;

}
