package com.wanlianyida.user.application.model.query.tms;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotBlank;

@Data
public class SignWaybillListQuery implements Serializable {

    /**
     * 订单号
     */
    @NotBlank(message = "订单号为空")
    private String orderId;

    /**
     * 结算标志[0-正常结算,1-转移结算,2-终止结算]
     */
    private List<String> transferredList;
}
