package com.wanlianyida.user.application.model.command.qrs;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年08月06日 14:29
 */
@Data
public class IdentifyCodeGoodsBindCommand extends ExternalBaseRequest {

    @ApiModelProperty("业务单号")
    @NotBlank(message = "业务单号不能为空")
    private String bizOrderNo;

    @ApiModelProperty("结算重量枚举")
    private String settlementWeightType;

    @ApiModelProperty("货运类型枚举")
    private String freightType;

    @ApiModelProperty("网络货运主体id")
    private String networkFreightId;

    @ApiModelProperty("0不提醒1提醒")
    private String ifPrompt;

    @ApiModelProperty("提醒内容")
    private String prompt;

    @ApiModelProperty("二维码单价是否脱敏显示10-脱敏显示  20-不脱敏显示")
    private String desensitizeTag;

    @ApiModelProperty("操作企业id（关系建立企业）")
    private String bindCompanyId;

    @ApiModelProperty("询价范围:30-平台司机,50自有司机")
    private String enquiryRange;

    @ApiModelProperty("业务类型（10 货源；20 订单）")
    private String bizOrderType;

    @ApiModelProperty("计划重量/件数")
    private BigDecimal planQuantity;

    @ApiModelProperty("运单单价(司机货源就是发货方开票价)")
    private BigDecimal waybillPrice;

    @ApiModelProperty("计价类型取枚举值")
    private String valuationType;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("价格开始时间")
    private Date priceStartTime;


    private List<BindIdentifyCodeGoods> identifyCodeGoodsList;

    @ApiModelProperty("预付费")
    private AdvancePayment advancePayment;

    private Guarantee guarantee;

    @ApiModelProperty("运费结算时间(单位工作日)")
    private Integer settlementPeriod;

    @Data
    public static class BindIdentifyCodeGoods {

        @ApiModelProperty("业务码")
        private String identifyCode;

        @ApiModelProperty("业务类型（20 企业货源码；30 企业收款码；）")
        private String identifyCodeType;

    }

    @Data
    public static class AdvancePayment {

        @ApiModelProperty("预付费标志")
        private String advancePaymentFlag;

        @ApiModelProperty("预付费金额")
        private BigDecimal advancePayment;
    }

    @Data
    public static class Guarantee {

        @ApiModelProperty("是否购买货物保障服务：1-是；0-否")
        private String buyGuarantee;

        @ApiModelProperty("保障类型：10-定额保险；20-货值保险")
        private String guaranteeType;

        @ApiModelProperty("定额额度（万元）")
        private BigDecimal quota;

        @ApiModelProperty("单位货值")
        private BigDecimal unitValue;

        @ApiModelProperty("被保人（公司全称）")
        private String insuredName;

    }

    @ApiModelProperty("渠道调度电话1")
    private String dispatcherPhone;

    @ApiModelProperty("渠道调度电话2")
    private String dispatcherBakPhone;

}
