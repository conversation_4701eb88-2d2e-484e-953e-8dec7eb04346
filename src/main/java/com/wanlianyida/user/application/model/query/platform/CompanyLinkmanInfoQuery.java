package com.wanlianyida.user.application.model.query.platform;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("联系人")
public class CompanyLinkmanInfoQuery {


    @ApiModelProperty("联系人名称或手机号")
    private String condition;

    @ApiModelProperty("联系人名称")
    private String linkName;

    @ApiModelProperty("联系人电话")
    private String linkMobile;

    @ApiModelProperty("分类，10议价，20装货，30结算，40常用")
    private Integer linkType;

    private String companyId;
}
