package com.wanlianyida.user.application.model.command.qrs;

import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年07月02日 17:19
 */
@Data
public class IdentifyCodeDisplayConfigCommand extends ExternalBaseRequest {

    /**
     * 货源码样式开关 10-开 20-关
     */
    private String configSwitch;

    /**
     * 货源码显示样式 10-详细地址 20-省市区 30-地址简称
     */
    private String configStyle;

    /**
     * 司机端货源码列表标题显示：10货源码、20企业名称
     */
    private String appGoodsListTitle;

    /**
     * 隐藏货源码标志[0-显示,1-隐藏]
     */
    private Integer hideCodeFlag;
}
