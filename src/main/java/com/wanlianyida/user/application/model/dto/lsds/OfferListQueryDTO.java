package com.wanlianyida.user.application.model.dto.lsds;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class OfferListQueryDTO {

    @ApiModelProperty(value = "货源编号", name = "goodsId", example = "RFQ20201208000006")
    private String goodsId;

    @ApiModelProperty(value = "当前最大可报价轮次", name = "exAssRound", example = "3")
    private Integer exAssRound;

    @ApiModelProperty(value = "公司ID", name = "companyId", example = "2")
    private String companyId;

    @ApiModelProperty(value = "报价状态", name = "offerStatus", example = "1")
    private Integer offerStatus;

    @ApiModelProperty(value = "当前轮次报价剩余分钟数", name = "exOfferLeftTime", example = "120")
    private Long exOfferLeftTime;

    @ApiModelProperty(value = "子货源编号", name = "childGoodsId", example = "")
    private String childGoodsId;

    @ApiModelProperty(value = "开标状态:10-待开标,20-已开标", name = "bidOpeningStatus", example = "10")
    private String bidOpeningStatus;

    @ApiModelProperty(value = "询价范围8421码-【checkbox:1-指定物流公司,2-面向物流公司,4-面向司机】", name = "enquiryRange", example = "2")
    private String enquiryRange;

    @ApiModelProperty(value = "当前报价轮次", name = "offerCurrentRounds", example = "1")
    private Integer offerCurrentRounds;

    @ApiModelProperty(value = "报价方是否签约主体:0-否,1-是", name = "contractFlag", example = "0")
    private Integer contractFlag;

    @ApiModelProperty(value = "能否向下游询价:0-不能,1-能询价", name = "downOfferFlag", example = "1")
    private Integer downOfferFlag;

    @ApiModelProperty(value = "货源类型:10-企业货源,20-招标货源", name = "goodsSourceType", example = "10")
    private String goodsSourceType;

    @ApiModelProperty(value = "承运商子订单号", name = "exOrdersId", example = "PO202012080002")
    private String exOrdersId;

    @ApiModelProperty(value = "出发地简称", name = "sendAddrShortName", example = "广深")
    private String sendAddrShortName;

    @ApiModelProperty(value = "目的地简称", name = "receiveAddrShortName", example = "北朝")
    private String receiveAddrShortName;

    @ApiModelProperty(value = "货物名称", name = "goodsName", example = "大米")
    private String goodsName;

    @ApiModelProperty(value = "总数量", name = "totalQuantity", example = "100")
    private BigDecimal totalQuantity;

    @ApiModelProperty(value = "总数量单位", name = "totalQuantityUnits", example = "吨")
    private String totalQuantityUnits;

    @ApiModelProperty(value = "总重量（吨）", name = "weightSum", example = "100")
    private BigDecimal weightSum;

    @ApiModelProperty(value = "总体积（立方米）", name = "volumeSum", example = "50")
    private BigDecimal volumeSum;

    @ApiModelProperty(value = "公司简称", name = "companyShortName", example = "万联易达")
    private String companyShortName;

    @ApiModelProperty(value = "运输类型【checkbox:1-公路集卡,2-公路整车,3-水运等】", name = "transportationType", example = "150")
    private String transportationType;

    @ApiModelProperty(value = "货源审核时间", name = "auditDate", example = "2020-08-12 14:47:14")
    private String auditDate;

    @ApiModelProperty(value = "货源有效期", name = "validityDate", example = "2020-08-08 00:00:00")
    private String validityDate;

    @ApiModelProperty(value = "运输里程", name = "transportMileage", example = "1000")
    private Integer transportMileage;

    @ApiModelProperty(value = "询价方式【radio:1-公开询价,2-指定单价】", name = "enquiryType", example = "1")
    private String enquiryType;

    @ApiModelProperty(value = "开票价", name = "enquiryTypeBaseOpenTicket", example = "")
    private BigDecimal enquiryTypeBaseOpenTicket;

    @ApiModelProperty(value = "是否提供运输方式【checkbox:11-是,21-否】", name = "transportIsOptions", example = "21")
    private String transportIsOptions;

    @ApiModelProperty(value = "交易状态【3:已撤销，4:已过期，5:已成交，6 部成】", name = "dealStatus", example = "5")
    private String dealStatus;
}
