package com.wanlianyida.user.application.model.dto.platform;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PlatformParameterDTO {

    /**
     * 参数编码
     */
    @ApiModelProperty(value = "参数编码", name = "paraCode")
    private String paraCode;
    /**
     * 参数说明
     */
    @ApiModelProperty(value = "参数说明", name = "paraDesc")
    private String paraDesc;
    /**
     * 参数id ( 参数表 )
     */
    @ApiModelProperty(value = "参数id ( 参数表 )", name = "paraId")
    private String paraId;
    /**
     * 参数名称
     */
    @ApiModelProperty(value = "参数名称", name = "paraName")
    private String paraName;
    /**
     * 参数值
     */
    @ApiModelProperty(value = "参数值", name = "paraValue")
    private String paraValue;
    /**
     * 参数值类型
     */
    @ApiModelProperty(value = "参数值类型", name = "paraValueType")
    private String paraValueType;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", name = "remark")
    private String remark;
}
