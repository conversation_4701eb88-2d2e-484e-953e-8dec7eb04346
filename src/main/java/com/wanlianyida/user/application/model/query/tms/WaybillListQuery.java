package com.wanlianyida.user.application.model.query.tms;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class WaybillListQuery extends ExternalBaseRequest {


    @ApiModelProperty(value = "货物编号", name = "goodsId")
    private String goodsId;

    @ApiModelProperty(value = "货物名称", name = "goodsName")
    private String goodsName;

    @ApiModelProperty(value = "订单号", name = "orderId")
    private String orderId;

    @ApiModelProperty(value = "运单号", name = "waybillId")
    private String waybillId;

    @ApiModelProperty(value = "send_type 为1、2时，车牌", name = "plateNumber")
    private String plateNumber;

    @ApiModelProperty(value = "send_type 为1、2时，主司机名称(必须是平台司机)，派车指定多个司机，需要指定一个主司机", name = "driverName")
    private String driverName;

    @ApiModelProperty(value = "联系人、司机", name = "linkmanPhone")
    private String exLinkmanPhone;

    @ApiModelProperty(value = "创建时间", name = "createDateStart")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDateStart;

    @ApiModelProperty(value = "创建时间", name = "createDateEnd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDateEnd;

    @ApiModelProperty(value = "订单别名", name="orderAlias")
    private String orderAlias;

    @ApiModelProperty(value = "起运地市")
    private String sendAddrCity;

    @ApiModelProperty(value = "目的地市")
    private String receiveAddrCity;

    @ApiModelProperty(value = "装货开始时间", name = "loadBeginDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date loadBeginDate;

    @ApiModelProperty(value = "装货结束时间", name = "loadEndDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date loadEndDate;

    @ApiModelProperty(value = "签收开始时间", name = "signBeginDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date signBeginDate;

    @ApiModelProperty(value = "签收结束时间", name = "signEndDate")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date signEndDate;

    @ApiModelProperty(value = "运单状态集合")
    private List<String> wabillStatusList;

    @ApiModelProperty("货主运单状态条件：0、全部 1、待提货 2、运输中 3、已完成")
    private String shipperStatus;

}
