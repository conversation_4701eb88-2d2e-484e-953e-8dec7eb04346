package com.wanlianyida.user.application.model.query.tms;

import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
public class ExterLatestChangeRecordQuery extends ExternalBaseRequest {

    @ApiModelProperty(value = "运单号")
    @NotEmpty(message = "运单号不能为空")
    private String waybillId;

    @ApiModelProperty(value = "审核类型[10-修改单价,20-修改目的地,30-修改出发地]")
    @NotNull(message = "审核类型不能为空")
    private Integer auditType;

}
