package com.wanlianyida.user.application.model.query.bi;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年07月31日 09:21
 */
@Data
public class BiMetadataQuery {

    @ApiModelProperty("用户角色类型，10：托运商，20：承运商，30网货主体")
    private Integer roleType;

    private String startTime;

    private String endTime;

    @ApiModelProperty("统计项[businessData-业务信息,exceptionData-异常信息,taskData-任务信息,signData-签约信息,goodsData-我的发货,deliveryData-我的承运,transportWaybillData-在途运单,waybillAuditData-运单审核,paymentInvoiceData-支付开票,collectData-汇总信息]")
    private List<String> handlerList;
}
