package com.wanlianyida.user.application.model.dto.lsds;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class GoodsOfferDTO {

    /**
     * 对货源报价ID 货源报价 我准备要接单进行承运进行报价。货主（老板）审核评估你的价格OK了再进行下单给你承运.
     */
    @ApiModelProperty(value = "对货源报价ID", name = "offerId", example = "HBJ20201208161616100068")
    private String offerId;

    /**
     * 对货源报价ID数组 货源报价 我准备要接单进行承运进行报价。货主（老板）审核评估你的价格OK了再进行下单给你承运.
     */
    @ApiModelProperty(value = "对货源报价ID数组", name = "offerIdArr")
    private String[] offerIdArr;

    /**
     * 子报价ID
     */
    @ApiModelProperty(value = "对货源报价ID", name = "childOfferId", example = "")
    private String childOfferId;
    /**
     * 货源id
     */
    @ApiModelProperty(value = "货源id", name = "goodsId", example = "RFQ20201208000006")
    private String goodsId;
    /**
     * 报价对应订单ID
     */
    @ApiModelProperty(value = "报价对应订单ID", name = "orderId", example = "")
    private String orderId;
    /**
     * 地址ID
     */
    @ApiModelProperty(value = "地址ID", name = "goodsAddressId", example = "AD20201208161546100065")
    private String goodsAddressId;
    /**
     * 报价轮次
     */
    @ApiModelProperty(value = "报价轮次", name = "offerRound", example = "1")
    private Integer offerRound;
    /**
     * 公司ID 报价人所属公司ID
     */
    @ApiModelProperty(value = "公司ID 报价人所属公司ID", name = "companyId", example = "2")
    private String companyId;
    /**
     * 报价人名称（公司名称或者简称) 同步过来用于显示
     */
    @ApiModelProperty(value = "报价人名称（公司名称或者简称) 同步过来用于显示", name = "companyShrorName", example = "万联易达")
    private String companyShortName;

    /**
     * 报价人名称（公司名称或者简称) 同步过来用于显示
     */
    @ApiModelProperty(value = "报价人名称（公司名称或者简称) 同步过来用于显示", name = "companyName", example = "万联易达")
    private String companyName;
    /**
     * 实际承运商公司ID
     */
    @ApiModelProperty(value = "实际承运商公司ID", name = "realityCarrierCompanyId", example = "47ac2b2384744b749f51d60ce6f5498f")
    private String realityCarrierCompanyId;
    /**
     * 实际承运商公司简称
     */
    @ApiModelProperty(value = "实际承运商公司简称", name = "realityCarrierCompanyShortName", example = "深圳软通动力")
    private String realityCarrierCompanyShortName;
    /**
     * 实际承运商用户ID
     */
    @ApiModelProperty(value = "实际承运商用户ID", name = "realityCarrierUserBaseId", example = "")
    private String realityCarrierUserBaseId;
    /**
     * 运输类型【combox:1-公路集卡,2-公路整车,3-水运,4-铁路】
     */
    @ApiModelProperty(value = "运输类型【combox:1-公路集卡,2-公路整车,3-水运,4-铁路】", name = "transportationType", example = "150")
    private String transportationType;
    /**
     * 基价
     */
    @ApiModelProperty(value = "基价", name = "enquiryTypeBasePrice", example = "1000")
    private BigDecimal enquiryTypeBasePrice;
    /**
     * 税率（%）
     */
    @ApiModelProperty(value = "税率（%）", name = "enquiryTypeBaseTaxRate", example = "9")
    private BigDecimal enquiryTypeBaseTaxRate;
    /**
     * 开票价
     */
    @ApiModelProperty(value = "开票价", name = "enquiryTypeBaseOpenTicket", example = "1090")
    private BigDecimal enquiryTypeBaseOpenTicket;
    /**
     * 实际承运商基价
     */
    @ApiModelProperty(value = "实际承运商基价", name = "realityCarrierEnquiryTypeBasePrice", example = "")
    private BigDecimal realityCarrierEnquiryTypeBasePrice;
    /**
     * 实际承运商开票价
     */
    @ApiModelProperty(value = "实际承运商开票价", name = "realityCarrierEnquiryTypeBaseOpenTicket", example = "")
    private BigDecimal realityCarrierEnquiryTypeBaseOpenTicket;
    /**
     * 实际承运商基价
     */
    @ApiModelProperty(value = "承运方基价", name = "exOffEnquiryTypeBasePrice", example = "")
    private BigDecimal exOffEnquiryTypeBasePrice;
    /**
     * 承运方开票价
     */
    @ApiModelProperty(value = "承运方开票价", name = "exOffEnquiryTypeBaseOpenTicket", example = "")
    private BigDecimal exOffEnquiryTypeBaseOpenTicket;
    /**
     * 报价重量
     */
    @ApiModelProperty(value = "报价重量", name = "offerWeight", example = "")
    private BigDecimal offerWeight;
    /**
     * 状态【radio:1-未中标，2-中标】
     */
    @ApiModelProperty(value = "状态【radio:1-未中标，2-中标】", name = "status", example = "2")
    private Integer status;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", name = "remark", example = "")
    private String remark;
    /**
     * 预计出发时间
     */
    @ApiModelProperty(value = "预计出发时间", name = "releaseDate", example = "2020-12-13 11:19:45")
    private String releaseDate;
    /**
     * 预计到达时间
     */
    @ApiModelProperty(value = "预计到达时间", name = "arriveDate", example = "2020-12-16 00:00:00")
    private String arriveDate;
    /**
     * 节点排序 分段可以直接拿到直接用来排序显示
     */
    @ApiModelProperty(value = "节点排序 分段可以直接拿到直接用来排序显示", name = "sortNode", example = "")
    private Integer sortNode;
    /**
     * 创建人id
     */
    @ApiModelProperty(value = "创建人id", name = "createBy", example = "")
    private String createBy;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", name = "createDate", example = "2020-12-07 22:05:47")
    private String createDate;
    /**
     * 最后更新人id
     */
    @ApiModelProperty(value = "最后更新人id", name = "modifyBy", example = "")
    private String modifyBy;
    /**
     * 最后更新时间
     */
    @ApiModelProperty(value = "最后更新时间", name = "modifyDate", example = "2020-12-07 22:02:35")
    private String modifyDate;

    /**
     * 动态sql
     */
    @ApiModelProperty(value = "动态sql", name = "sqlStr", example = "")
    private String sqlStr;

    /**
     * 运费
     */
    @ApiModelProperty(value = "运费", name = "exFreightPrice", example = "")
    private BigDecimal exFreightPrice;
    /**
     * 扩展字段
     */
    @ApiModelProperty(value = "扩展字段", name = "ex", example = "")
    private String ex;
    /**
     * 资源ID(货源ID)
     */
    @ApiModelProperty(value = "资源ID(货源ID)", name = "sourceId", example = "")
    private String sourceId;
    /**
     * 出发地简称
     */
    @ApiModelProperty(value = "出发地简称", name = "sendAddrShortName", example = "广深")
    private String sendAddrShortName;
    /**
     * 出发地省名称
     */
    @ApiModelProperty(value = "出发地省名称", name = "sendAddrProvinceName", example = "广东省")
    private String sendAddrProvinceName;
    /**
     * 出发地市名称
     */
    @ApiModelProperty(value = "出发地市名称", name = "sendAddrCityName", example = "深圳市")
    private String sendAddrCityName;
    /**
     * 出发地区/县名称
     */
    @ApiModelProperty(value = "出发地区/县名称", name = "sendAddrAreaName", example = "罗湖区")
    private String sendAddrAreaName;
    /**
     * 出发地乡/镇/街道名称
     */
    @ApiModelProperty(value = "出发地乡/镇/街道名称", name = "sendAddrStreetName", example = "")
    private String sendAddrStreetName;
    /**
     * 出发地省code
     */
    @ApiModelProperty(value = "出发地省code", name = "sendAddrProvince", example = "")
    private String sendAddrProvince;
    /**
     * 出发地市code
     */
    @ApiModelProperty(value = "出发地市code", name = "sendAddrCity", example = "")
    private String sendAddrCity;
    /**
     * 出发地区/县code
     */
    @ApiModelProperty(value = "出发地区/县code", name = "sendAddrArea", example = "")
    private String sendAddrArea;
    /**
     * 出发地乡/镇/街道code
     */
    @ApiModelProperty(value = "出发地乡/镇/街道code", name = "sendAddrStreet", example = "")
    private String sendAddrStreet;
    /**
     * 出发地详细地址
     */
    @ApiModelProperty(value = "出发地详细地址", name = "sendAddrDetail", example = "")
    private String sendAddrDetail;
    /**
     * 出发地联系人
     */
    @ApiModelProperty(value = "出发地联系人", name = "sendLinker", example = "张三")
    private String sendLinker;
    /**
     * 出发地联系人联系方式
     */
    @ApiModelProperty(value = "出发地联系人联系方式", name = "sendPhoneNumber", example = "13900000000")
    private String sendPhoneNumber;
    /**
     * 目的地简称
     */
    @ApiModelProperty(value = "目的地简称", name = "receiveAddrShortName", example = "北朝")
    private String receiveAddrShortName;
    /**
     * 目的地省
     */
    @ApiModelProperty(value = "目的地省名称", name = "receiveAddrProvinceName", example = "北京市")
    private String receiveAddrProvinceName;
    /**
     * 目的地市
     */
    @ApiModelProperty(value = "目的地市名称", name = "receiveAddrCityName", example = "北京市")
    private String receiveAddrCityName;
    /**
     * 目的地区/县
     */
    @ApiModelProperty(value = "目的地区/县名称 ", name = "receiveAddrAreaName", example = "朝阳区")
    private String receiveAddrAreaName;
    /**
     * 目的地乡/镇/街道名称
     */
    @ApiModelProperty(value = "目的地乡/镇/街道名称", name = "receiveAddrStreetName", example = "")
    private String receiveAddrStreetName;
    /**
     * 目的地省code
     */
    @ApiModelProperty(value = "目的地省code", name = "receiveAddrProvince", example = "")
    private String receiveAddrProvince;
    /**
     * 目的地市code
     */
    @ApiModelProperty(value = "目的地市code", name = "receiveAddrCity", example = "")
    private String receiveAddrCity;
    /**
     * 目的地区/县code
     */
    @ApiModelProperty(value = "目的地区/县code", name = "receiveAddrArea", example = "")
    private String receiveAddrArea;
    /**
     *目的地乡/镇/街道code
     */
    @ApiModelProperty(value = "目的地乡/镇/街道code", name = "receiveAddrStreet", example = "")
    private String receiveAddrStreet;
    /**
     * 目的详细地址
     */
    @ApiModelProperty(value = "目的详细地址", name = "receiveAddrDetail", example = "")
    private String receiveAddrDetail;
    /**
     * 目的地联系人
     */
    @ApiModelProperty(value = "目的地联系人", name = "receiveLinker", example = "李四")
    private String receiveLinker;
    /**
     * 目的地联系人联系方式
     */
    @ApiModelProperty(value = "目的地联系人联系方式", name = "receivePhoneNumber", example = "13900000000")
    private String receivePhoneNumber;
    /**
     * 地址类型【1.货源地址 2.分段地址 3.运输方案地址 4.车源地址】
     */
    @ApiModelProperty(value = "地址类型【1.货源地址 2.分段地址 3.运输方案地址 4.车源地址】", name = "exAddressType", example = "")
    private String addressType;
    /**
     * 承运商报价是否提供了运输方案【11-是,21-否】
     */
    @ApiModelProperty(value = "承运商报价是否提供了运输方案【11-是,21-否】", name = "hasPlan", example = "21")
    private String hasPlan;

    /**
     * 平台公司id
     */
    @ApiModelProperty(value = "平台公司id ", name = "platformCompanyId", example = "")
    private String  platformCompanyId;


    @ApiModelProperty(value = "重量 ", name = "weightSum", example = "")
    private BigDecimal weightSum;

    @ApiModelProperty(value = "总数量 ", name = "totalQuantity", example = "")
    private BigDecimal totalQuantity;

    @ApiModelProperty(value = "计价类型 ", name = "totalQuantityUnits", example = "")
    private String totalQuantityUnits;

    /**
     * 10 展示 20不展示
     */
    private Integer showWinBids;

    /**
     * 报价状态:1-未报价,2-已报价,3-未成交,4-已成交,5-已撤销
     */
    private String offerStatus;

    /**
     * 开标状态:10-待开标,20-已开标
     */
    private String bidOpeningStatus;

    /**
     * 开标时间
     */
    private Date bidOpeningDate;

    /**
     * 报价方是否签约主体:0-否,1-是
     */
    private Integer contractFlag;

    /**
     * 能否向下游询价:0-不能,1-能询价
     */
    private Integer downOfferFlag;

    /**
     *	实际报价人用户id
     */
    private String priceUserBaseId;

}
