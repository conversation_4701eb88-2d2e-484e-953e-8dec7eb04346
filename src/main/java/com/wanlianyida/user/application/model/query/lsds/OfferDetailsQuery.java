package com.wanlianyida.user.application.model.query.lsds;

import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class OfferDetailsQuery extends ExternalBaseRequest {

    @ApiModelProperty(value = "货源编号", name = "goodsId", example = "RFQ20201208000006")
    @NotBlank(message = "goodsId:不能为空!")
    private String goodsId;

    @ApiModelProperty(value = "公司ID", name = "companyId", example = "2")
    @NotBlank(message = "companyId:不能为空!")
    private String companyId;

    @ApiModelProperty(value = "当前报价轮次", name = "offerCurrentRounds", example = "1")
    @NotNull(message = "offerCurrentRounds:不能为空!")
    private Integer offerCurrentRounds;

}
