package com.wanlianyida.user.application.model.dto.lsds;

import com.wanlianyida.lsds.api.model.dto.BidsFilesFilterDTO;
import com.wanlianyida.lsds.api.model.dto.GoodsDeductibleDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class LsdsGoodsDTO {


    // ------------------------------ 基础信息 ------------------------------
    @ApiModelProperty(value = "订单id-询价后生成的订单id", name = "orderId", example = "POC202010240001-01")
    private String orderId;

    @ApiModelProperty(value = "货源编号", name = "goodsId", example = "RFQ20201208000006")
    private String goodsId;

    @ApiModelProperty(value = "货源图片", name = "goodsPicture")
    private String goodsPicture;

    @ApiModelProperty(value = "是否为货物拆单【11-是,21-否】", name = "goodsIsSplit", example = "21")
    private String goodsIsSplit;

    @ApiModelProperty(value = "公司ID", name = "companyId", example = "2")
    private String companyId;

    @ApiModelProperty(value = "是否提供运输方式【11-是,21-否】", name = "transportIsOptions", example = "21")
    private String transportIsOptions;

    @ApiModelProperty(value = "预计发货时间", name = "releaseDate", example = "2020-08-13 15:56:29")
    private String releaseDate;

    @ApiModelProperty(value = "要求到货时间", name = "arriveDate", example = "2020-09-26 00:00:00")
    private String arriveDate;

    @ApiModelProperty(value = "当前报价轮次", name = "offerCurrentRounds", example = "1")
    private Integer offerCurrentRounds;

    @ApiModelProperty(value = "当前最大可报价轮次", name = "exAssRound", example = "")
    private Integer exAssRound;

    @ApiModelProperty(value = "交易状态【3:已撤销，4:已过期，5:已成交，6 部成】", name = "dealStatus", example = "5")
    private String dealStatus;

    @ApiModelProperty(value = "当前轮次报价剩余分钟数", name = "exOfferLeftTime", example = "")
    private Long exOfferLeftTime;

    @ApiModelProperty(value = "开标状态:10-待开标,20-已开标", name = "bidOpeningStatus")
    private String bidOpeningStatus;

    @ApiModelProperty(value = "基价", name = "enquiryTypeBasePrice", example = "")
    private BigDecimal enquiryTypeBasePrice;

    @ApiModelProperty(value = "税率（%）", name = "enquiryTypeBaseTaxRate", example = "")
    private BigDecimal enquiryTypeBaseTaxRate;

    @ApiModelProperty(value = "开票价", name = "enquiryTypeBaseOpenTicket", example = "")
    private BigDecimal enquiryTypeBaseOpenTicket;

    @ApiModelProperty(value = "询价方式【1-公开询价,2-指定单价】", name = "enquiryType", example = "1")
    private String enquiryType;

    @ApiModelProperty(value = "记录状态【1-草稿,2-待审核,3-发布中,4-审核不通过,5-删除】", name = "recordStatus", example = "1")
    private String recordStatus;

    @ApiModelProperty(value = "有效期", name = "validityDate", example = "2020-08-08 00:00:00")
    private String validityDate;

    @ApiModelProperty(value = "运输要求备注", name = "otherRemark", example = "")
    private String otherRemark;

    @ApiModelProperty(value = "结算方式【20-月结,10-现结,99-其他】", name = "otherClearType", example = "10")
    private String otherClearType;

    @ApiModelProperty(value = "结算重量", name = "otherClearWeight", example = "A00104")
    private String otherClearWeight;

    @ApiModelProperty(value = "合同ID", name = "contractId", example = "")
    private String contractId;

    @ApiModelProperty(value = "柜型结算类型【1-按柜,2-按重量】", name = "chargeType", example = "1")
    private String chargeType;

    @ApiModelProperty(value = "单价", name = "unitPrice", example = "")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "总价", name = "sumPrice", example = "")
    private BigDecimal sumPrice;

    @ApiModelProperty(value = "货源类型:10-企业货源,20-招标货源", name = "goodsSourceType")
    private String goodsSourceType;

    @ApiModelProperty(value = "是否自动生成订单标识", name = "autoPlaceOrderFlag")
    private boolean autoPlaceOrderFlag;

    @ApiModelProperty(value = "货源详情中承运商简称", name = "exCompanyName", example = "")
    private String exCompanyName;

    @ApiModelProperty(value = "当前报价轮次开始时间", name = "currentOfferStartDate", example = "2020-08-12 14:47:14")
    private String currentOfferStartDate;

    @ApiModelProperty(value = "报价有效时间(小时)", name = "enquiryOfferTime", example = "48")
    private Integer enquiryOfferTime;

    @ApiModelProperty(value = "可以报价轮次", name = "offerRound", example = "3")
    private Integer offerRound;

    @ApiModelProperty(value = "地址ID", name = "goodsAddressId", example = "AD20201208161546100065")
    private String goodsAddressId;

    // ------------------------------ 货物信息 ------------------------------
    @ApiModelProperty(value = "货物名称", name = "goodsName", example = "大米")
    private String goodsName;

    @ApiModelProperty(value = "货物描述", name = "goodsDesc", example = "大米")
    private String goodsDesc;

    @ApiModelProperty(value = "总数量", name = "totalQuantity", example = "100")
    private BigDecimal totalQuantity;

    @ApiModelProperty(value = "总数量单位", name = "totalQuantityUnits", example = "件")
    private String totalQuantityUnits;

    @ApiModelProperty(value = "总重量（吨）", name = "weightSum", example = "100")
    private BigDecimal weightSum;

    @ApiModelProperty(value = "总体积（立方米）", name = "volumeSum", example = "50")
    private BigDecimal volumeSum;

    @ApiModelProperty(value = "包装方式", name = "packType", example = "")
    private String packType;

    @ApiModelProperty(value = "亏吨免赔系数对象", name = "lsdsGoodsDeductible")
    private GoodsDeductibleDTO lsdsGoodsDeductible;

    @ApiModelProperty(value = "指定车型", name = "assignCarType", example = "")
    private String assignCarType;

    @ApiModelProperty(value = "指定车长(米）", name = "assignCarLength", example = "")
    private BigDecimal assignCarLength;

    @ApiModelProperty(value = "指定车牌", name = "assignCarPlateNumber", example = "")
    private String assignCarPlateNumber;

    @ApiModelProperty(value = "货柜数量", name = "containerAmount", example = "100")
    private Integer containerAmount;

    @ApiModelProperty(value = "是否需要发票【11-不需要,21-需要】", name = "otherIsInvoice", example = "21")
    private String otherIsInvoice;

    @ApiModelProperty(value = "签回单方式【1-无需回单,2-原件返回,3-签单返回】", name = "otherReceiptType", example = "1")
    private String otherReceiptType;

    // ------------------------------ 地址信息 ------------------------------
    @ApiModelProperty(value = "出发地简称", name = "sendAddrShortName", example = "广深")
    private String sendAddrShortName;

    @ApiModelProperty(value = "目的地简称", name = "receiveAddrShortName", example = "北朝")
    private String receiveAddrShortName;

    @ApiModelProperty(value = "出发地省名称", name = "sendAddrProvinceName", example = "广东省")
    private String sendAddrProvinceName;

    @ApiModelProperty(value = "出发地市名称", name = "sendAddrCityName", example = "深圳市")
    private String sendAddrCityName;

    @ApiModelProperty(value = "出发地区/县名称", name = "sendAddrAreaName", example = "罗湖区")
    private String sendAddrAreaName;

    @ApiModelProperty(value = "出发地乡/镇/街道名称", name = "sendAddrStreetName", example = "")
    private String sendAddrStreetName;

    @ApiModelProperty(value = "出发地省code", name = "sendAddrProvince", example = "")
    private String sendAddrProvince;

    @ApiModelProperty(value = "出发地市code", name = "sendAddrCity", example = "")
    private String sendAddrCity;

    @ApiModelProperty(value = "出发地区/县code", name = "sendAddrArea", example = "")
    private String sendAddrArea;

    @ApiModelProperty(value = "出发地乡/镇/街道code", name = "sendAddrStreet", example = "")
    private String sendAddrStreet;

    @ApiModelProperty(value = "出发地详细地址", name = "sendAddrDetail", example = "")
    private String sendAddrDetail;

    @ApiModelProperty(value = "出发地联系人", name = "sendLinker", example = "张三")
    private String sendLinker;

    @ApiModelProperty(value = "出发地联系人联系方式", name = "sendPhoneNumber", example = "13900000000")
    private String sendPhoneNumber;

    @ApiModelProperty(value = "目的地省名称", name = "receiveAddrProvinceName", example = "北京市")
    private String receiveAddrProvinceName;

    @ApiModelProperty(value = "目的地市名称", name = "receiveAddrCityName", example = "北京市")
    private String receiveAddrCityName;

    @ApiModelProperty(value = "目的地区/县名称", name = "receiveAddrAreaName", example = "朝阳区")
    private String receiveAddrAreaName;

    @ApiModelProperty(value = "目的地乡/镇/街道名称", name = "receiveAddrStreetName", example = "")
    private String receiveAddrStreetName;

    @ApiModelProperty(value = "目的地省code", name = "receiveAddrProvince", example = "")
    private String receiveAddrProvince;

    @ApiModelProperty(value = "目的地市code", name = "receiveAddrCity", example = "")
    private String receiveAddrCity;

    @ApiModelProperty(value = "目的地区/县code", name = "receiveAddrArea", example = "")
    private String receiveAddrArea;

    @ApiModelProperty(value = "目的地乡/镇/街道code", name = "receiveAddrStreet", example = "")
    private String receiveAddrStreet;

    @ApiModelProperty(value = "目的详细地址", name = "receiveAddrDetail", example = "")
    private String receiveAddrDetail;

    @ApiModelProperty(value = "目的地联系人", name = "receiveLinker", example = "李四")
    private String receiveLinker;

    @ApiModelProperty(value = "目的地联系人联系方式", name = "receivePhoneNumber", example = "13900000000")
    private String receivePhoneNumber;

    // ------------------------------ 扩展信息 ------------------------------
    @ApiModelProperty(value = "货源生成订单后返回的母订单号", name = "parentOrderId", example = "POC202012080002")
    private String parentOrderId;

    @ApiModelProperty(value = "招标文件", name = "bidsFiles")
    private List<BidsFilesFilterDTO> bidsFiles;

    @ApiModelProperty(value = "出发地城市编码", name = "startSiteCityCode", example = "440300")
    private String startSiteCityCode;

    @ApiModelProperty(value = "发货方公司ID", name = "shipperCompanyId", example = "1001")
    private String shipperCompanyId;

    @ApiModelProperty(value = "网络货运主体id(平台运营主体管理表)")
    private String networkMainBodyId;

}
