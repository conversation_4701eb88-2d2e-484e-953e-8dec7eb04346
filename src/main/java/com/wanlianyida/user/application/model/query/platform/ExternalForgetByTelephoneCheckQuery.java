package com.wanlianyida.user.application.model.query.platform;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class ExternalForgetByTelephoneCheckQuery extends ExternalCompanyMemberQuery{

    @ApiModelProperty(value = "手机号", name = "telephone")
    @NotEmpty(message = "手机号不能为空")
    private String telephone;

    @ApiModelProperty(value = "图形验证码", name = "graphValidateCode")
    @NotEmpty(message = "图形验证码不能为空")
    private String graphValidateCode;

    @ApiModelProperty(value = "短信验证码", name = "messageValidateCode")
    @NotEmpty(message = "短信验证码不能为空")
    private String messageValidateCode;

    @ApiModelProperty(value = "扩展字段", name = "ex")
    @NotEmpty(message = "扩展字段不能为空")
    private String ex;

}
