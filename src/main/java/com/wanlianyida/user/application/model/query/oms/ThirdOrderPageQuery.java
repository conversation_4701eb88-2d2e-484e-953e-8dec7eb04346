package com.wanlianyida.user.application.model.query.oms;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 三方订单列表查询参数
 */
@Data
public class ThirdOrderPageQuery extends ExternalBaseRequest {

    @ApiModelProperty(value = "订单号", name = "orderId")
    private String orderId;

    @ApiModelProperty(value = "三方网货主体", name = "networkMainBodyId")
    private String networkMainBodyId;

    @ApiModelProperty(value = "三方网货主体名称", name = "networkMainBodyName")
    private String networkMainBodyName;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "推送开始时间", name = "pushStartDate")
    private Date pushStartDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "推送结束时间", name = "pushEndDate")
    private Date pushEndDate;

    @ApiModelProperty(value = "三方订单号", name = "thirdOrderId")
    private String thirdOrderId;

    @ApiModelProperty(value = "出发地", name = "sendAddrShorthand")
    private String sendAddrShorthand;

    @ApiModelProperty(value = "目的地", name = "receiveAddrShorthand")
    private String receiveAddrShorthand;

}
