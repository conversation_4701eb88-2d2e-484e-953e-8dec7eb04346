package com.wanlianyida.user.application.model.command.tcs;

import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotBlank;

@Data
public class TcsAddPartnerCommand extends ExternalBaseRequest implements Serializable {

    private static final long serialVersionUID = -1675312222705475990L;


    @NotBlank(message = "来源标识不能为空")
    @ApiModelProperty(value = "设计服务伙伴来源标识",example = "1：代报价,2：报价,2：转调度")
    private String partnerSetPageTag;

    @ApiModelProperty(value = "业务归属公司")
    private String bizCompanyId;

    @ApiModelProperty(value = "业务归属公司name")
    private String bizCompanyName;

    @ApiModelProperty(value = "货源号")
    private String goodsId;

    @ApiModelProperty(value = "货品名称")
    private String goodsName;

    @ApiModelProperty(value = "订单号")
    private String orderId;

    @ApiModelProperty(value = "实际承运企业")
    private String carrierCompanyId;

    @ApiModelProperty(value = "运力伙伴")
    private List<TcsPartnerInfoCommand> choosePartnerInfoList;
}
