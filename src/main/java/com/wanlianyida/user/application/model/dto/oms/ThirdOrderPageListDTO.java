package com.wanlianyida.user.application.model.dto.oms;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 三方订单列表
 */
@Data
public class ThirdOrderPageListDTO {

    @ApiModelProperty(value = "三方订单主键ID")
    private Long id;

    @ApiModelProperty(value = "万联易达订单号")
    private String omsOrderId;

    @ApiModelProperty(value = "三方订单号")
    private String thirdOrderId;

    @ApiModelProperty(value = "三方网货主体")
    private String networkMainBodyId;

    @ApiModelProperty(value = "三方网货主体名称")
    private String networkMainBodyName;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "推送时间")
    private Date pushDate;

    @ApiModelProperty(value = "货物名称")
    private String goodsName;

    @ApiModelProperty(value = "货物总量")
    private BigDecimal orderQuantity;

    @ApiModelProperty(value = "货物总量单位")
    private String orderQuantityUnit;

    @ApiModelProperty(value = "不含税运费单价")
    private BigDecimal exclTaxFreightPrice;

    @ApiModelProperty(value = "发货地-省")
    private String sendAddrProvince;

    @ApiModelProperty(value = "发货地-市")
    private String sendAddrCity;

    @ApiModelProperty(value = "发货地-区")
    private String sendAddrArea;

    @ApiModelProperty(value = "发货地-省-中文")
    private String sendAddrProvinceName;

    @ApiModelProperty(value = "发货地-市-中文")
    private String sendAddrCityName;

    @ApiModelProperty(value = "发货地-区-中文")
    private String sendAddrAreaName;

    @ApiModelProperty(value = "收货地-省")
    private String receiveAddrProvince;

    @ApiModelProperty(value = "收货地-市")
    private String receiveAddrCity;

    @ApiModelProperty(value = "收货地-区")
    private String receiveAddrArea;

    @ApiModelProperty(value = "收货地-省-中文")
    private String receiveAddrProvinceName;

    @ApiModelProperty(value = "收货地-市-中文")
    private String receiveAddrCityName;

    @ApiModelProperty(value = "收货地-区-中文")
    private String receiveAddrAreaName;

    @ApiModelProperty(value = "发货人姓名")
    private String startSendLinker;

    @ApiModelProperty(value = "收货人姓名")
    private String endReceiveLinker;

    @ApiModelProperty(value = "结算类型[10-延迟付 20-分次结算]")
    private Integer settlementType;

    @ApiModelProperty(value = "结算类型描述")
    private Integer settlementTypeName;

}
