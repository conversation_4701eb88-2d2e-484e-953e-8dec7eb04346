package com.wanlianyida.user.application.model.command.lsds.publish;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wanlianyida.lsds.api.model.command.publish.AssignSupplierCommand;
import com.wanlianyida.lsds.api.model.command.publish.AttachmentCommand;
import com.wanlianyida.lsds.api.model.command.publish.LossDeductibleCommand;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 发布货源 Command
 */
@Data
public class GoodsSourcePublishBaseCommand {
    /**
     * -----------------------------------关键信息---------------------------------------
     */
    /**
     * 货源编号
     */
    private String goodsId;
    /**
     * 客户端平台【1:3PL,2:货主App端】
     */
    @NotNull(message = "客户端平台不能为空")
    private Integer clientPlatform;
    /**
     * 发布主体类型 [10-司机货源 20-企业货源]
     */
    @NotBlank(message = "发布主体类型不能为空")
    private String publisherType;
    /**
     * 业务场景[100-司机货源,110-撮合管理,120-网络货运-司机货源,130-撮合发货-司机货源,140-物流计划单-司机货源,150-场站计划单-司机货源,160-货主端-司机货源,
     * 200-企业货源,210-招标管理,220-撮合发货-企业货源,221-撮合发货-招标货源,230-场站计划单-企业货源,240-转交易,250-下游询价,260-商贸询价单,270-货主端-企业货源,271-货主端-招标货源]
     */
    @NotBlank(message = "业务场景不能为空")
    private String businessScenario;
    /**
     * 操作类型 [10-保存草稿箱 20-提交]
     */
    @NotBlank(message = "操作类型不能为空")
    private String buttonType;

    /**
     * 货运类型 [1-传统模式, 2-网络模式]
     */
    private String freightType;

    /**
     * 网络货运主体ID
     */
    private String networkMainBodyId;

    /**
     * -----------------------------------发货信息---------------------------------------
     */
    /**
     * 线路id
     */
    private String lineId;
    /**
     * 出发地地址id
     */
    private String startLineId;
    /**
     * 出发地简称
     */
    @NotBlank(message = "出发地简称不能为空")
    private String sendAddrShortName;
    /**
     * 目的地地址id
     */
    private String endLineId;
    /**
     * 目的地简称
     */
    @NotBlank(message = "目的地简称不能为空")
    private String receiveAddrShortName;
    /**
     * 要求发货时间
     */
    @NotNull(message = "要求发货时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date releaseDate;
    /**
     * 要求到货时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date arriveDate;

    /**
     * -----------------------------------货物信息---------------------------------------
     */
    /**
     * 货物名称
     */
    @NotBlank(message = "货物名称不能为空")
    private String goodsName;
    /**
     * 货物类型
     */
    private String goodsType;
    /**
     * 货物描述
     */
    private String goodsDesc;
    /**
     * 包装方式:dict:3
     */
    private String packType;
    /**
     * 总数量
     */
    @NotNull(message = "总数量不能为空")
    private BigDecimal totalQuantity;
    /**
     * 总数量单位 [10-吨,20-柜,30-车]
     */
    @NotBlank(message = "总数量单位不能为空")
    private String totalQuantityUnits;
    /**
     * 总重量（吨）
     */
    private BigDecimal weightSum;
    /**
     * 总体积（立方米）
     */
    private BigDecimal volumeSum;
    /**
     * 总件数（件）
     */
    private Integer totalGoods;
    /**
     * -----------------------------------运输信息---------------------------------------
     */
    /**
     * 运输类型 [110-公路整车, 111-公路集卡,112-公路短倒, 120-水运, 130-铁路, 150-多式联运, 160-物流项目,170-整车短倒]
     */
    @NotBlank(message = "运输类型不能为空")
    private String transportationType;
    /**
     * 询价范围 [1-邀请承运商 2-全部承运商 30-平台司机 40-指定司机 50-自有司机]
     */
    @NotBlank(message = "询价范围不能为空")
    private String enquiryRange;
    /**
     * 货源指定供应商或司机列表
     */
    private List<AssignSupplierCommand> assignSupplierList;
    /**
     * 货源有效期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date validityDate;
    /**
     * 指定车型
     */
    private String assignCarType;
    /**
     * 指定车长
     */
    private BigDecimal assignCarLength;
    /**
     * 指定车牌
     */
    private String assignCarPlateNumber;
    /**
     * 指定柜型
     */
    private String containerType;

    /**
     * 指定柜型数量
     */
    private Integer containerAmount;
    /**
     * 柜型结算类型【1-按柜,2-按重量】
     */
    private String chargeType;

    /**
     * 运输类别 10大宗运输
     */
    private String transportCategory;
    /**
     * -----------------------------------结算信息---------------------------------------
     */
    /**
     * 询价方式 [10-公开询价,20-指定单价]
     */
    @NotBlank(message = "询价方式不能为空")
    private String enquiryType;
    /**
     * 差价类型 [10-不含差价,20-含差价]
     */
    private String priceDifferenceType;
    /**
     * 基价
     */
    private BigDecimal enquiryTypeBasePrice;
    /**
     * 税率(%)
     */
    private BigDecimal enquiryTypeBaseTaxRate;
    /**
     * 网络货运主体运费留存率
     */
    private BigDecimal freightSurvivalRate;
    /**
     * 开票价
     */
    private BigDecimal enquiryTypeBaseOpenTicket;

    /**
     * 金额取整方式
     */
    private Integer roundingMode;

    /**
     * 亏吨扣款信息
     */
    private LossDeductibleCommand lossDeductible;

    /**
     * 结算重量指定
     */
    @NotBlank(message = "结算重量指定不能为空")
    private String otherClearWeight;
    /**
     * 结算方式 [10-现结,20-月结,99-其他]
     */
    @NotBlank(message = "结算方式不能为空")
    private String otherClearType;
    /**
     * -----------------------------------其他信息---------------------------------------
     */
    /**
     * 回单方式 [11-不需要签回单,21-原件返回,31-签单返回]
     */
    @NotBlank(message = "是否回单不能为空")
    private String otherReceiptType;
    /**
     * 卸货自动签收方式 [10-自动签收，20-手动签收]
     */
    private String unloadAutoSignMethod;

    /**
     * 客户ID
     */
    @NotBlank(message = "客户ID不能为空")
    private String customerId;
    /**
     * 货源备注
     */
    private String goodsRemark;
    /**
     * 运输要求
     */
    private String otherRemark;
    /**
     * 货源图片
     */
    private List<AttachmentCommand> goodsPictures;
    /**
     * 销售合同号
     */
    private String salesContractNumber;

    /**
     * 来源业务ID:下游询价填货源号，转交易填订单号
     */
    private String sourceBusinessId;

}
