/**
 * =========================================================
 * ===       此类是由代码工具生成，框架开发者
 * ===       框架开发者Create By: 李健华
 * ===       创建时间: 2019/11/22 18:02:08
 * =========================================================
 */

package com.wanlianyida.user.application.model.query.tcs;

import lombok.Data;

import java.io.Serializable;

@Data
public class GuacheQuery implements Serializable {
    private static final long serialVersionUID = 100000L;

    private String carPlateNo;

}
