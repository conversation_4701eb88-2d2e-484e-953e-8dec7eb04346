package com.wanlianyida.user.application.model.command.crm;

import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class AddressChangeCommand extends ExternalBaseRequest {

    @NotEmpty(message = "地址ID不能为空")
    private String lineId;

    @NotEmpty(message = "联系人不能为空")
    private String linker;

    @NotEmpty(message = "联系电话不能为空")
    private String phoneNumber;

    private boolean changeBizData;
}
