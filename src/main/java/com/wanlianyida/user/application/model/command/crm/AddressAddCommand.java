package com.wanlianyida.user.application.model.command.crm;

import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import lombok.Data;

@Data
public class AddressAddCommand extends ExternalBaseRequest {

    /**
     * 省
     */
    private String sendAddrProvinceName;

    /**
     * 省
     */
    private String sendAddrProvince;

    /**
     * 市
     */
    private String sendAddrCityName;

    /**
     * 市
     */
    private String sendAddrCity;

    /**
     * 区
     */
    private String sendAddrAreaName;

    /**
     * 区
     */
    private String sendAddrArea;

    /**
     * 详细地址
     */
    private String sendAddrDetail;

    /**
     * 收货地址
     */
    private String sendAddrrss;

    /**
     * 联系人
     */
    private String startSendLinker;

    /**
     * 手机号
     */
    private String startSendPhoneNumber;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 地址简称
     */
    private String lineShortName;

    /**
     * 业务类型
     */
    private String lineAddressType;




}
