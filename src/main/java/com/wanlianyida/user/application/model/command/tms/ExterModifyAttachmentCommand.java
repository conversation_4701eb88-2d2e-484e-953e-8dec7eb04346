package com.wanlianyida.user.application.model.command.tms;

import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("变更信息附件表")
public class ExterModifyAttachmentCommand extends ExternalBaseRequest {

    private Long id;

    @ApiModelProperty("变更信息表id")
    private Long modifyInfoId;

    @ApiModelProperty("业务类型，10变更线路")
    private Integer bizType;

    @ApiModelProperty("文件名")
    private String fileName;

    @ApiModelProperty("附件地址")
    private String fileUrl;

    private String creatorId;

    private Date createdDate;

    private String updaterId;

    private Date updatedDate;

    private Integer versionCode;

}
