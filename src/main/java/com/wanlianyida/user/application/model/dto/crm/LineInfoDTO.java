package com.wanlianyida.user.application.model.dto.crm;

import lombok.Data;

@Data
public class LineInfoDTO {

    /**
     * 线路id
     */
    private String lineId;

    /**
     * 地址类型 10 出发地 ,20 目的地
     */
    private String lineType;

    /**
     * 线路出发地发货人/常用地址联系人 称谓
     */
    private String startSendLinker;
    /**
     * 线路出发地发货人/常用地址联系人  联系电话
     */
    private String startSendPhoneNumber;

    /**
     * 线路出发地发货人/常用地址联系人 称谓
     */
    private String endReceiveLinker;

    /**
     * 线路出发地发货人/常用地址联系人  联系电话
     */
    private String endReceivePhoneNumber;

    /**
     * 出发地id
     */
    private String sendAddrId;

    /**
     * 目的地联系人
     */
    private String receiveAddrId;

    /**
     * 出发地简称
     */
    private String sendShortName;

    /**
     * 目的地简称
     */
    private String receiveShortName;


}
