package com.wanlianyida.user.application.model.command.lsds.publish;

import com.wanlianyida.lsds.api.model.command.publish.IdentifyCodeCommand;
import com.wanlianyida.lsds.api.model.command.publish.InsuranceCommand;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 司机货源个性化参数
 */
@Data
public class DriverGoodsSourceCommand {
    /**
     * 司机评级
     */
    private String driverGrade;
    /**
     * 别名
     */
    private String orderAlias;

    /**
     * 议价设置【10-可议价,20-不可议价】
     */
    private Integer bargainConfig;
    /**
     * 议价联系人姓名
     */
    private String bargainName;

    /**
     * 议价联系人手机号
     */
    private String bargainMobile;
    /**
     * 抹零方式 【10-抹零,20-不抹零】
     */
    private String zeroRoundingMethod;
    /**
     * 短倒人工确认标志 [10-自动确认；20-人工确认]
     */
    private String manualConfirmFlag;
    /**
     * 分次支付司机运费【0-停用,1-启用】
     */
    private Integer batchPaymentSwitch;
    /**
     * 是否到付【0-无,1-应付司机运费-预付-回单付】
     */
    private Integer signPaymentSwitch;
    /**
     * 是否回单付【0-无,1-是】
     */
    private Integer receiptPaymentSwitch;

    /**
     * 回单金额
     */
    private BigDecimal receiptAmount;
    /**
     * 是否使用预付费 [0-是；1-否]
     */
    private String advancePaymentFlag;
    /**
     * 预付费(元)
     */
    private BigDecimal advancePayment;
    /**
     * 运费结算时间 (单位工作日)
     */
    private Integer settlementPeriod;
    /**
     * 结算联系人姓名
     */
    private String settlementName;

    /**
     * 结算联系人电话
     */
    private String settlementMobile;
    /**
     * 装货联系人名称
     */
    private String loadName;

    /**
     * 装货联系人电话
     */
    private String loadMobile;

    /**
     * 运输跟踪服务[ 10使用，20不使用 ]
     */
    private Integer trackService;
    /**
     * -----------------------------------保险信息---------------------------------------
     */
    /**
     * 保险信息
     */
    private InsuranceCommand insurance;

    /**
     * -----------------------------------增值服务信息---------------------------------------
     */
    /**
     * 优享服务状态 [10-使用 20-未使用]
     */
    private String premiumServStatus;
    /**
     * 渠道调度电话1
     */
    private String dispatcherPhone;

    /**
     * 渠道调度电话2
     */
    private String dispatcherBakPhone;

    /**
     * 二维码单价脱敏显示【10-脱敏显示,20-不脱敏显示】
     */
    private String priceDesensitizeTag;
    /**
     * 二维码列表（货源码+收款码）
     */
    private List<IdentifyCodeCommand> qrsCodeRelations;

    /**
     * 司机运费差价(%)
     */
    private BigDecimal freightPriceDifferencePercentage;

    /**
     * 司机运费差价(元)
     */
    private BigDecimal freightPriceDifference;
}
