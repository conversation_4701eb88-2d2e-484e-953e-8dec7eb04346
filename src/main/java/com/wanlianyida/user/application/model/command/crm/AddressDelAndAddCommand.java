package com.wanlianyida.user.application.model.command.crm;

import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class AddressDelAndAddCommand extends ExternalBaseRequest {



    /**
     * 地址id
     */
    private String lineId;

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 线路/常用地址 类型【1-线路,2-常用地址】
     */
    private String lineAddressType;

    /**
     * 常用地址简称
     */
    private String lineShortName;

    /**
     * 常用地址（省code）
     */
    private String sendAddrProvince;

    /**
     * 常用地址（省中文）
     */
    private String sendAddrProvinceName;

    /**
     * 常用地址（市code）
     */
    private String sendAddrCity;

    /**
     * 常用地址（市中文）
     */
    private String sendAddrCityName;

    /**
     * 常用地址（区/县code）
     */
    private String sendAddrArea;

    /**
     * 常用地址（区/县中文）
     */
    private String sendAddrAreaName;

    /**
     * 常用地址（街道code）
     */
    private String sendAddrStreet;

    /**
     * 常用地址（街道中文）
     */
    private String sendAddrStreetName;

    /**
     * 常用地址详细地址
     */
    private String sendAddrDetail;

    /**
     * 常用地址联系人
     */
    private String startSendLinker;

    /**
     * 常用地址联系人联系电话
     */
    private String startSendPhoneNumber;

    private BigDecimal electronicFenceRadius;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    private Integer status;


    private String existLineCheckTag;

    /**
     * 出发地和目的地地址id
     */
    private String sendAndReceiveAddrId;

    /**
     * 地址是否删除 0.正常，1.已删；
     */
    private String item3;
}
