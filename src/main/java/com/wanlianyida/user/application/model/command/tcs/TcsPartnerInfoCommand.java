package com.wanlianyida.user.application.model.command.tcs;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class TcsPartnerInfoCommand extends ExternalBaseRequest implements Serializable {
    private static final long serialVersionUID = 4056666504325603068L;

    /**
     * 伙伴主键
     */
    @ApiModelProperty(value = "伙伴主键", name = "partnerId")
    private String partnerId;

    /**
     * 伙伴企业id
     */
    @ApiModelProperty(value = "伙伴企业id", name = "partnerCompanyId")
    private String partnerCompanyId;

    /**
     * 伙伴企业全称
     */
    @ApiModelProperty(value = "伙伴企业全称", name = "partnerCompanyName")
    private String partnerCompanyName;

    /**
     * 伙伴企业简称
     */
    @ApiModelProperty(value = "伙伴企业简称", name = "partnerCompanyShortName")
    private String partnerCompanyShortName;

    /**
     * 伙伴类型10-货源伙伴 20-运力伙伴
     */
    @ApiModelProperty(value = "伙伴类型10-货源伙伴 20-运力伙伴", name = "partnerType")
    private String partnerType;

    /**
     * 托运企业id
     */
    @ApiModelProperty(value = "托运企业id", name = "shipperCompanyId")
    private String shipperCompanyId;

    /**
     * 托运企业全称
     */
    @ApiModelProperty(value = "托运企业全称", name = "shipperCompanyName")
    private String shipperCompanyName;

    /**
     * 托运企业简称
     */
    @ApiModelProperty(value = "托运企业简称", name = "shipperCompanyShortName")
    private String shipperCompanyShortName;

    /**
     * 结算方式10-按差价 20-按每车固定金额 30-按每车固定比例 40-按每吨固定金额 50-按每吨固定比例
     */
    @ApiModelProperty(value = "结算方式 10-按差价 20-按每车固定金额 30-按每车固定比例 40-按每吨固定金额 50-按每吨固定比例", name = "settlementType")
    private String settlementType;

    /**
     * 结算值比例%
     */
    @ApiModelProperty(value = "结算值比例%", name = "settlementRate")
    private Integer settlementRate;

    /**
     * 服务伙伴关系状态  10-待生效  20-已生效  30-已拒绝  40-已停用

     */
    @ApiModelProperty(value = "服务伙伴关系状态  10-待生效  20-已生效  30-已拒绝  40-已停用", name = "status")
    private String status;

    /**
     * 服务发票类型 10-增值税专用发票 20-增值税普通发票(电子)
     */
    @ApiModelProperty(value = "服务发票类型 10-增值税专用发票 20-增值税普通发票(电子)", name = "invoiceType")
    private String invoiceType;

    /**
     * 发票税率%
     */
    @ApiModelProperty(value = "发票税率%", name = "invoiceRate")
    private String invoiceRate;

    /**
     * 是否交易主体 10-是 20-否
     */
    @ApiModelProperty(value = "是否交易主体 10-是 20-否", name = "isMainBody")
    private String isMainBody;

    /**
     * 业务归属公司id
     */
    @ApiModelProperty(value = "业务归属公司id", name = "bizCompanyId")
    private String bizCompanyId;

    /**
     * 订单单价（包含税率后的开票价）
     */
    @ApiModelProperty(value = "订单单价（包含税率后的开票价）", name = "enquiryTypeBaseOpenTicket")
    private BigDecimal enquiryTypeBaseOpenTicket;


    /**
     * 伙伴货物主键
     */
    @ApiModelProperty(value = "伙伴货物主键", name = "partnerGoodsId")
    private String partnerGoodsId;

    /**
     * 货物名称
     */
    @ApiModelProperty(value = "货物名称", name = "goodsName")
    private String goodsName;

    /**
     * 伙伴货物记录数
     */
    @ApiModelProperty(value = "伙伴货物记录数", name = "goodsCount")
    private int goodsCount;

    /**
     * 货物名称列表
     */
    @ApiModelProperty(value = "货物名称列表", name = "goodsNameList")
    private List<String> goodsNameList;


    /**
     * 货物类型
     */
    @ApiModelProperty(value = "货物类型", name = "goodsType")
    private String goodsType;


    /**
     * 自动确认伙伴关系10-是 20-否
     */
    @ApiModelProperty(value = "自动确认伙伴关系10-是 20-否", name = "autoConfirmTag")
    private String autoConfirmTag;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间", name = "modifyDate")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date modifyDate;


    /**
     * 伙伴货物主键集合
     */
    @ApiModelProperty(value = "伙伴货物主键集合", name = "partnerGoodsIds")
    private List<String> partnerGoodsIds;

}
