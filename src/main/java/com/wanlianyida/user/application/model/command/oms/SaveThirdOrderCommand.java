package com.wanlianyida.user.application.model.command.oms;

import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 保存三方订单
 **/
@ApiModel(description = "保存三方订单命令对象")
@Data
public class SaveThirdOrderCommand extends ExternalBaseRequest {

    @ApiModelProperty(value = "万联易达订单号", name = "omsOrderId")
    private String omsOrderId;

    @ApiModelProperty(value = "三方订单号", name = "thirdOrderId")
    private String thirdOrderId;

    @ApiModelProperty(value = "三方网货主体", name = "networkMainBodyId")
    private String networkMainBodyId;

    @ApiModelProperty(value = "网络货运主体名称", name = "networkMainBodyName")
    private String networkMainBodyName;

    @ApiModelProperty(value = "推送时间", name = "pushDate")
    private LocalDateTime pushDate;

    @ApiModelProperty(value = "货源编号", name = "goodsId")
    private String goodsId;

    @ApiModelProperty(value = "货物名称", name = "goodsName")
    private String goodsName;

    @ApiModelProperty(value = "货物类型", name = "goodsType")
    private String goodsType;

    @ApiModelProperty(value = "货物重量", name = "goodsWeight")
    private BigDecimal goodsWeight;

    @ApiModelProperty(value = "下单数量单位[10-吨 20-车]", name = "orderQuantityUnit")
    private Integer orderQuantityUnit;

    @ApiModelProperty(value = "下单数量", name = "orderQuantity")
    private BigDecimal orderQuantity;

    @ApiModelProperty(value = "不含税运费单价", name = "exclTaxFreightPrice")
    private BigDecimal exclTaxFreightPrice;

    @ApiModelProperty(value = "货物单价", name = "goodsPrice")
    private BigDecimal goodsPrice;

    @ApiModelProperty(value = "起运地简称", name = "sendAddrShorthand")
    private String sendAddrShorthand;

    @ApiModelProperty(value = "起运地(省)", name = "sendAddrProvince")
    private String sendAddrProvince;

    @ApiModelProperty(value = "起运地(市)", name = "sendAddrCity")
    private String sendAddrCity;

    @ApiModelProperty(value = "起运地(区/县)", name = "sendAddrArea")
    private String sendAddrArea;

    @ApiModelProperty(value = "起运详细地址", name = "sendAddrDetail")
    private String sendAddrDetail;

    @ApiModelProperty(value = "发货人", name = "startSendLinker")
    private String startSendLinker;

    @ApiModelProperty(value = "发货人联系方式", name = "startSendPhoneNumber")
    private String startSendPhoneNumber;

    @ApiModelProperty(value = "目的地简称", name = "receiveAddrShorthand")
    private String receiveAddrShorthand;

    @ApiModelProperty(value = "目的地(省)", name = "receiveAddrProvince")
    private String receiveAddrProvince;

    @ApiModelProperty(value = "目的地(市)", name = "receiveAddrCity")
    private String receiveAddrCity;

    @ApiModelProperty(value = "目的地(区/县)", name = "receiveAddrArea")
    private String receiveAddrArea;

    @ApiModelProperty(value = "目的详细地址", name = "receiveAddrDetail")
    private String receiveAddrDetail;

    @ApiModelProperty(value = "收货人", name = "endReceiveLinker")
    private String endReceiveLinker;

    @ApiModelProperty(value = "收货人联系方式", name = "endReceivePhoneNumber")
    private String endReceivePhoneNumber;

    @ApiModelProperty(value = "起运地(省)(中文)", name = "sendAddrProvinceName")
    private String sendAddrProvinceName;

    @ApiModelProperty(value = "起运地(市)(中文)", name = "sendAddrCityName")
    private String sendAddrCityName;

    @ApiModelProperty(value = "起运地(区/县)(中文)", name = "sendAddrAreaName")
    private String sendAddrAreaName;

    @ApiModelProperty(value = "目的地(省)(中文)", name = "receiveAddrProvinceName")
    private String receiveAddrProvinceName;

    @ApiModelProperty(value = "目的地(市)(中文)", name = "receiveAddrCityName")
    private String receiveAddrCityName;

    @ApiModelProperty(value = "目的地(区/县)(中文)", name = "receiveAddrAreaName")
    private String receiveAddrAreaName;

    @ApiModelProperty(value = "结算类型[10-延迟付 20-分次结算]", name = "settlementType")
    private Integer settlementType;

    @ApiModelProperty(value = "结算吨数类型[10-按照原发吨数 20-按照实收吨数 30-按照原发和实收吨数较小的]", name = "settlementWeightType")
    private Integer settlementWeightType;

    @ApiModelProperty(value = "结算天数", name = "settlementDays")
    private Integer settlementDays;

    @ApiModelProperty(value = "是否尾款结算[0-否 1-是]", name = "isFinalPaymentSettled")
    private Integer isFinalPaymentSettled;

    @ApiModelProperty(value = "尾款结算金额", name = "finalPaymentSettledAmount")
    private BigDecimal finalPaymentSettledAmount;

    @ApiModelProperty(value = "途耗类型[10-按重量(单位为kg) 20-按比例(单位为%)]", name = "transportLossType")
    private Integer transportLossType;

    @ApiModelProperty(value = "途耗数量", name = "transportLossQuantity")
    private BigDecimal transportLossQuantity;

    @ApiModelProperty(value = "收货抹零类型[10-不抹零 20-个位抹零 30-十位抹零 40-小数抹零 50-五元取整]", name = "roundOffType")
    private Integer roundOffType;

    @ApiModelProperty(value = "固定扣款类型[10-无 20-固定金额 30-固定比例]", name = "fixedDeductionType")
    private Integer fixedDeductionType;

    @ApiModelProperty(value = "固定扣款数量", name = "fixedDeductionQuantity")
    private BigDecimal fixedDeductionQuantity;

    @ApiModelProperty(value = "固定扣款原因", name = "fixedDeductionReason")
    private String fixedDeductionReason;

    @ApiModelProperty(value = "亏吨扣款类型[10-不扣款 20-亏吨即扣款 30-超出指定重量 40-超出原发比例]", name = "shortfallDeductionType")
    private Integer shortfallDeductionType;

    @ApiModelProperty(value = "亏吨扣款数量", name = "shortfallDeductionQuantity")
    private BigDecimal shortfallDeductionQuantity;

    @ApiModelProperty(value = "扣款金额保留[10-小数点四舍五入 20-小数点第三位舍弃 30-小数点第四位四舍五入]", name = "deductionPrecisionType")
    private Integer deductionPrecisionType;

}
