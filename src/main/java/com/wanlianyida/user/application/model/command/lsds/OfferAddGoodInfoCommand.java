package com.wanlianyida.user.application.model.command.lsds;

import lombok.Data;

import java.io.Serializable;

@Data
public class OfferAddGoodInfoCommand implements Serializable {

    /**
     * 货源编号  货源信息表
     */
    private String goodsId;

    /**
     * 当前报价轮次
     */
    private Integer offerCurrentRounds;

    /**
     * 公司ID
     */
    private String companyId;

    /**
     * 当前报价轮次开始时间
     */
    private String currentOfferStartDate;

    /**
     * 报价有效时间(小时)
     */
    private Integer enquiryOfferTime;

    /**
     * 可以报价轮次，获取系统设置当前可允许的轮次作记录
     */
    private Integer offerRound;

    /**
     * 是否提供运输方式【checkbox:11-是,21-否】
     */
    private String transportIsOptions;

    /**
     * 报价页面 10：货源列表 20：报价管理 30 伙伴报价管理
     */
    private Integer offerSource;



}
