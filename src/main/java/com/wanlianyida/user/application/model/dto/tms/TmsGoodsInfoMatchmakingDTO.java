package com.wanlianyida.user.application.model.dto.tms;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class TmsGoodsInfoMatchmakingDTO implements Serializable {

    private Long id;

    @ApiModelProperty("业务id")
    private String busId;

    @ApiModelProperty("不含税单价")
    private BigDecimal price;

    @ApiModelProperty("含税单价")
    private BigDecimal priceTax;

    @ApiModelProperty("税率")
    private BigDecimal taxRatio;

    @ApiModelProperty("议价设置，10可议价，20不可议价")
    private Integer bargainConfig;

    @ApiModelProperty("议价联系人名称")
    private String bargainName;

    @ApiModelProperty("议价联系人电话")
    private String bargainMobile;

    @ApiModelProperty("装车联系人名称")
    private String loadName;

    @ApiModelProperty("装车联系人电话")
    private String loadMobile;

    @ApiModelProperty("结算联系人名称")
    private String settlementName;

    @ApiModelProperty("结算联系人电话")
    private String settlementMobile;

    @ApiModelProperty("撮合有效期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date expireDate;

    @ApiModelProperty("运输跟踪服务，10使用，20不使用")
    private Integer trackService;

    @ApiModelProperty("运输要求")
    private String transportRequire;

    @ApiModelProperty("类型，10货源，20订单")
    private Integer configType;
}
