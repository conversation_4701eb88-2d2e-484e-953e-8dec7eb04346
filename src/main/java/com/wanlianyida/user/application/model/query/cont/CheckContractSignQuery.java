package com.wanlianyida.user.application.model.query.cont;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
public class CheckContractSignQuery extends ExternalBaseRequest {

    /**
     * 业务类型 120-网络货运合同,130-承运合同
     */
    @NotEmpty(message = "业务类型不能为空")
    private String contractType;
    /**
     * 合同类型 20-长期合同,40-长期合同补充协议
     */
    @NotEmpty(message = "合同类型不能为空")
    private String contractNature;

    /**W
     * 甲方公司ID
     */
    @NotEmpty(message = "甲方公司ID不能为空")
    private String partyACompanyId;
    /**
     * 乙方公司ID
     */
    @NotEmpty(message = "乙方公司ID不能为空")
    private String partyBCompanyId;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @NotNull(message = "开始时间不能为空")
    private Date startTime;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @NotNull(message = "结束时间不能为空")
    private Date endTime;



}
