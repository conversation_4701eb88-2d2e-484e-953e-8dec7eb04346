package com.wanlianyida.user.application.model.dto.bi;

import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年06月16日 16:43
 */
@Data
public class MetadataDTO {

    private int count;

    private String weightSum;

    private String freightSum;

    public void setWeightSum(BigDecimal weightSum) {
        this.weightSum = weightSum.setScale(3, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
    }

    public void setFreightSum(BigDecimal freightSum) {
        this.freightSum = freightSum.setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
    }

    public void setWeightSum(String weightSum) {
        this.weightSum = new BigDecimal(weightSum).setScale(3, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
    }

    public void setFreightSum(String freightSum) {
        this.freightSum = new BigDecimal(freightSum).setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
    }
}
