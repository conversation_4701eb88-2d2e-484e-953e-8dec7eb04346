package com.wanlianyida.user.application.model.query.lsds;

import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OfferGoodsListQuery extends ExternalBaseRequest {

    @ApiModelProperty(value = "目的地简称", name = "receiveAddrShortName", example = "北朝")
    private String receiveAddrShortName;

    @ApiModelProperty(value = "出发地简称", name = "sendAddrShortName", example = "广深")
    private String sendAddrShortName;

    @ApiModelProperty(value = "起始发布时间", name = "startAuditDate", example = "2020-06-16 14:46:02")
    private String startAuditDate;

    @ApiModelProperty(value = "截止发布时间", name = "endCreateDate", example = "2020-06-16 14:46:02")
    private String endAuditDate;

    @ApiModelProperty(value = "货品名称", name = "goodsName", example = "大米")
    private String goodsName;

    @ApiModelProperty(value = "运输类型【checkbox:1-公路集卡,2-公路整车,3-水运,4-铁路,5-多式联运,6-物流项目】", name = "transportationType", example = "150")
    private String transportationType;

    @ApiModelProperty(value = "10 创建时间倒序   20 审核时间降序  30  审核时间升序   40  创建时间降序 ")
    private Integer queryType;

    @ApiModelProperty(value = "公司id", name = "companyId", example = "2")
    private String companyId;

}
