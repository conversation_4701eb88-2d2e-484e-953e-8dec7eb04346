package com.wanlianyida.user.application.model.query.oms;

import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class OrderDetailQuery extends ExternalBaseRequest {

    @NotBlank(message = "订单号不能为空")
    @ApiModelProperty(value = "订单号", name = "orderId")
    private String orderId;

}
