package com.wanlianyida.user.application.model.dto.tcs;

import lombok.Data;

/**
 * 发布司机货源指定司机运力查询
 * <AUTHOR>
 * @date 2025/03/04
 */
@Data
public class AssignDriverTransportCapacityDTO {
    /**
     * 司机id
     */
    private String driverId;
    /**
     * 司机身份证
     */
    private String idCardNo;
    /**
     * 司机姓名
     */
    private String name;
    /**
     * 司机手机号
     */
    private String contactPhoneNumber;
    /**
     * 司机用户id
     */
    private String userBaseId;
    /**
     * 车牌号
     */
    private String carPlateNo;
    /**
     * 司机准假车型
     */
    private String exLicenseType;
    /**
     * 司机运营标识
     */
    private String networkDriver;
    /**
     * 司机评级
     */
    private String driverRating;
    /**
     * 司机实名认证状态
     */
    private String eSiginStatus;
    /**
     * 司机注册状态
     */
    private String loginStatus;

}
