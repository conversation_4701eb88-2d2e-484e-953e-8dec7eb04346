package com.wanlianyida.user.application.model.dto.oms;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单列表
 */
@Data
public class ShipperOrderPageListDTO {

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderId;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态")
    private String orderStatus;

    /**
     * 承运方公司id
     */
    @ApiModelProperty(value = "承运方公司id")
    private String carrierCompanyId;

    /**
     * 承运方简称
     */
    @ApiModelProperty(value = "承运方简称")
    private String carrierName;

    /**
     * 承运方全称
     */
    @ApiModelProperty(value = "承运方全称")
    private String carrierFullName;

    /**
     * 起运地简称
     */
    @ApiModelProperty(value = "出发地简称")
    private String sendAddrShorthand;

    /**
     * 目的地简称
     */
    @ApiModelProperty(value = "目的地简称")
    private String receiveAddrShorthand;

    /**
     * 货源名称
     */
    @ApiModelProperty(value = "货源名称")
    private String goodsName;

    /**
     * 订单总数量
     */
    @ApiModelProperty(value = "订单总数量")
    private String orderQuantity;

    /**
     * 下单数量单位
     */
    @ApiModelProperty(value = "订单总数量单位")
    private String orderQuantityUnits;

    /**
     * 货物总重量(吨)
     */
    @ApiModelProperty(value = "货物总重量(吨)")
    private BigDecimal weightSum;

    /**
     * 运输类型
     */
    @ApiModelProperty(value = "运输类型")
    private String transportationType;

    /**
     * 订单金额
     */
    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderAmount;

    /**
     * 要求发货时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @ApiModelProperty(value = "要求发货时间")
    private Date releaseDate;

    /**
     * 要求到货时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @ApiModelProperty(value = "要求到货时间")
    private Date arriveDate;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 订单有效期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "订单有效期")
    private Date orderExpireDate;

    /**
     * 货运类型
     */
    @ApiModelProperty(value = "货运类型")
    private String freightType;

    /**
     * 发货方简称
     */
    @ApiModelProperty(value = "发货方简称")
    private String shipperName;

    /**
     * 订单类型
     */
    @ApiModelProperty(value = "订单类型")
    private String orderSourceType;

    /**
     * 订单来源
     */
    @ApiModelProperty(value = "订单来源")
    private String orderSource;

    /**
     * 订单发货状态 21发货未完成，11发货已完成
     */
    @ApiModelProperty(value = "订单发货状态 21发货未完成，11发货已完成")
    private String orderShippingStatus;

    /********* 按钮逻辑字段 *********/

    /**
     * 支付状态：1.待付款。2.已付款。
     */
    @ApiModelProperty(value = "订单支付状态")
    private String orderPayStatus;

    /**
     * 发货方评价状态 ：【select:21-未评价,11-已评价】
     */
    @ApiModelProperty(value = "发货方评价状态")
    private String shipperAppraiseStatus;

    /**
     * 托运方公司id
     */
    @ApiModelProperty(value = "托运方公司id")
    private String shipperCompanyId;

    /**
     * 订单拆分类型【select: 0-未拆分,1-拆段,2-拆重】
     */
    @ApiModelProperty(value = "订单拆分类型")
    private String orderSplit;

    /**
     * 订单拆分人的企业id
     */
    @ApiModelProperty(value = "拆单公司Id")
    private String splitCompanyId;

    /**
     * 订单拆分人的类型，1发货方，2承运方;
     */
    @ApiModelProperty(value = "拆单角色")
    private String splitRole;

    /**
     * 拆单类型 null 没有拆单 1横拆,2纵拆
     */
    @ApiModelProperty(value = "拆单类型 null 没有拆单 1横拆,2纵拆")
    private String splitType;

    /**
     * 当前订单拆分方式[0-未拆分,1-拆段,2-拆重]
     */
    @ApiModelProperty(value = "当前订单拆分方式[0-未拆分,1-拆段,2-拆重]")
    private String currentOrderSplitMethod;

    /********* 扩展信息 *********/
    /**
     * 第三方流程id
     */
    @ApiModelProperty(value = "第三方流程id")
    private String flowId;

    /**
     * 签署流程id
     */
    @ApiModelProperty(value = "签署流程id")
    private String signFlowId;

    /**
     * 流程状态，1：签署中，2：已签署，3：签署失败
     */
    @ApiModelProperty(value = "流程状态，1：签署中，2：已签署，3：签署失败")
    private String flowStatus;

}
