package com.wanlianyida.user.application.model.query.its;

import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Map;

@Data
public class ExternalItsListQuery extends ExternalBaseRequest {

    @ApiModelProperty(value = "中台接口appCode")
    @NotBlank(message = "中台接口appCode不能为空")
    private String appCode;

    @ApiModelProperty(value = "接口请求参数的key")
    @NotBlank(message = "接口请求参数的key不能为空")
    private String key;

    @ApiModelProperty(value = "接口参数")
    private Map<String,Object> biParam;

}
