package com.wanlianyida.user.application.model.dto.tcs;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class TcsGoodsAndOrderChoosePartnerDTO implements Serializable {
    private static final long serialVersionUID = -8720159317755502908L;


    @ApiModelProperty(value = "主键", name = "goodsCpId")
    private String goodsCpId;


    @ApiModelProperty(value = "货源id", name = "goodsId")
    private String goodsId;

    @ApiModelProperty(value = "订单号", name = "orderId")
    private String orderId;


    @ApiModelProperty(value = "托运企业id", name = "shipperCompanyId")
    private String shipperCompanyId;


    @ApiModelProperty(value = "承运企业id", name = "carrierCompanyId")
    private String carrierCompanyId;


    @ApiModelProperty(value = "伙伴id", name = "partnerId")
    private String partnerId;


    @ApiModelProperty(value = "伙伴企业id", name = "partnerCompanyId")
    private String partnerCompanyId;


    @ApiModelProperty(value = "伙伴企业全称", name = "partnerCompanyName")
    private String partnerCompanyName;


    @ApiModelProperty(value = "伙伴企业简称", name = "partnerCompanyShortName")
    private String partnerCompanyShortName;


    @ApiModelProperty(value = "伙伴类型  10-货源伙伴 20-运力伙伴", name = "partnerType")
    private String partnerType;


    @ApiModelProperty(value = "结算方式10-按差价 20-按每车固定金额 30-按每车固定比例 40-按每吨固定金额 50-按每吨固定比例", name = "settlementType")
    private String settlementType;


    @ApiModelProperty(value = "结算值比例%", name = "settlementRate")
    private Integer settlementRate;


    @ApiModelProperty(value = "订单单价（包含税率后的开票价）", name = "enquiryTypeBaseOpenTicket")
    private BigDecimal enquiryTypeBaseOpenTicket;


    @ApiModelProperty(value = "业务归属公司id", name = "bizCompanyId")
    private String bizCompanyId;

}
