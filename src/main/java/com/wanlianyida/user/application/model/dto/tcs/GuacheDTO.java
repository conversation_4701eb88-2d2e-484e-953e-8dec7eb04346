package com.wanlianyida.user.application.model.dto.tcs;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class GuacheDTO implements Serializable {
    private static final long serialVersionUID = 650908882262367211L;

    /**
     * 车辆id
     */
    private String carId;

    /**
     * 挂车车牌
     */
    private String trailerPlateNumber;

    /**
     * 车辆类型
     */
    private String assignCarType;

    /**
     * 车长
     */
    private BigDecimal carLength;

    /**
     * 挂车整备质量(吨)
     * */
    private Integer transCurbWeight;

    /**
     * 挂车核定载质量(吨)
     * */
    private Integer standardTransWeight;

    /**
     * 车辆颜色
     */
    private Integer carColor;

}
