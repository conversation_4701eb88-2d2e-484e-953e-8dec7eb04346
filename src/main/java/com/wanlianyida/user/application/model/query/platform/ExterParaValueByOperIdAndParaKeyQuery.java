package com.wanlianyida.user.application.model.query.platform;

import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class ExterParaValueByOperIdAndParaKeyQuery extends ExternalBaseRequest {


    @ApiModelProperty(value = "参数字典key值", name = "parameterKey")
    @NotBlank(message = "参数字典key值不允许为空")
    private String parameterKey;


    @ApiModelProperty(value = "网络货运主体id", name = "operationMainBodyId")
    @NotBlank(message = "网络货运主体id不允许为空")
    private String operationMainBodyId;


}
