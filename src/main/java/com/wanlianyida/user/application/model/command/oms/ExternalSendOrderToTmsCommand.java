package com.wanlianyida.user.application.model.command.oms;

import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class ExternalSendOrderToTmsCommand extends ExternalBaseRequest {

    @ApiModelProperty(value = "订单号", name = "orderId")
    @NotBlank(message = "订单号不能为空")
    private String orderId;

    @ApiModelProperty(value = "订单别名-非必填", name = "orderAlias")
    @NotBlank(message = "订单别名不能为空")
    private String orderAlias;

    @ApiModelProperty(value = "货物名称", name = "goodsName",example = "货物名称")
    private String goodsName;

    @ApiModelProperty(value = "最后更新人", name = "modifyBy")
    private String modifyBy;

    @ApiModelProperty(value = "创建人id", name = "createBy")
    private String createBy;

}
