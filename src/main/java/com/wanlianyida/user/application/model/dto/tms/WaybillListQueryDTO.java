package com.wanlianyida.user.application.model.dto.tms;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class WaybillListQueryDTO {

    /**
     * 运单号
     */
    @ApiModelProperty(value = "运单号", name = "waybillId", example = "D04A242401FB401C9485DB02F5300717")
    private String waybillId;

    /**
     * 运单状态
     */
    @ApiModelProperty("公路整车：100-待确认(已派车) 110-待取消（发货方或司机申请取消） 120-已确认 300-出车 310-已签到 400-提柜(公路集卡) 410-已装货 415-卸货签到 420-已卸货 430-还柜(公路集卡)  500-已签收 510-已换车 520-已回单 530-已撤销 " +
            "集卡(提重还空): 100-待确认(已派车) 110-待取消（发货方或司机申请取消） 120-已确认 300-出车  400-提柜(公路集卡) 420-已卸货  430-还柜(公路集卡) 510-已换车 520-已回单 530-已撤销 " +
            "集卡(提空还重)：100-待确认(已派车) 110-待取消（发货方或司机申请取消） 120-已确认 300-出车  400-提柜(公路集卡) 410-已装货 430-还柜(公路集卡) 510-已换车  520-已回单 530-已撤销 " +
            "水运：99-待处理 100- 出口/出货其它 110-已订舱/舱 310-已放行(出口) 320-已装船 330-已离港 410-已补料 420-已出单 430-已交单 610-已到港 620-已卸船 710-已放行(进口) 720-已提货 730-已卸货 740-已收货 810-已还柜 999-进口/收货其它 " +
            "铁路：99-待处理 100- 出口/出货其它 110-已请车 310-已放行(出口) 320-已进货 330-已装车 340-已发车 510-已放行 520-已换车  610-已到站 620-已卸车 710-已放行(进口) 720-已提货 730-已卸货 740-已收货 999-进口/收货其它")
    private String wabillStatus;

    /**
     * 起运地市
     */
    @ApiModelProperty(value = "起运地市", name = "sendAddrCity", example = "上海市")
    private String sendAddrCity;

    /**
     * 目的地市
     */
    @ApiModelProperty(value = "目的地市", name = "receiveAddrCity", example = "北京市")
    private String receiveAddrCity;

    /**
     * 货物名称
     */
    @ApiModelProperty(value = "货物名称", name = "goodsName", example = "电子产品")
    private String goodsName;

    /**
     * 下单数量
     */
    @ApiModelProperty(value = "下单数量", name = "placeOrderNumber", example = "100")
    private BigDecimal placeOrderNumber;

    /**
     * 计价方式
     */
    @ApiModelProperty(value = "计价方式：10-重量, 20-柜, 30-车", name = "pricingMd", example = "10")
    private String pricingMd;

    /**
     * 单价（元/吨）
     */
    @ApiModelProperty(value = "单价，单位：元/吨", name = "price", example = "500.00")
    private BigDecimal price;

    /**
     * 货运类型
     */
    @ApiModelProperty(value = "货运类型：1-传统模式，2-网络模式", name = "freightType", example = "1")
    private String freightType;

    /**
     * 车牌
     */
    @ApiModelProperty(value = "车牌", name = "plateNumber", example = "京A12345")
    private String plateNumber;

    /**
     * 联系人姓名
     */
    @ApiModelProperty(value = "联系人姓名", name = "linkmanName", example = "张三")
    private String linkmanName;

    /**
     * 联系人电话
     */
    @ApiModelProperty(value = "联系人电话", name = "linkmanPhone", example = "13800138000")
    private String linkmanPhone;

    /**
     * 运输类型
     */
    @ApiModelProperty(value = "运输类型：110-公路整车, 111-公路集卡, 120-水运", name = "transportType", example = "110")
    private String transportType;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", name = "createDate", example = "2025-06-13 10:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 分账标志
     */
    @ApiModelProperty(value = "是否存在委托人：1-否，2-是", name = "splitFlag", example = "1")
    private String splitFlag;

    /**
     * 委托人名称
     */
    @ApiModelProperty(value = "委托人名称", name = "splitName", example = "李四")
    private String splitName;

    /**
     * 委托人账号
     */
    @ApiModelProperty(value = "委托人账号", name = "splitAccount", example = "*********")
    private String splitAccount;

    /**
     * 委托人收款渠道
     */
    @ApiModelProperty(value = "委托人收款渠道", name = "splitChannel", example = "支付宝")
    private String splitChannel;

    /**
     * 修改次数
     */
    @ApiModelProperty(value = "修改次数", name = "renewTimes", example = "0")
    private Integer renewTimes;

    /**
     * 委托人类型
     */
    @ApiModelProperty(value = "委托人委托类型", name = "splitType", example = "1")
    private String splitType;

    /**
     * 委托人规则值
     */
    @ApiModelProperty(value = "委托人规则值", name = "splitTypeValue", example = "20")
    private String splitTypeValue;

    /**
     * 货主端是否显示二次确认按钮
     */
    @ApiModelProperty(value = "二次确认按钮是否显示", name = "secondConfirmBtnShow", example = "false")
    private Boolean secondConfirmBtnShow;

    /**
     * 再来一单按钮是否显示
     */
    @ApiModelProperty(value = "再来一单按钮是否显示", name = "zlydBtnShow", example = "false")
    private Boolean zlydBtnShow;

    /**
     * 收款人身份证号
     */
    @ApiModelProperty(value = "实际收款人身份证号", name = "payeeIdCard", example = "110101199001011234")
    private String payeeIdCard;

    /**
     * 司机身份证号
     */
    @ApiModelProperty(value = "司机身份证号", name = "driverCardId", example = "110101198501012345")
    private String driverCardId;

    /**
     * 预计起运时间
     */
    @ApiModelProperty(value = "预计起运时间", name = "beginDate", example = "2025-06-15 08:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginDate;

    /**
     * 预计到达时间
     */
    @ApiModelProperty(value = "预计到达时间", name = "endDate", example = "2025-06-16 18:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDate;

    @ApiModelProperty(value = "货物编号", name = "goodsId")
    private String goodsId;

    @ApiModelProperty(value = "订单号", name = "orderId")
    private String orderId;

    @ApiModelProperty(value = "send_type 为1、2时，主司机名称(必须是平台司机)，派车指定多个司机，需要指定一个主司机", name = "driverName")
    private String driverName;

    @ApiModelProperty(value = "订单别名", name = "orderAlias")
    private String orderAlias;

    @ApiModelProperty(value = "司机手机号 ")
    private String driverPhone;

}
