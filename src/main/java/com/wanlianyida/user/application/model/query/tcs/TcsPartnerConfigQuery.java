package com.wanlianyida.user.application.model.query.tcs;

import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel(description = "查询已设计伙伴信息请求参数")
public class TcsPartnerConfigQuery extends ExternalBaseRequest implements Serializable {
    private static final long serialVersionUID = -4047703350640079939L;

    @NotBlank(message = "来源标识不能为空")
    @ApiModelProperty(value = "设计服务伙伴来源标识",example = "1：代报价,2：转调度")
    private String partnerSetPageTag;

    @ApiModelProperty(value = "货源id")
    private String goodsId;

    /**
     * 代报价的时候传
     */
    @ApiModelProperty(value = "承运企业")
    private String carrierCompanyId;


    @ApiModelProperty(value = "订单id")
    private String orderId;

    @ApiModelProperty(value = "托运企业")
    private String shipperCompanyId;

}
