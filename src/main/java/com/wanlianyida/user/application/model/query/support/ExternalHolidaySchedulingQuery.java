package com.wanlianyida.user.application.model.query.support;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

import javax.validation.constraints.NotNull;

/**
 * 排班计划 Query
 */
@Data
public class ExternalHolidaySchedulingQuery extends ExternalBaseRequest {

    @ApiModelProperty("排班时间")
    @NotNull(message = "排班时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date schedulingDate;

}
