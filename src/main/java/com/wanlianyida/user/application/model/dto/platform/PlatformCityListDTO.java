package com.wanlianyida.user.application.model.dto.platform;

import lombok.Data;

import java.util.List;

@Data
public class PlatformCityListDTO {

    /**
     * （例如：湖南-HN，字母码（最多3位编码）仅县级以上）
     */
    private String alphaCode;
    /**
     * 地区编码
     */
    private String areaCode;
    /**
     * 状态,1:启用 2:未启用
     */
    private String enable;
    /**
     * 全称
     */
    private String fullname;
    /**
     * 名字
     */
    private String name;
    /**
     * 节点编码id  城市区域表管理
     */
    private String node;
    /**
     * 父节点id
     */
    private String parentId;
    /**
     * 邮政编码
     */
    private String postCode;
    /**
     * 备注
     */
    private String remark;
    /**
     * 行政简称
     */
    private String shortName;
    /**
     * 排序
     */
    private int sort;
    /**
     * 拼写
     */
    private String spell;

    /**
     * 层数 先定10层
     * */
    private int level;


    /**
     * 父级名称
     */
    private String parentName;

    /**
     * 省编号
     */
    private String provinceCode;

    /**
     * 省名称
     */
    private String provinceName;


    /**
     * 映射数量
     */
    private Integer reflectionCount;

    /**
     * 城市级别list
     */
    private List<Integer> levelList;


}
