package com.wanlianyida.user.application.model.dto.platform;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class BatchConfigDTO {

    @ApiModelProperty("企业id")
    private String companyId;

    @ApiModelProperty("企业ids")
    private List<String> companyIdList;

    @ApiModelProperty("配置属性")
    private String configKey;

    @ApiModelProperty("配置属性对应的值")
    private String configValue;
}
