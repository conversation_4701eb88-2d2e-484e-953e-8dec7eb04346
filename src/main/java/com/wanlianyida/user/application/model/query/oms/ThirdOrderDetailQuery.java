package com.wanlianyida.user.application.model.query.oms;

import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class ThirdOrderDetailQuery extends ExternalBaseRequest {

    @NotBlank(message = "万联易达订单号不能为空")
    @ApiModelProperty(value = "万联易达订单号", name = "omsOrderId")
    private String omsOrderId;

}
