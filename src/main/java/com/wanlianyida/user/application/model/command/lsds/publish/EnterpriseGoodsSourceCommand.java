package com.wanlianyida.user.application.model.command.lsds.publish;

import com.wanlianyida.lsds.api.model.command.publish.AttachmentCommand;
import com.wanlianyida.lsds.api.model.command.publish.SplitLineCommand;
import lombok.Data;

import java.util.List;

/**
 * 企业货源个性化参数
 */
@Data
public class EnterpriseGoodsSourceCommand {
    /**
     * 交易签约主体id
     */
    private String transactionContractingBodyId;
    /**
     * 报价有效时间(小时)
     */
    private Integer enquiryOfferTime;
    /**
     * 当前可允许报价轮次
     */
    private Integer offerRound;
    /**
     * 招标文件
     */
    private List<AttachmentCommand> bidsFiles;
    /**
     * 上级货源编号(向下游询价产生)
     */
    private String parentGoodsId;
    /**
     * 是否拆单 [11-是,21-否]
     */
    private String goodsIsSplit;
    /**
     * 多式联运 运输段拆分列表
     */
    private List<SplitLineCommand> goodsSplitList;

    /**
     * 月结日 (1-31表示月结日，如果是32则为自然月)
     */
    private String clearMonthDay;

    /**
     * 是否需要发票【11-不需要,21-需要】
     */
    private String otherIsInvoice;


    /**
     * 是否提供运输方案【11-是,21-否】
     */
    private String transportIsOptions;
}
