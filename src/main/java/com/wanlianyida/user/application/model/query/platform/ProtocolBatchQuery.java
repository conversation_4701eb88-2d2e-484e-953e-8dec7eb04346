package com.wanlianyida.user.application.model.query.platform;

import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

import javax.validation.constraints.NotEmpty;

/**
 * 批量查询协议
 */
@Data
public class ProtocolBatchQuery extends ExternalBaseRequest {

    @NotEmpty(message = "显示位置必输")
    @ApiModelProperty(value = "显示位置字典codes数组", name = "dispLocations")
    private List<String> dispLocations;
}
