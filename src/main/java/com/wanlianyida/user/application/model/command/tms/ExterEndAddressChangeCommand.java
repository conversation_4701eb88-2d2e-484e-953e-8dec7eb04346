package com.wanlianyida.user.application.model.command.tms;

import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

import javax.validation.constraints.NotEmpty;

@Data
public class ExterEndAddressChangeCommand extends ExternalBaseRequest {

    @ApiModelProperty(value = "运单号")
    @NotEmpty(message = "运单号不能为空")
    private String waybillId;

    @ApiModelProperty("目的地id")
    @NotEmpty(message = "目的地id不能为空")
    private String endAddressId;

    @ApiModelProperty(value = "申请原因")
    @NotEmpty(message = "申请原因不能为空")
    private String applyReason;

    @ApiModelProperty(value = "附件")
    private List<ExterModifyAttachmentCommand> modifyAttachmentCommandList;



}
