package com.wanlianyida.user.application.model.command.tcs;

import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotBlank;

@Data
public class TcsDeletePartnerCommand extends ExternalBaseRequest implements Serializable {

    @NotBlank(message = "来源标识不能为空")
    @ApiModelProperty(value = "设计服务伙伴来源标识",example = "1：代报价,2：报价,2：转调度")
    private String partnerSetPageTag;

    @ApiModelProperty("伙伴货源表主键集合")
    private List<String> goodsCpId;

    @ApiModelProperty("订单id")
    private String orderId;

}
