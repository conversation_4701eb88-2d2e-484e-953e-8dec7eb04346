package com.wanlianyida.user.application.model.dto.tcs;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(description = "已伙伴信息")
public class TcsPartnerConfigDTO implements Serializable {

    private static final long serialVersionUID = -631634158306577138L;

    @ApiModelProperty("业务归属公司id")
    private String bizCompanyId;

    @ApiModelProperty("业务归属公司name")
    private String bizCompanyName;

    @ApiModelProperty("业务归属公司sortName")
    private String bizCompanyShortName;

    @ApiModelProperty("是否展示选择伙伴")
    private boolean showPartnerTag;

    @ApiModelProperty("伙伴集合")
    private List<TcsGoodsAndOrderChoosePartnerDTO> choosePartnerInfoList;


}
