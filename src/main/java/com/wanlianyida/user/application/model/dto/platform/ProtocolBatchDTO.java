package com.wanlianyida.user.application.model.dto.platform;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
public class ProtocolBatchDTO {

    @NotBlank(message = "协议名称必输")
    @Size(max = 50, message = "协议名称最多50字符")
    @ApiModelProperty(value = "协议名称", name = "fullName")
    private String fullName;

    @NotBlank(message = "显示名称必输")
    @Size(max = 50, message = "显示名称最多50字符")
    @ApiModelProperty(value = "显示名称", name = "shortName")
    private String shortName;

    @NotBlank(message = "附件OSSurl必输")
    @ApiModelProperty(value = "附件OSSurl", name = "fileUrl")
    private String fileUrl;

    /**
     * 入参是数组：入表参数是dispLocationsStr
     */
    @NotEmpty(message = "显示位置必输")
    @ApiModelProperty(value = "显示位置字典codes数组", name = "dispLocations")
    private List<String> dispLocations;

    /**
     * 出参是逗号隔开的字符串：入表参数
     */
    @ApiModelProperty(value = "显示位置字典codes", name = "dispLocationsStr")
    private String dispLocationsStr;

    /**
     * 出参是逗号隔开的字符串
     */
    @ApiModelProperty(value = "显示位置字典names", name = "dispLocationNamesStr")
    private String dispLocationNamesStr;

    @NotNull(message = "排序必输")
    @Range(min = 0, max = 99999, message = "排序值在0~99999之间")
    @ApiModelProperty(value = "排序", name = "sort")
    private Integer sort;

    @ApiModelProperty(value = "是否有效1是0否", name = "isEffective")
    private String isEffective;

    @ApiModelProperty(value = "逻辑删除1是0否", name = "delFlag")
    private String delFlag;
}
