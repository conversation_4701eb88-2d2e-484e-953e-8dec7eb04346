package com.wanlianyida.user.application.model.query.analysis;

import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> @Date 2025/8/26 10:50
 * @Description
 */
@Data
public class WaybillSummaryQuery extends ExternalBaseRequest {

    /**
     * 建议序列化的class都给一个序列化的ID，这样可以保证序列化的成功，版本的兼容性。
     */
    private static final long serialVersionUID = 100002L;

    @ApiModelProperty(value = "接口请求参数的key")
    @NotBlank(message = "接口请求参数的key不能为空")
    private String key;

    /**
     * 运单数据汇总优化需求要求使用单证号代替运单、订单、货源号等具体字段
     */
    @ApiModelProperty(value = "单证号", name = "businessCode")
    private String businessCode;


    @ApiModelProperty(value = "货物类型", name = "goodsType")
    private String goodsType;

    @ApiModelProperty(value = "运输类型：【1-公路集卡(提重还空),2-公路集卡(提空还重),3-公路整车,4-铁路,5-水运，6-公路短倒】", name = "transportType",example = "1")
    private String transportType;

    @ApiModelProperty(value =  "创建时间分段开始时间",name = "createDateBegin")
    private String createDateBegin;

    @ApiModelProperty(value =  "创建时间分段截止时间",name = "createDateEnd")
    private String createDateEnd;

    @ApiModelProperty(value = "网络货运主体id",name = "networkMainBodyId")
    private String networkMainBodyId;

    @ApiModelProperty(value = "订单实际托运人公司ID",name = "realityCompanyId")
    private String realityCompanyId;

    @ApiModelProperty(value = "订单实际托运人公司名称",name = "realityCompanyName")
    private String realityCompanyName;

    @ApiModelProperty(value = "车牌号", name = "carPlateNo")
    private String plateNumber;
    /**
     * 运单数据汇总优化需求新增条件字段
     */
    @ApiModelProperty(value = "预审状态", name = "preExamStatus")
    private String preExamStatus;

    //对应bms_network_freight_payment表的is_reconciliation字段
    @ApiModelProperty(value = "对账状态：【select:100-待申请,200-待审核,300-待付款,400-审核不通过,500-付款中,600-支付成功,700-支付失败】",name = "reconciliatStatus",example = "10")
    private String dzzt;

    //对应bms_network_freight_payment表的pay_status字段
    @ApiModelProperty(value = "付款状态：【select:100-待申请,200-待审核,300-待付款,400-审核不通过,500-付款中,600-支付成功,700-支付失败】",name = "payStatus",example = "10")
    private String payStatus;

    //对应bms_network_freight_payment表的receipt_status字段
    @ApiModelProperty(value = "申请开票状态：【select:10待申请,20待审核,30审核不通过,40待开票,50开票成功,60部分开票】",name = "receiptStatus",example = "10")
    private String receiptStatus;

    /**
     * 运单数据汇总优化需求新增条件字段
     */
    @ApiModelProperty(value = "开票时间（开始）", name = "invoicedDateStart")
    private String invoicedDateStart;

    /**
     * 运单数据汇总优化需求新增条件字段
     */
    @ApiModelProperty(value = "开票时间（结束）", name = "invoicedDateEnd")
    private String invoicedDateEnd;

    @ApiModelProperty(value = "客户名称", name = "customerName")
    private String customerName;

    @ApiModelProperty(value = "所属企业id集合", name = "realityCompanyIds")
    private List<String> bizCompanyIds;

    /** 货运类型:1-传统模式(默认)、 2-网络模式 */
    @ApiModelProperty(value = "货运类型:1-传统模式(默认)、 2-网络模式",name = "freightType")
    private String freightType;

    @Override
    public String toString() {
        return "WaybillSummaryQuery{" +
                "key='" + key + '\'' +
                ", businessCode='" + businessCode + '\'' +
                ", goodsType='" + goodsType + '\'' +
                ", transportType='" + transportType + '\'' +
                ", createDateBegin='" + createDateBegin + '\'' +
                ", createDateEnd='" + createDateEnd + '\'' +
                ", networkMainBodyId='" + networkMainBodyId + '\'' +
                ", realityCompanyId='" + realityCompanyId + '\'' +
                ", realityCompanyName='" + realityCompanyName + '\'' +
                ", plateNumber='" + plateNumber + '\'' +
                ", preExamStatus='" + preExamStatus + '\'' +
                ", dzzt='" + dzzt + '\'' +
                ", payStatus='" + payStatus + '\'' +
                ", receiptStatus='" + receiptStatus + '\'' +
                ", invoicedDateStart='" + invoicedDateStart + '\'' +
                ", invoicedDateEnd='" + invoicedDateEnd + '\'' +
                ", customerName='" + customerName + '\'' +
                ", bizCompanyIds=" + bizCompanyIds +
                ", freightType='" + freightType + '\'' +
                '}';
    }
}
