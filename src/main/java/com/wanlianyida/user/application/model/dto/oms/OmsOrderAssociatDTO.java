package com.wanlianyida.user.application.model.dto.oms;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(description = "订单关系")
public class OmsOrderAssociatDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 关系表主键Id
     */
    @ApiModelProperty(value = "关系表主键Id", name = "orderAssociateId")
    private String orderAssociateId;

    /**
     * 订单id
     */
    @ApiModelProperty(value = "订单id", name = "orderId")
    private String orderId;

    /**
     * 父类订单id（拆单）
     */
    @ApiModelProperty(value = "父类订单id（拆单）", name = "parentOrderId")
    private String parentOrderId;

    /**
     * 顶级订单号，转交易的时候为了追溯第一条单
     */
    @ApiModelProperty(value = "顶级订单号，转交易的时候为了追溯第一条单", name = "topOrderId")
    private String topOrderId;

    /**
     * 上游订单号
     */
    @ApiModelProperty(value = "上游订单号", name = "higherOrderId")
    private String higherOrderId;

    /**
     * 当前订单的货源号
     */
    @ApiModelProperty(value = "当前订单的货源号", name = "goodsId")
    private String goodsId;

    /**
     * 转交易的货源号，写在转交易的订单上，（无业务意义，展示用）
     */
    @ApiModelProperty(value = "转交易的货源号，写在转交易的订单上，（无业务意义，展示用）", name = "changeDealGoodsId")
    private String changeDealGoodsId;

    /**
     * 订单地址表主键id
     */
    @ApiModelProperty(value = "订单地址表主键id", name = "orderAddressId")
    private String orderAddressId;

    /**
     * 转交易层级 转1次的记录1  第二次被转的订单记录2
     */
    @ApiModelProperty(value = "转交易层级 转1次的记录1  第二次被转的订单记录2", name = "changeDealLevel")
    private String changeDealLevel;

    /**
     * 子单（多式联运）运输段：0母单。 1第一段。2第二段 类推
     */
    @ApiModelProperty(value = "子单（多式联运）运输段：0母单。 1第一段。2第二段 类推", name = "transportIndex")
    private String transportIndex;

}
