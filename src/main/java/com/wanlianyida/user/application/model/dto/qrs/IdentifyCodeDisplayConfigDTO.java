package com.wanlianyida.user.application.model.dto.qrs;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.wanlianyida.framework.lgicommon.model.dto.ExternalBaseDTO;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年07月02日 17:18
 */
@Data
public class IdentifyCodeDisplayConfigDTO extends ExternalBaseDTO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 企业ID
     */
    private String companyId;

    /**
     * 货源码样式开关 10-开 20-关
     */
    private String configSwitch;

    /**
     * 货源码显示样式 10-详细地址 20-省市区 30-地址简称
     */
    private String configStyle;

    /**
     * 司机端货源码列表标题显示：10货源码、20企业名称
     */
    private String appGoodsListTitle;

    /**
     * 分享码类型，10 A码（白底）；20 B码（蓝底）
     */
    private String qrType;

    /**
     * 企业简称
     */
    private String companyShortName;

    /**
     * 隐藏货源码标志[0-显示,1-隐藏]
     */
    private Integer hideCodeFlag;
}
