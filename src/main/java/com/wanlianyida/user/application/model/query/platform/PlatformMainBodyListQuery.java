package com.wanlianyida.user.application.model.query.platform;

import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import lombok.Data;

import java.util.List;

@Data
public class PlatformMainBodyListQuery extends ExternalBaseRequest {

    /**
     * 主体类型
     */
    private List<String> mainBodyTypeList;

    /**
     * 参数key
     */
    private String paramKey;

    /**
     * 参数值
     */
    private String paramValue;
}
