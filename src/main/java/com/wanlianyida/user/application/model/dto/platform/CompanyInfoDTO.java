package com.wanlianyida.user.application.model.dto.platform;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class CompanyInfoDTO implements Serializable {


    /**
     * 主键id
     */
    private String companyId;

    /**
     * 经营主体ID
     */
    private String mainBodyId;

    /**
     * 统一社会信用代码
     */
    private String socialCreditCode;

    /**
     * 真实统一信用码
     */
    private String socialCreditCodeReal;

    /**
     * 公司代码 生成规则
     */
    private String companyCode;

    /**
     * 上级公司代码为了记录子公司的关系，但这里的信用编码是唯一的，这里只为了简化层级显示用
     */
    private String parentCompanyCode;

    /**
     * 企业邀请码(预留可能用于APP)
     */
    private String invitationCode;

    /**
     * 公司名称(全称)
     */
    private String companyName;

    /**
     * 企业简称
     */
    private String companyShortName;

    /**
     * 企业地址（省Code）
     */
    private String province;

    /**
     * 企业地址（省）（中文）
     */
    private String provinceName;

    /**
     * 企业地址（市Code）
     */
    private String city;

    /**
     * 企业地址（市）（中文）
     */
    private String cityName;

    /**
     * 企业地址（区/县Code）
     */
    private String area;

    /**
     * 企业地址（区/县）（中文）
     */
    private String areaName;

    /**
     * 企业地址（镇/街道Code）
     */
    private String street;

    /**
     * 企业地址（镇/街道）（中文）
     */
    private String streetName;

    /**
     * 企业地址（详细地址）
     */
    private String addressDetail;

    /**
     * 企业类型【select:1-工商企业,2-个体工商户,3-个人】
     */
    private String licenseType;

    /**
     * 会员类型【select:10-项目会员,20-一般会员】
     */
    private String memberType;

    /**
     * 状态【select:1-正常,2-已删除】
     */
    private String status;

    /**
     * 是否冻结【11-是,,21-否】,默认是否
     */
    private String freez;

    /**
     * 申请来源【select:1-PC端,2-移动端】
     */
    private String applySource;

    /**
     * 注册ip
     */
    private String registerIp;

    /**
     * 企业logo地址
     */
    private String logoUrl;

    /**
     * 实名认证状态。营业执照是否审核通过，不含运输许可证之类的【select:1-未认证（审核还没通过或者已经过期）,2-认证通过】
     */
    private String realnameStatus;

    /**
     * 企业类型，为了不和执照类型冲突，当企业申请运输资质的审核通过的时候，变更成物流企业。【select:1-发货企业2-个体工商,3-其他,4-物流企业】
     */
    private String companyType;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createDate;

    /**
     * 修改人
     */
    private String modifyBy;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date modifyDate;

    /**
     * 联系人
     */
    private String contacts;

    /**
     * 电话
     */
    private String phone;

    /**
     * 主管税务机关
     */
    private String taxAuthority;

    /**
     * 货运类型,10传统货运20网络货运
     */
    private String freightType;

    /**
     * 传统货运开关,0关闭 1一方实名认证时生成合同 2双方实名认证时生成合同
     */
    private String traditionalFreightSwitch;

    /**
     * 平台3PL标识,0不是3PL 1是3PL
     */
    private String platformFlag;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 业务所属公司id
     */
    private String bizCompanyId;

    /**
     * 上级公司id
     */
    private String parentCompanyId;

    /**
     * 是否黑名单【0-默认,1-加入黑名单,2-移除黑名单】
     */
    private String isBlack = "0";
}
