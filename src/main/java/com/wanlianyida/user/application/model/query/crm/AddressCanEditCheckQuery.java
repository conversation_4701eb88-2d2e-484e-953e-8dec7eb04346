package com.wanlianyida.user.application.model.query.crm;

import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class AddressCanEditCheckQuery extends ExternalBaseRequest {

    @NotEmpty(message = "地址id不能为空")
    private String lineId;

    @NotEmpty(message = "地址类型不能为空")
    private String lineAddressType;


}
