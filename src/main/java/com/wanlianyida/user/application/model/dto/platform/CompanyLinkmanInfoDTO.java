package com.wanlianyida.user.application.model.dto.platform;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("联系人")
public class CompanyLinkmanInfoDTO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty("联系人名称")
    private String linkName;

    @ApiModelProperty("联系人电话")
    private String linkMobile;

    @ApiModelProperty("分类，10议价，20装货，30结算,40常用")
    private Integer linkType;

    @ApiModelProperty("公司id")
    private String companyId;

    @ApiModelProperty("创建人id")
    private String creatorId;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("最后更新人id")
    private String lastUpdaterId;

    @ApiModelProperty("最后更新时间")
    private Date lastUpdateTime;

}
