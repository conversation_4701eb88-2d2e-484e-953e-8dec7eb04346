package com.wanlianyida.user.application.model.query.cont;

import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ContractByLogisticsIdQuery extends ExternalBaseRequest {


    @ApiModelProperty(value = "货源id")
    private String goodsId;

    @ApiModelProperty(value = "订单id")
    private String orderId;

    @ApiModelProperty(value = "合同类型 20-长期合同,40-长期合同补充协议")
    private String contractNature;

}
