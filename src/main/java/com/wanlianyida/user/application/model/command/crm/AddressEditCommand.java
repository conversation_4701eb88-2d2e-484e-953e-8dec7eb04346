package com.wanlianyida.user.application.model.command.crm;

import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class AddressEditCommand extends ExternalBaseRequest {

    /**
     * 地址id
     */
    private String lineId;

    /**
     * 省
     */
    private String sendAddrProvinceName;

    /**
     * 省
     */
    private String sendAddrProvince;

    /**
     * 市
     */
    private String sendAddrCityName;

    /**
     * 市
     */
    private String sendAddrCity;

    /**
     * 区
     */
    private String sendAddrAreaName;

    /**
     * 区
     */
    private String sendAddrArea;

    /**
     * 详细地址
     */
    private String sendAddrDetail;

    /**
     * 收货地址
     */
    private String sendAddrrss;

    /**
     * 联系人
     */
    private String startSendLinker;

    /**
     * 手机号
     */
    private String startSendPhoneNumber;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 地址简称
     */
    private String lineShortName;

    private BigDecimal electronicFenceRadius;
    /**
     * 是否同步变更订单或者货源
     */
    private String changeTag ="10";

    /**
     * 公司id
     */
    private String companyId;

    /**
     * 联系人id
     */
    private String linkmanId;


    //#############################不需要前端传#############################

    /**
     * 地址上联系人
     */
    private String addressLinkman;

    /**
     * 地址上联系电话
     */
    private String addressLinkmanPhone;
}
