package com.wanlianyida.user.application.model.command.platform;

import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class ExternalPlfUsrAgreeOrRefuseBindCommand extends ExternalBaseRequest {

    @NotBlank(message = "企业员工id不能为空")
    private String companyMemberId;

    @NotBlank(message = "绑定类型不能为空:10-同意,20-拒绝")
    private String bindType;

    /**
     * 最后更新人id
     */
    @NotBlank(message = "最后更新人id不能为空")
    private String lastUpdaterId;

    /**
     * 最后更新人姓名
     */
    @NotBlank(message = "最后更新人姓名不能为空")
    private String lastUpdaterName;

}
