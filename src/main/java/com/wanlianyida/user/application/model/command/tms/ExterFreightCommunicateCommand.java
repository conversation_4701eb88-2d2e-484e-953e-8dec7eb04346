package com.wanlianyida.user.application.model.command.tms;

import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 运费催付回复 Command
 * <AUTHOR>
 */
@Data
public class ExterFreightCommunicateCommand extends ExternalBaseRequest {
    @NotBlank(message = "运单号不能为空")
    @ApiModelProperty("运单号")
    private String waybillId;
    @NotBlank(message = "催付回复不能为空")
    @ApiModelProperty("催付回复")
    private String replyContent;
    @NotBlank(message = "回复角色不能为空")
    @ApiModelProperty("回复角色")
    private String replyRole;
    private String userBaseId;
}
