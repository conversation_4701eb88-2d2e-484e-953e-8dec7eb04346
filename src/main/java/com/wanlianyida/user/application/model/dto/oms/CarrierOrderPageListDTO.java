package com.wanlianyida.user.application.model.dto.oms;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单列表
 */
@Data
public class CarrierOrderPageListDTO {

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String orderId;

    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态")
    private String orderStatus;

    /**
     * 托运方公司id
     */
    @ApiModelProperty(value = "托运方公司id")
    private String shipperCompanyId;

    /**
     * 托运方简称
     */
    @ApiModelProperty(value = "托运方简称")
    private String shipperName;

    /**
     * 托运方全称
     */
    @ApiModelProperty(value = "托运方全称")
    private String shipperFullName;

    /**
     * 出发地简称
     */
    @ApiModelProperty(value = "出发地简称")
    private String sendAddrShorthand;

    /**
     * 目的地简称
     */
    @ApiModelProperty(value = "目的地简称")
    private String receiveAddrShorthand;

    /**
     * 货源名称
     */
    @ApiModelProperty(value = "货源名称")
    private String goodsName;

    /**
     * 订单总数量
     */
    @ApiModelProperty(value = "订单总数量")
    private String orderQuantity;

    /**
     * 订单总数量单位
     */
    @ApiModelProperty(value = "订单总数量单位")
    private String orderQuantityUnits;

    /**
     * 货物总重量(吨)
     */
    @ApiModelProperty(value = "货物总重量(吨)")
    private BigDecimal weightSum;

    /**
     * 运输类型
     */
    @ApiModelProperty(value = "运输类型")
    private String transportationType;

    /**
     * 订单金额
     */
    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderAmount;

    /**
     * 要求发货时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @ApiModelProperty(value = "要求发货时间")
    private Date releaseDate;

    /**
     * 要求到货时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @ApiModelProperty(value = "要求到货时间")
    private Date arriveDate;

    /**
     * 订单来源
     */
    @ApiModelProperty(value = "订单来源")
    private String orderSource;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 订单有效期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "订单有效期")
    private Date orderExpireDate;

    /**
     * 货运类型
     */
    @ApiModelProperty(value = "货运类型")
    private String freightType;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customerName;

    /**
     * 客户Id
     */
    @ApiModelProperty(value = "客户Id")
    private String customerId;

    /**
     * 客户类型
     */
    @ApiModelProperty(value = "客户类型")
    private String customerType;

    /**
     * 订单类型
     */
    @ApiModelProperty(value = "订单类型")
    private String orderSourceType;

    /**
     * 转交易的货源号
     */
    @ApiModelProperty(value = "转交易的货源号")
    private String changeDealGoodsId;

    /********* 按钮逻辑字段 *********/

    /**
     * 订单发货状态 21发货未完成，11发货已完成
     */
    @ApiModelProperty(value = "订单发货状态")
    private String orderShippingStatus;

    /**
     * 支付类型：【select:20-月结,10-现结,99-其他支付类型】
     */
    @ApiModelProperty(value = "支付类型")
    private String otherClearType;

    /**
     * 承运方评价状态 ：【select:21-未评价,11-已评价】
     */
    @ApiModelProperty(value = "承运方评价状态")
    private String carrierAppraiseStatus;

    /**
     * 订单拆分类型【select: 0-未拆分,1-拆段,2-拆重】
     */
    @ApiModelProperty(value = "订单拆分类型")
    private String orderSplit;

    /**
     * 订单拆分人的企业id
     */
    @ApiModelProperty(value = "拆单公司Id")
    private String splitCompanyId;

    /**
     * 订单拆分人的类型，1发货方，2承运方;
     */
    @ApiModelProperty(value = "拆单角色")
    private String splitRole;

    /**
     * 拆单类型 null 没有拆单 1横拆,2纵拆
     */
    @ApiModelProperty(value = "拆单类型 null 没有拆单 1横拆,2纵拆")
    private String splitType;

    /**
     * 父级订单状态
     */
    @ApiModelProperty(value = "父级订单状态")
    private String parentOrderStatus;

    /**
     * 装箱数量
     */
    @ApiModelProperty(value = "装箱数量")
    private Integer containerNumber;

    /**
     * 货物拆单剩余重量
     */
    @ApiModelProperty(value = "货物拆单剩余重量")
    private BigDecimal surplusWeightSum;

    /**
     * 实际承运方公司id
     */
    @ApiModelProperty(value = "实际承运方公司id")
    private String realityCarrierCompanyId;

    /**
     * 订单支付状态:1-待付款,2-已付款
     */
    @ApiModelProperty(value = "订单支付状态:1-待付款,2-已付款")
    private String orderPayStatus;

    /**
     * 当前订单拆分方式[0-未拆分,1-拆段,2-拆重]
     */
    @ApiModelProperty(value = "当前订单拆分方式[0-未拆分,1-拆段,2-拆重]")
    private String currentOrderSplitMethod;

    /**
     * 父订单号
     */
    @ApiModelProperty(value = "父订单号")
    private String parentOrderId;

    /**
     * 父订单支付状态
     */
    @ApiModelProperty(value = "父订单支付状态")
    private String parentOrderPayStatus;

    /**
     * 父订单未拆分剩余重量
     */
    @ApiModelProperty(value = "父订单未拆分剩余重量")
    private BigDecimal parentSurplusWeightSum;

    /********* 扩展信息 *********/
    /**
     * POY子订单
     */
    private String downstreamServiceOrderId;

    /**
     * 第三方流程id
     */
    @ApiModelProperty(value = "第三方流程id")
    private String flowId;

    /**
     * 签署流程id
     */
    @ApiModelProperty(value = "签署流程id")
    private String signFlowId;

    /**
     * 流程状态，1：签署中，2：已签署，3：签署失败
     */
    @ApiModelProperty(value = "流程状态，1：签署中，2：已签署，3：签署失败")
    private String flowStatus;

}
