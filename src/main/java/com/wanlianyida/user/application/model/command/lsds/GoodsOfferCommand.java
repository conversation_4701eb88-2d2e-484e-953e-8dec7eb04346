package com.wanlianyida.user.application.model.command.lsds;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class GoodsOfferCommand implements Serializable {


    /**
     * 拆单类型-横拆段 --纵拆大小【1-横拆 默认,2-纵拆】
     */
    private Integer splitType;

    /**
     * 地址Id
     */
    private String goodsAddressId;


    /**
     * 货源编码
     */
    private String goodsId;

    /**
     * 货物拆单id 货源拆单信息表
     */
    private String goodsSplitId;


    /**
     * 公司ID 报价人所属公司ID
     */
    private String companyId;

    /**
     * 报价人名称（公司名称或者简称) 同步过来用于显示
     */
    private String companyName;

    /**
     * 报价人名称（公司名称或者简称) 同步过来用于显示
     */
    private String companyShortName;

    /**
     * 报价轮次
     */
    private Integer offerRound;

    /**
     * 出发地简称
     */
    private String sendAddrShortName;

    /**
     * 出发地省名称
     */
    private String sendAddrProvinceName;

    /**
     * 出发地市名称
     */
    private String sendAddrCityName;

    /**
     * 出发地区/县名称
     */
    private String sendAddrAreaName;

    /**
     * 出发地乡/镇/街道名称
     */
    private String sendAddrStreetName;

    /**
     * 出发地省code
     */
    private String sendAddrProvince;

    /**
     * 出发地市code
     */
    private String sendAddrCity;

    /**
     * 出发地区/县code
     */
    private String sendAddrArea;

    /**
     * 出发地乡/镇/街道code
     */
    private String sendAddrStreet;

    /**
     * 出发地详细地址
     */
    private String sendAddrDetail;

    /**
     * 出发地联系人
     */
    private String sendLinker;

    /**
     * 出发地联系人联系方式
     */
    private String sendPhoneNumber;

    /**
     * 目的地简称
     */
    private String receiveAddrShortName;

    /**
     * 目的地省
     */
    private String receiveAddrProvinceName;

    /**
     * 目的地市
     */
    private String receiveAddrCityName;

    /**
     * 目的地区/县
     */
    private String receiveAddrAreaName;

    /**
     * 目的地乡/镇/街道名称
     */
    private String receiveAddrStreetName;

    /**
     * 目的地省code
     */
    private String receiveAddrProvince;

    /**
     * 目的地市code
     */
    private String receiveAddrCity;
    /**
     * 目的地区/县code
     */
    private String receiveAddrArea;
    /**
     *目的地乡/镇/街道code
     */
    private String receiveAddrStreet;
    /**
     * 目的详细地址
     */
    private String receiveAddrDetail;
    /**
     * 目的地联系人
     */
    private String receiveLinker;
    /**
     * 目的地联系人联系方式
     */
    private String receivePhoneNumber;

    /**
     * 节点排序 分段可以直接拿到直接用来排序显示
     */
    private Integer sortNode;

    /**
     * 运输类型【combox:1-公路集卡,2-公路整车,3-水运,4-铁路】
     */
    private String transportationType;

    /**
     * 基价
     */
    private BigDecimal enquiryTypeBasePrice;

    /**
     * 税率（%）
     */
    private BigDecimal enquiryTypeBaseTaxRate;

    /**
     * 开票价
     */
    private BigDecimal enquiryTypeBaseOpenTicket;

    /**
     * 预计出发时间
     */
    private String releaseDate;
    /**
     * 预计到达时间
     */
    private String arriveDate;

    /**
     * 出发地城市ID存储城市code 最终的ID，上级省市向上推
     */
    private String startSiteCityCode;

    /**
     * 出发地地址简称(冗余为了前端选择用于显示 用处不大）
     */
    private String startSiteCityName;

    /**
     * 出发地详细地址
     */
    private String startSiteAddress;


    /**
     * 出发地发货人联系称谓
     */
    private String startSendLinker;


    /** 出发地联系人手机号码 */
    private String startSendPhoneNumber;

    /**
     * 目的地城市ID存储城市code
     */
    private String endSiteCityCode;

    /**
     * 目的地址简称
     */
    private String endSiteCityName;


    /**
     * 目的地详细地址
     */
    private String endSiteAddress;

    /**
     * 目的地收货人联系称谓
     */
    private String endReceiveLinker;

    /**
     * 目的地收货人联系电话
     */
    private String endReceivePhoneNumber;

    /**
     * 报价有效时间(小时)
     */
    private Integer enquiryOfferTime;

    /**
     * 拆单量
     */
    private String splitAmount;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建人id
     */
    private String createBy;

    /**
     * 创建时间
     */
    private String createDate;

    /**
     * 最后更新人id
     */
    private String modifyBy;

    /**
     * 最后修改时间
     */
    private String modifyDate;

    /**
     * 税率
     */
    private BigDecimal exGoodsRate;

    /**
     * 货源详情中承运商简称（整单报价交易完成时使用）
     */
    private String exCompanyName;

    /**
     * 资源ID(货源ID)
     */
    private String sourceId;


    /**
     * 地址类型【1.货源地址 2.分段地址 3.运输方案地址 4.车源地址】
     */
    private String addressType;

    /** 总数量 */
    private BigDecimal totalQuantity;

    /** 总数量单位 10.吨 20.车 30.柜 */
    private String totalQuantityUnits;

    private String remark;


}
