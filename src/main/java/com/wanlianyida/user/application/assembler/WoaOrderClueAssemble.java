package com.wanlianyida.user.application.assembler;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import com.wanlianyida.user.application.model.command.oms.OrderPushThirdCommand;
import com.wanlianyida.user.application.model.command.woa.GaiaOrderDetailCommand;
import com.wanlianyida.user.application.model.dto.oms.OrderDetailQueryDTO;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * WoaOrderClue转换器
 */
public class WoaOrderClueAssemble {
    private static final Map<Integer, Integer> ROUND_OFF_TYPE_MAP = new HashMap<>();

    static {
        ROUND_OFF_TYPE_MAP.put(10, 0);  // 不抹零
        ROUND_OFF_TYPE_MAP.put(20, 1);  // 个位抹零
        ROUND_OFF_TYPE_MAP.put(30, 2);  // 十位抹零
        ROUND_OFF_TYPE_MAP.put(40, 3);  // 小数抹零
        ROUND_OFF_TYPE_MAP.put(50, 4);  // 五元取整
    }

    private static final Map<Integer, Integer> FIXED_DEDUCTION_TYPE_MAP = new HashMap<>();

    static {
        FIXED_DEDUCTION_TYPE_MAP.put(10, 0);  // 无
        FIXED_DEDUCTION_TYPE_MAP.put(20, 1);  // 固定金额
        FIXED_DEDUCTION_TYPE_MAP.put(30, 2);  // 固定比例
    }

    private static final Map<Integer, Integer> SETTLEMENT_WEIGHT_TYPE_MAP = new HashMap<>();

    static {
        SETTLEMENT_WEIGHT_TYPE_MAP.put(10, 0);  // 按照原发吨数
        SETTLEMENT_WEIGHT_TYPE_MAP.put(20, 1);  // 按照实收吨数
        SETTLEMENT_WEIGHT_TYPE_MAP.put(30, 2);  // 按照原发和实收吨数较小的
    }

    private static final Map<Integer, Integer> DEDUCTION_PRECISION_TYPE_MAP = new HashMap<>();

    static {
        DEDUCTION_PRECISION_TYPE_MAP.put(10, 0);  // 小数点四舍五入
        DEDUCTION_PRECISION_TYPE_MAP.put(20, 1);  // 小数点第三位舍弃
        DEDUCTION_PRECISION_TYPE_MAP.put(30, 2);  // 小数点第四位四舍五入
    }

    private static final Map<Integer, Integer> SETTLEMENT_TYPE_MAP = new HashMap<>();

    static {
        SETTLEMENT_TYPE_MAP.put(10, 0);  // 延迟付
        SETTLEMENT_TYPE_MAP.put(20, 1);  // 分次结算
    }

    private static final Map<Integer, Integer> SHORTFALL_DEDUCTION_TYPE_MAP = new HashMap<>();

    static {
        SHORTFALL_DEDUCTION_TYPE_MAP.put(10, 1);  // 不扣款
        SHORTFALL_DEDUCTION_TYPE_MAP.put(20, 2);  // 亏吨即扣款
        SHORTFALL_DEDUCTION_TYPE_MAP.put(30, 3);  // 超出指定重量
        SHORTFALL_DEDUCTION_TYPE_MAP.put(40, 4);  // 超出原发比例
    }

    private static final Map<String, String> FORWARDING_UNIT_MAP = new HashMap<>();

    static {
        FORWARDING_UNIT_MAP.put("10", "吨");
        FORWARDING_UNIT_MAP.put("20", "吨");
    }

    public static GaiaOrderDetailCommand toGaiaOrderDetailCommand(OrderPushThirdCommand command,
                                                                  OrderDetailQueryDTO orderDetailQueryDTO) {
        if (ObjUtil.isNull(orderDetailQueryDTO)) {
            return null;
        }
        GaiaOrderDetailCommand gaiaOrderDetailCommand = new GaiaOrderDetailCommand();
        // todo 货主ID待确认
        gaiaOrderDetailCommand.setEnterpriseId(command.getNetworkMainBodyId());
        gaiaOrderDetailCommand.setApiOrderId(command.getOrderId());
        gaiaOrderDetailCommand.setCargoType(orderDetailQueryDTO.getGoodsName());
        gaiaOrderDetailCommand.setConsigneeName(orderDetailQueryDTO.getExAddress().getEndReceiveLinker());
        gaiaOrderDetailCommand.setConsigneePhone(orderDetailQueryDTO.getExAddress().getEndReceivePhoneNumber());
        gaiaOrderDetailCommand.setConsignerName(orderDetailQueryDTO.getExAddress().getStartSendLinker());
        gaiaOrderDetailCommand.setConsignerPhone(orderDetailQueryDTO.getExAddress().getStartSendPhoneNumber());
        gaiaOrderDetailCommand.setConsumeRatio(command.getTransportLossQuantity());
        gaiaOrderDetailCommand.setConsumeRatioType(command.getTransportLossType());
        gaiaOrderDetailCommand.setDeliveryAreaCode(orderDetailQueryDTO.getExAddress().getSendAddrArea());
        gaiaOrderDetailCommand.setDeliveryLatitude(Double.parseDouble(orderDetailQueryDTO.getExAddress().getSendAddrLat()));
        gaiaOrderDetailCommand.setDeliveryLongitude(Double.parseDouble(orderDetailQueryDTO.getExAddress().getSendAddrLng()));
        String deliveryPlace = orderDetailQueryDTO.getExAddress().getSendAddrProvinceName() + orderDetailQueryDTO.getExAddress().getSendAddrCityName()
                + orderDetailQueryDTO.getExAddress().getSendAddrAreaName() + orderDetailQueryDTO.getExAddress().getSendAddrDetail();
        gaiaOrderDetailCommand.setDeliveryPlace(deliveryPlace);
        gaiaOrderDetailCommand.setEraseType(ROUND_OFF_TYPE_MAP.get(command.getRoundOffType()));
        gaiaOrderDetailCommand.setFinalSettleAmount(command.getFinalPaymentSettledAmount());
        gaiaOrderDetailCommand.setFixedDeduction(command.getFixedDeductionQuantity());
        gaiaOrderDetailCommand.setFixedDeductionType(FIXED_DEDUCTION_TYPE_MAP.get(command.getFixedDeductionType()));
        gaiaOrderDetailCommand.setForwardingUnit(FORWARDING_UNIT_MAP.get(orderDetailQueryDTO.getOrderQuantityUnits()));
        gaiaOrderDetailCommand.setFreight(command.getExclTaxFreightPrice());
        gaiaOrderDetailCommand.setFreightTonType(SETTLEMENT_WEIGHT_TYPE_MAP.get(command.getSettlementWeightType()));
        gaiaOrderDetailCommand.setGoodsType(orderDetailQueryDTO.getGoodsType());
        //todo 结算单位待确认
        gaiaOrderDetailCommand.setInvoiceType(0);
        // todo 是否开启自动扣除亏吨扣款金额标识 ,0:未开启,1开启
        gaiaOrderDetailCommand.setIsAutoSubShortTonAmt(command.getShortfallDeductionType() == 10 ? 0 : 1);
        gaiaOrderDetailCommand.setIsInsure(1);
        //todo 是否平台单
        gaiaOrderDetailCommand.setIsPlatformOrder(1);
        gaiaOrderDetailCommand.setIsReceipt(1 == command.getIsFinalPaymentSettled());
        gaiaOrderDetailCommand.setLoadDateBegin(DateUtil.parse("2025-09-23"));
        gaiaOrderDetailCommand.setLoadDateEnd(DateUtil.parse("2025-09-27"));
        //todo 装车结算（元）
        gaiaOrderDetailCommand.setLoadSettleAmount(new BigDecimal("0"));
        gaiaOrderDetailCommand.setLossCharge(command.getShortfallDeductionType() != 10);
        gaiaOrderDetailCommand.setLossTonMoneyType(DEDUCTION_PRECISION_TYPE_MAP.get(command.getDeductionPrecisionType()));
        gaiaOrderDetailCommand.setMemo(orderDetailQueryDTO.getRemark());
        gaiaOrderDetailCommand.setOrderNickName(command.getOrderId());
        gaiaOrderDetailCommand.setPaymentType(SETTLEMENT_TYPE_MAP.get(command.getSettlementType()));
        //todo 定向单是否发布到货源列表; 发布:1,不发布:0 默认不发布
        gaiaOrderDetailCommand.setPublishResourceList(0);
        gaiaOrderDetailCommand.setReceiveAreaCode(orderDetailQueryDTO.getExAddress().getReceiveAddrArea());
        gaiaOrderDetailCommand.setReceiveLatitude(Double.parseDouble(orderDetailQueryDTO.getExAddress().getReceiveAddrLat()));
        gaiaOrderDetailCommand.setReceiveLongitude(Double.parseDouble(orderDetailQueryDTO.getExAddress().getReceiveAddrLng()));
        String receivePlace = orderDetailQueryDTO.getExAddress().getReceiveAddrProvinceName() + orderDetailQueryDTO.getExAddress().getReceiveAddrCityName()
                + orderDetailQueryDTO.getExAddress().getReceiveAddrAreaName() + orderDetailQueryDTO.getExAddress().getReceiveAddrDetail();
        gaiaOrderDetailCommand.setReceivePlace(receivePlace);
        gaiaOrderDetailCommand.setSettleDay(command.getSettlementDays());
        gaiaOrderDetailCommand.setShortTonAmtChoiceSet(SHORTFALL_DEDUCTION_TYPE_MAP.get(command.getShortfallDeductionType()));
        gaiaOrderDetailCommand.setSinglePrice(command.getGoodsPrice());
        gaiaOrderDetailCommand.setSlugDeliveryPlace(orderDetailQueryDTO.getExAddress().getSendAddrShorthand());
        gaiaOrderDetailCommand.setSlugReceivePlace(orderDetailQueryDTO.getExAddress().getReceiveAddrShorthand());
        // todo 含税单价状态 0:非含税(默认);1:含税
        gaiaOrderDetailCommand.setTaxFreightFlag(0);
        gaiaOrderDetailCommand.setTotalTon(orderDetailQueryDTO.getWeightSum());
        // todo 货物总价值(元/每车)
        gaiaOrderDetailCommand.setTotalValueAmount(new BigDecimal("0"));
        gaiaOrderDetailCommand.setType(2);
        // todo 卸货费
        gaiaOrderDetailCommand.setUnloadFee(new BigDecimal("0"));
        // todo 卸货单位
        gaiaOrderDetailCommand.setUnloadingCompany("");
        gaiaOrderDetailCommand.setUnLoadSettleAmount(new BigDecimal("0"));
        return gaiaOrderDetailCommand;
    }

}
