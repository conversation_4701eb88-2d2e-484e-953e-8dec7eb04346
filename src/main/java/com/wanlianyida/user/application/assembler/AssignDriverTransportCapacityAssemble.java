package com.wanlianyida.user.application.assembler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.framework.lgicore.utils.JwtUtil;
import com.wanlianyida.tcs.api.model.dto.TcsCompanyDriverCapacityVO;
import com.wanlianyida.tcs.api.model.query.TcsCompanyCarCapacityFilter;
import com.wanlianyida.user.application.model.dto.tcs.AssignDriverTransportCapacityDTO;
import com.wanlianyida.user.application.model.query.tcs.AssignDriverTransportCapacityQuery;
import com.wanlianyida.user.infrastructure.enums.LgiOrchUserEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 发布司机货源查指定司机运力参数转换
 * <AUTHOR>
 * @date 2025/03/11
 */
@Slf4j
public class AssignDriverTransportCapacityAssemble {
    public static PagingInfo<TcsCompanyCarCapacityFilter> changeFilter(PagingInfo<AssignDriverTransportCapacityQuery> pagingInfo) {
        //转换入参
        AssignDriverTransportCapacityQuery filterModel = pagingInfo.getFilterModel();
        TcsCompanyCarCapacityFilter filter = new TcsCompanyCarCapacityFilter();
        if (StrUtil.equals(LgiOrchUserEnum.SelectDriverTypeEnum.PLATFORM.getCode(), filterModel.getSelectDriverType())) {
            // 精确查tcs平台司机
            filter.setCapacityType(LgiOrchUserEnum.ChangeCarTypeEnum.PLATFORM.getCode());
        } else {
            // 模糊查tcs企业id下的自有司机
            filter.setCapacityType(LgiOrchUserEnum.ChangeCarTypeEnum.SELF.getCode());
            filter.setCompanyId(JwtUtil.getCompanyIdByToken());
            filter.setLikeMatch(true);
            filter.setPlateNo(filterModel.getCarPlateNo());
        }
        filter.setDriverName(filterModel.getName());
        filter.setDriverContactPhone(filterModel.getContactPhoneNumber());
        filter.setDriverLicenseNo(filterModel.getIdCardNo());
        PagingInfo<TcsCompanyCarCapacityFilter> query = new PagingInfo<>();
        BeanUtil.copyProperties(pagingInfo, query, CopyOptions.create().ignoreNullValue().ignoreError());
        query.setFilterModel(filter);
        log.info("queryAssignDriverCapacity#changeFilter#query->{}", JSONUtil.toJsonStr(query));
        return query;
    }

    public static ResultMode<AssignDriverTransportCapacityDTO> changeResultMode(ResultMode<TcsCompanyDriverCapacityVO> resultModeExchange) {
        if (!resultModeExchange.isSucceed()) {
            return ResultMode.fail(resultModeExchange.getErrCode(),resultModeExchange.getErrMsg());
        }
        if (IterUtil.isEmpty(resultModeExchange.getModel()) || ObjUtil.isNull(CollUtil.getFirst(resultModeExchange.getModel()))) {
            return ResultMode.success();
        }
        //转换出参：tcs接口返回啥转啥，缺的以后再说
        ResultMode<AssignDriverTransportCapacityDTO> resultMode = new ResultMode<>();
        resultMode.setTotal(resultModeExchange.getTotal());
        List<TcsCompanyDriverCapacityVO> resultList = resultModeExchange.getModel();
        List<AssignDriverTransportCapacityDTO> dtoList = new ArrayList<>();
        resultList.forEach(item -> {
            AssignDriverTransportCapacityDTO dto = new AssignDriverTransportCapacityDTO();
            dto.setDriverId(item.getDriverCode());
            dto.setIdCardNo(item.getLicenseNo());
            dto.setName(item.getDriverName());
            dto.setContactPhoneNumber(item.getDriverContactPhone());
            dto.setUserBaseId(item.getUserBaseId());
            dto.setCarPlateNo(item.getPlateNo());
            dto.setExLicenseType(item.getDrivingLicenseType());
            if (StrUtil.isNotBlank(item.getUserBaseId())) {
                dto.setLoginStatus(LgiOrchUserEnum.DriverLoginStatusEnum.ONLINE.getCode());
            } else {
                dto.setLoginStatus(LgiOrchUserEnum.DriverLoginStatusEnum.OFFLINE.getCode());
            }
            dtoList.add(dto);
        });
        resultMode.setModel(dtoList);
        return resultMode;
    }
}
