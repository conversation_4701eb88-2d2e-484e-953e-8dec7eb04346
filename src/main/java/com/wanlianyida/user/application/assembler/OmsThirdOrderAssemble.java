package com.wanlianyida.user.application.assembler;

import cn.hutool.core.util.ObjUtil;
import com.wanlianyida.user.application.model.command.oms.OrderPushThirdCommand;
import com.wanlianyida.user.application.model.command.oms.SaveThirdOrderCommand;
import com.wanlianyida.user.application.model.dto.oms.OrderDetailQueryDTO;

import java.time.LocalDateTime;

/**
 * 三方订单转换器
 */
public class OmsThirdOrderAssemble {

    public static SaveThirdOrderCommand toSaveThirdOrderCommand(OrderPushThirdCommand command,
                                                                OrderDetailQueryDTO orderDetailQueryDTO,
                                                                String thirdOrderId) {
        if (ObjUtil.isNull(orderDetailQueryDTO)) {
            return null;
        }
        SaveThirdOrderCommand saveThirdOrderCommand = new SaveThirdOrderCommand();
        saveThirdOrderCommand.setOmsOrderId(orderDetailQueryDTO.getOrderId());
        saveThirdOrderCommand.setThirdOrderId(thirdOrderId);
        saveThirdOrderCommand.setNetworkMainBodyId(command.getNetworkMainBodyId());
        saveThirdOrderCommand.setNetworkMainBodyName(command.getNetworkMainBodyName());
        saveThirdOrderCommand.setPushDate(LocalDateTime.now());
        saveThirdOrderCommand.setGoodsId(orderDetailQueryDTO.getGoodsId());
        saveThirdOrderCommand.setGoodsName(orderDetailQueryDTO.getGoodsName());
        saveThirdOrderCommand.setGoodsType(orderDetailQueryDTO.getGoodsType());
        saveThirdOrderCommand.setGoodsWeight(orderDetailQueryDTO.getWeightSum());
        saveThirdOrderCommand.setOrderQuantityUnit(Integer.parseInt(orderDetailQueryDTO.getOrderQuantityUnits()));
        saveThirdOrderCommand.setOrderQuantity(orderDetailQueryDTO.getOrderQuantity());
        saveThirdOrderCommand.setExclTaxFreightPrice(command.getExclTaxFreightPrice());
        saveThirdOrderCommand.setGoodsPrice(command.getGoodsPrice());
        saveThirdOrderCommand.setSendAddrShorthand(orderDetailQueryDTO.getExAddress().getSendAddrShorthand());
        saveThirdOrderCommand.setReceiveAddrShorthand(orderDetailQueryDTO.getExAddress().getReceiveAddrShorthand());
        saveThirdOrderCommand.setSendAddrProvince(orderDetailQueryDTO.getExAddress().getSendAddrProvince());
        saveThirdOrderCommand.setSendAddrCity(orderDetailQueryDTO.getExAddress().getSendAddrCity());
        saveThirdOrderCommand.setSendAddrArea(orderDetailQueryDTO.getExAddress().getSendAddrArea());
        saveThirdOrderCommand.setSendAddrDetail(orderDetailQueryDTO.getExAddress().getSendAddrDetail());
        saveThirdOrderCommand.setStartSendLinker(orderDetailQueryDTO.getExAddress().getStartSendLinker());
        saveThirdOrderCommand.setStartSendPhoneNumber(orderDetailQueryDTO.getExAddress().getStartSendPhoneNumber());
        saveThirdOrderCommand.setReceiveAddrShorthand(orderDetailQueryDTO.getExAddress().getReceiveAddrShorthand());
        saveThirdOrderCommand.setReceiveAddrProvince(orderDetailQueryDTO.getExAddress().getReceiveAddrProvince());
        saveThirdOrderCommand.setReceiveAddrCity(orderDetailQueryDTO.getExAddress().getReceiveAddrCity());
        saveThirdOrderCommand.setReceiveAddrArea(orderDetailQueryDTO.getExAddress().getReceiveAddrArea());
        saveThirdOrderCommand.setReceiveAddrDetail(orderDetailQueryDTO.getExAddress().getReceiveAddrDetail());
        saveThirdOrderCommand.setEndReceiveLinker(orderDetailQueryDTO.getExAddress().getEndReceiveLinker());
        saveThirdOrderCommand.setEndReceivePhoneNumber(orderDetailQueryDTO.getExAddress().getEndReceivePhoneNumber());
        saveThirdOrderCommand.setSendAddrProvinceName(orderDetailQueryDTO.getExAddress().getSendAddrProvinceName());
        saveThirdOrderCommand.setSendAddrCityName(orderDetailQueryDTO.getExAddress().getSendAddrCityName());
        saveThirdOrderCommand.setSendAddrAreaName(orderDetailQueryDTO.getExAddress().getSendAddrAreaName());
        saveThirdOrderCommand.setReceiveAddrProvinceName(orderDetailQueryDTO.getExAddress().getReceiveAddrProvinceName());
        saveThirdOrderCommand.setReceiveAddrCityName(orderDetailQueryDTO.getExAddress().getReceiveAddrCityName());
        saveThirdOrderCommand.setReceiveAddrAreaName(orderDetailQueryDTO.getExAddress().getReceiveAddrAreaName());
        saveThirdOrderCommand.setSettlementType(command.getSettlementType());
        saveThirdOrderCommand.setSettlementWeightType(command.getSettlementWeightType());
        saveThirdOrderCommand.setIsFinalPaymentSettled(command.getIsFinalPaymentSettled());
        saveThirdOrderCommand.setFinalPaymentSettledAmount(command.getFinalPaymentSettledAmount());
        saveThirdOrderCommand.setTransportLossType(command.getTransportLossType());
        saveThirdOrderCommand.setTransportLossQuantity(command.getTransportLossQuantity());
        saveThirdOrderCommand.setRoundOffType(command.getRoundOffType());
        saveThirdOrderCommand.setFixedDeductionType(command.getFixedDeductionType());
        saveThirdOrderCommand.setFixedDeductionQuantity(command.getFixedDeductionQuantity());
        saveThirdOrderCommand.setFixedDeductionReason(command.getFixedDeductionReason());
        saveThirdOrderCommand.setShortfallDeductionType(command.getShortfallDeductionType());
        saveThirdOrderCommand.setShortfallDeductionQuantity(command.getShortfallDeductionQuantity());
        saveThirdOrderCommand.setDeductionPrecisionType(command.getDeductionPrecisionType());
        return saveThirdOrderCommand;
    }

}
