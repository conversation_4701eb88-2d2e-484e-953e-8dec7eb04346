package com.wanlianyida.user.application.assembler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import com.wanlianyida.crm.api.model.dto.CrmCompanyLineAddressDTO;
import com.wanlianyida.crm.api.model.dto.CrmCustomerDTO;
import com.wanlianyida.lsds.api.model.command.publish.GoodsLineCommand;
import com.wanlianyida.lsds.api.model.command.publish.PublishGoodsSourceCommand;
import com.wanlianyida.platform.api.model.dto.PlatformCmOperationMainBodyDetailDTO;
import com.wanlianyida.platform.api.model.dto.PlatformUmCompanyDTO;
import com.wanlianyida.user.application.model.command.lsds.publish.GoodsSourcePublishCommand;
import com.wanlianyida.user.infrastructure.enums.BusinessScenarioEnum;

/**
 * 货源转换器
 *
 * <AUTHOR>
 */
public class GoodsSourceAssembler {


    /**
     * 创建发布货源Command
     *
     * @param command
     * @param line
     * @param startAddress
     * @param endAddress
     * @param customer
     * @param company
     * @return
     */
    public static PublishGoodsSourceCommand createPublishGoodsSourceCommand(GoodsSourcePublishCommand command, CrmCompanyLineAddressDTO line, CrmCompanyLineAddressDTO startAddress, CrmCompanyLineAddressDTO endAddress, CrmCustomerDTO customer, PlatformUmCompanyDTO company, PlatformCmOperationMainBodyDetailDTO mainBody) {
        PublishGoodsSourceCommand publishGoodsSourceCommand = BeanUtil.toBean(command, PublishGoodsSourceCommand.class);
        //公司信息
        publishGoodsSourceCommand.setCompanyId(company.getCompanyId());
        publishGoodsSourceCommand.setCompanyShortName(company.getCompanyShortName());
        //线路信息
        GoodsLineCommand goodsLine = generateGoodsLine(line, startAddress, endAddress);
        publishGoodsSourceCommand.setGoodsLine(BeanUtil.toBean(goodsLine, GoodsLineCommand.class));
        //客户信息
        publishGoodsSourceCommand.setCustomerType(customer.getCustomerType());
        publishGoodsSourceCommand.setCustomerName(customer.getCustomerName());
        //关键类型
        BusinessScenarioEnum businessScenario = BusinessScenarioEnum.businessScenarioMap.get(command.getBusinessScenario());
        publishGoodsSourceCommand.setPublisherType(businessScenario.getPublisherType());
        publishGoodsSourceCommand.setBizModelType(businessScenario.getBizModelType());

        if (ObjUtil.isNotNull(mainBody)) {
            publishGoodsSourceCommand.setNetworkMainBodyName(mainBody.getBodyName());
        }
        publishGoodsSourceCommand.setReleaseType(businessScenario.getReleaseType());
        publishGoodsSourceCommand.setSourceEntryType(businessScenario.getSourceEntryType());
        return publishGoodsSourceCommand;
    }

    /**
     * 货源线路转换
     *
     * @param line
     * @param startAddress
     * @param endAddress
     * @return
     */
    public static GoodsLineCommand generateGoodsLine(CrmCompanyLineAddressDTO line, CrmCompanyLineAddressDTO startAddress, CrmCompanyLineAddressDTO endAddress) {
        GoodsLineCommand goodsLine = GoodsLineCommand.builder().addressType("1")
                //线路信息
                .lineId(line.getLineId())
                .sendAddrId(line.getSendAddrId())
                .receiveAddrId(line.getReceiveAddrId())
                .transportMileage(line.getTransportMileage())
                .takeUpTime(line.getTakeUpTime())

                //出发地信息
                .sendAddrShortName(startAddress.getLineShortName())
                .sendAddrProvince(startAddress.getSendAddrProvince())
                .sendAddrCity(startAddress.getSendAddrCity())
                .sendAddrArea(startAddress.getSendAddrArea())
                .sendAddrStreet(startAddress.getSendAddrStreet())
                .sendAddrProvinceName(startAddress.getSendAddrProvinceName())
                .sendAddrCityName(startAddress.getSendAddrCityName())
                .sendAddrAreaName(startAddress.getSendAddrAreaName())
                .sendAddrStreetName(startAddress.getSendAddrStreetName())
                .sendAddrDetail(startAddress.getSendAddrDetail())
                .sendLinker(startAddress.getStartSendLinker())
                .sendPhoneNumber(startAddress.getStartSendPhoneNumber())
                .sendLongitude(startAddress.getItem1())
                .sendLatitude(startAddress.getItem2())
                .sendAddrId(startAddress.getLineId())



                //目的地信息
                .receiveAddrShortName(endAddress.getLineShortName())
                .receiveAddrProvince(endAddress.getSendAddrProvince())
                .receiveAddrCity(endAddress.getSendAddrCity())
                .receiveAddrArea(endAddress.getSendAddrArea())
                .receiveAddrStreet(endAddress.getSendAddrStreet())
                .receiveAddrProvinceName(endAddress.getSendAddrProvinceName())
                .receiveAddrCityName(endAddress.getSendAddrCityName())
                .receiveAddrAreaName(endAddress.getSendAddrAreaName())
                .receiveAddrStreetName(endAddress.getSendAddrStreetName())
                .receiveAddrDetail(endAddress.getSendAddrDetail())
                .receiveLinker(endAddress.getStartSendLinker())
                .receivePhoneNumber(endAddress.getStartSendPhoneNumber())
                .receiveLongitude(endAddress.getItem1())
                .receiveLatitude(endAddress.getItem2())
                .receiveAddrId(endAddress.getLineId())
                .build();
        return goodsLine;
    }

}
