package com.wanlianyida.user.application.assembler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.framework.lgicore.utils.JwtUtil;
import com.wanlianyida.tcs.api.model.dto.TcsCompanyDriverCapacityVO;
import com.wanlianyida.tcs.api.model.query.TcsCompanyCarCapacityFilter;
import com.wanlianyida.user.application.model.dto.tcs.DriverTransportCapacityDTO;
import com.wanlianyida.user.application.model.query.tcs.DriverTransportCapacityQuery;
import com.wanlianyida.user.infrastructure.enums.LgiOrchUserEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 派车司机运力参数转换
 * <AUTHOR>
 * @date 2025/03/11
 */
@Slf4j
public class DriverTransportCapacityAssemble {
    public static PagingInfo<TcsCompanyCarCapacityFilter> changeFilter(PagingInfo<DriverTransportCapacityQuery> pagingInfo) {
        //转换入参
        DriverTransportCapacityQuery filterModel = pagingInfo.getFilterModel();
        TcsCompanyCarCapacityFilter filter = new TcsCompanyCarCapacityFilter();
        if (StrUtil.isBlank(filterModel.getSelectDriverType())) {
            filterModel.setSelectDriverType(LgiOrchUserEnum.SelectDriverTypeEnum.SELF.getCode());
        }
        if (StrUtil.equals(LgiOrchUserEnum.SelectDriverTypeEnum.PLATFORM.getCode(), filterModel.getSelectDriverType())) {
            // 精确查tcs平台司机
            filter.setCapacityType(LgiOrchUserEnum.ChangeCarTypeEnum.PLATFORM.getCode());
        } else {
            // 模糊查tcs企业id下的自有司机
            filter.setCapacityType(LgiOrchUserEnum.ChangeCarTypeEnum.SELF.getCode());
            filter.setCompanyId(JwtUtil.getCompanyIdByToken());
            filter.setLikeMatch(true);
        }
        // 正则处理filterModel.getName()是数字开头就按手机号否则按姓名
        if (ReUtil.isMatch("^\\d+", filterModel.getName())) {
            filter.setDriverContactPhone(filterModel.getName());
        } else {
            filter.setDriverName(filterModel.getName());
        }
        PagingInfo<TcsCompanyCarCapacityFilter> query = new PagingInfo<>();
        BeanUtil.copyProperties(pagingInfo, query, CopyOptions.create().ignoreNullValue().ignoreError());
        query.setFilterModel(filter);
        log.info("queryDriversCapacity#changeFilter#query->{}", JSONUtil.toJsonStr(query));
        return query;
    }

    public static ResultMode<DriverTransportCapacityDTO> changeResultMode(ResultMode<TcsCompanyDriverCapacityVO> resultModeExchange) {
        if (!resultModeExchange.isSucceed()) {
            return ResultMode.fail(resultModeExchange.getErrCode(),resultModeExchange.getErrMsg());
        }
        if (IterUtil.isEmpty(resultModeExchange.getModel()) || ObjUtil.isNull(CollUtil.getFirst(resultModeExchange.getModel()))) {
            return ResultMode.success();
        }
        //转换出参：tcs接口返回啥转啥，缺的以后再说
        ResultMode<DriverTransportCapacityDTO> resultMode = new ResultMode<>();
        resultMode.setTotal(resultModeExchange.getTotal());
        List<TcsCompanyDriverCapacityVO> resultList = resultModeExchange.getModel();
        List<DriverTransportCapacityDTO> dtoList = new ArrayList<>();
        resultList.forEach(item -> {
            DriverTransportCapacityDTO dto = new DriverTransportCapacityDTO();
            dto.setDriverId(item.getDriverCode());
            dto.setContactPhoneNumber(item.getDriverContactPhone());
            dto.setDriverName(item.getDriverName());
            dto.setIdCardNo(item.getLicenseNo());
            dto.setUserBaseId(item.getUserBaseId());
            dto.setCompanyId(item.getCompanyId());
            if (StrUtil.isNotBlank(item.getUserBaseId())) {
                dto.setLoginStatus(LgiOrchUserEnum.DriverLoginStatusEnum.ONLINE.getCode());
            } else {
                dto.setLoginStatus(LgiOrchUserEnum.DriverLoginStatusEnum.OFFLINE.getCode());
            }
            dtoList.add(dto);
        });
        resultMode.setModel(dtoList);
        return resultMode;
    }
}
