package com.wanlianyida.user.application.assembler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.framework.lgicore.utils.JwtUtil;
import com.wanlianyida.tcs.api.model.dto.TcsCompanyCarCapacityVO;
import com.wanlianyida.tcs.api.model.query.TcsCompanyCarCapacityFilter;
import com.wanlianyida.tms.api.model.dto.TransportCarDTO;
import com.wanlianyida.tms.api.model.query.CarTransportCapacityQuery;
import com.wanlianyida.user.infrastructure.enums.LgiOrchUserEnum;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 派车车辆运力参数转换
 * <AUTHOR>
 * @date 2025/03/11
 */
@Slf4j
public class CarTransportCapacityAssemble {
    public static PagingInfo<TcsCompanyCarCapacityFilter> changeFilter(PagingInfo<CarTransportCapacityQuery> pagingInfo) {
        //转换入参
        CarTransportCapacityQuery filterModel = pagingInfo.getFilterModel();
        TcsCompanyCarCapacityFilter filter = new TcsCompanyCarCapacityFilter();
        filter.setCapacityType(LgiOrchUserEnum.ChangeCarTypeEnum.SELF.getCode());
        if (StrUtil.equals(LgiOrchUserEnum.SearchCarTypeEnum.PLATFORM.getCode(),filterModel.getCapacityIdentification())) {
            filter.setCapacityType(LgiOrchUserEnum.ChangeCarTypeEnum.PLATFORM.getCode());
        }
        if (StrUtil.equals(LgiOrchUserEnum.SearchCarTypeEnum.SELF.getCode(),filterModel.getCapacityIdentification())) {
            filter.setCapacityType(LgiOrchUserEnum.ChangeCarTypeEnum.SELF.getCode());
            filter.setCompanyId(JwtUtil.getCompanyIdByToken());
        }
        if (StrUtil.equals(LgiOrchUserEnum.SearchCarTypeEnum.SUPPLEMENT.getCode(),filterModel.getCapacityIdentification())) {
            filter.setCapacityType(LgiOrchUserEnum.ChangeCarTypeEnum.SELF.getCode());
            filter.setCompanyId(JwtUtil.getCompanyIdByToken());
            String carPlateNo = filterModel.getCarPlateNo();
            if (StrUtil.isNotBlank(carPlateNo) ){
                filter.setCapacityType(LgiOrchUserEnum.ChangeCarTypeEnum.PLATFORM.getCode());
                filter.setCompanyId(null);
            }
        }
        // 精确查
        filter.setPlateNo(filterModel.getCarPlateNo());
        PagingInfo<TcsCompanyCarCapacityFilter> query = new PagingInfo<>();
        BeanUtil.copyProperties(pagingInfo, query, CopyOptions.create().ignoreNullValue().ignoreError());
        query.setFilterModel(filter);
        log.info("queryCompanyCarCapacity#changeFilter#query->{}", JSONUtil.toJsonStr(query));
        return query;
    }

    public static ResultMode<TransportCarDTO> changeResultMode(ResultMode<TcsCompanyCarCapacityVO> resultModeExchange) {
        if (!resultModeExchange.isSucceed()) {
            return ResultMode.fail(resultModeExchange.getErrCode(),resultModeExchange.getErrMsg());
        }
        if (IterUtil.isEmpty(resultModeExchange.getModel()) || ObjUtil.isNull(CollUtil.getFirst(resultModeExchange.getModel()))) {
            return ResultMode.success();
        }
        //转换出参：tcs接口返回啥转啥，缺的以后再说
        ResultMode<TransportCarDTO> resultMode = new ResultMode<>();
        resultMode.setTotal(resultModeExchange.getTotal());
        List<TcsCompanyCarCapacityVO> resultList = resultModeExchange.getModel();
        List<TransportCarDTO> transportCarDTOList = new ArrayList<>();
        resultList.forEach(item -> {
            TransportCarDTO transportCarDTO = new TransportCarDTO();
            transportCarDTO.setCarPlateNo(item.getPlateNo());
            transportCarDTO.setCarColor(item.getPlateColor());
            transportCarDTO.setBussRelations(item.getCarRelationType());
            transportCarDTO.setCarStatus(LgiOrchUserEnum.ChangeCarStatusEnum.getCodeByTcsCode(item.getCarRelationStatus()));
            transportCarDTO.setCarType(item.getCarType());
            transportCarDTO.setCarNormLoad(item.getVerifyLoadWeight());
            transportCarDTO.setTractionQuality(item.getTractionWeight());
            if (item.getCarLength() != null) {
                transportCarDTO.setCarLength(BigDecimal.valueOf(item.getCarLength()));
            }
            if (item.getCarWidth() != null) {
                transportCarDTO.setCarWidth(BigDecimal.valueOf(item.getCarWidth()));
            }
            if (item.getCarHeight() != null) {
                transportCarDTO.setCarHeight(BigDecimal.valueOf(item.getCarHeight()));
            }
            transportCarDTO.setDefaultTrailerCurbWeight(item.getDefaultTrailerCurbWeight());
            transportCarDTO.setDefaultTrailer(item.getTrailerPlateNo());
            if (item.getTrailerPlateColor() != null) {
                transportCarDTO.setDefaultTrailerCarColor(String.valueOf(item.getTrailerPlateColor()));
            }
            transportCarDTO.setDriverId(item.getDriverCode());
            transportCarDTO.setDriverName(item.getDriverName());
            transportCarDTO.setContactPhoneNumber(item.getDriverContactPhone());
            transportCarDTO.setIdCardNo(item.getLicenseNo());
            if (StrUtil.isNotBlank(item.getUserBaseId())) {
                transportCarDTO.setLoginStatus(LgiOrchUserEnum.DriverLoginStatusEnum.ONLINE.getCode());
            } else {
                transportCarDTO.setLoginStatus(LgiOrchUserEnum.DriverLoginStatusEnum.OFFLINE.getCode());
            }
            transportCarDTO.setCompanyId(item.getCompanyId());
            transportCarDTO.setUserBaseId(item.getUserBaseId());
            transportCarDTO.setIsDefault(item.getIsDefault());
            transportCarDTOList.add(transportCarDTO);
        });
        resultMode.setModel(transportCarDTOList);
        return resultMode;
    }
}
