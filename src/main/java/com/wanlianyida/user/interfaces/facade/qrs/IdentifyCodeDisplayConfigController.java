package com.wanlianyida.user.interfaces.facade.qrs;

import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.command.qrs.IdentifyCodeDisplayConfigCommand;
import com.wanlianyida.user.application.model.dto.qrs.IdentifyCodeDisplayConfigDTO;
import com.wanlianyida.user.application.model.query.qrs.IdentifyCodeDisplayConfigByShareCodeQuery;
import com.wanlianyida.user.application.model.query.qrs.IdentifyCodeDisplayConfigQuery;
import com.wanlianyida.user.application.service.qrs.IdentifyCodeDisplayConfigAppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年07月03日 13:07
 */
@Api("二维码显示配置")
@RestController
@RequestMapping("/identify-code-display-config")
public class IdentifyCodeDisplayConfigController {

    @Resource
    private IdentifyCodeDisplayConfigAppService identifyCodeDisplayConfigAppService;

    @ApiOperation("基于分享码查询")
    @PostMapping("/query-by-share-code")
    public ResultMode<IdentifyCodeDisplayConfigDTO> queryByShareCode(@RequestBody IdentifyCodeDisplayConfigByShareCodeQuery query) {
        return identifyCodeDisplayConfigAppService.queryByShareCode(query);
    }

    @ApiOperation("查询")
    @PostMapping("/query")
    ResultMode<IdentifyCodeDisplayConfigDTO> query(@RequestBody IdentifyCodeDisplayConfigQuery query) {
        return identifyCodeDisplayConfigAppService.query(query);
    }

    @ApiOperation("保存")
    @PostMapping("/insert")
    ResultMode<String> insert(@RequestBody IdentifyCodeDisplayConfigCommand command) {
        return identifyCodeDisplayConfigAppService.insert(command);
    }
}
