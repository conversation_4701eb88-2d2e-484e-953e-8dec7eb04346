package com.wanlianyida.user.interfaces.facade.tms;

import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.command.tms.ExterEndAddressChangeCommand;
import com.wanlianyida.user.application.model.query.tms.ExterLatestChangeRecordQuery;
import com.wanlianyida.user.application.service.tms.WaybillChangeRecordAppService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 运单修改申请记录表
 */
@RequestMapping("/waybill-change-record")
@Slf4j
@RestController
public class WaybillChangeRecordController {

    @Resource
    private WaybillChangeRecordAppService waybillChangeRecordAppService;

    /**
     * 新增运单目的地修改
     */
    @PostMapping(value = "/add-end-address-change")
    public ResultMode addEndAddressChange(@RequestBody ExterEndAddressChangeCommand command) {
        return waybillChangeRecordAppService.addEndAddressChange(command);
    }

    /**
     * 获取运单变更最近的一条提交记录
     * 更具审核类型
     */
    @PostMapping(value = "/latest-change-record")
    public ResultMode getLatestChangeRecord(@RequestBody @Validated ExterLatestChangeRecordQuery query) {
        return waybillChangeRecordAppService.getLatestChangeRecord(query);
    }


}
