package com.wanlianyida.user.interfaces.facade.lsds;

import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.query.lsds.RoundingModeQuery;
import com.wanlianyida.user.application.service.lsds.GoodsConfigAppService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

import javax.annotation.Resource;

@Slf4j
@RequestMapping("/goods-config")
@RestController
public class GoodsConfigController {

    @Resource
    private GoodsConfigAppService goodsConfigAppService;

    /**
     * 浮动配置
     * @param query
     * @return
     */
    @PostMapping("/rounding-mode-query")
    public ResultMode<Map<String, Integer>> roundingModeQuery(@RequestBody RoundingModeQuery query){
        Map<String, Integer> config = goodsConfigAppService.roundingModeQuery(query);
        return ResultMode.success(config);
    }
}
