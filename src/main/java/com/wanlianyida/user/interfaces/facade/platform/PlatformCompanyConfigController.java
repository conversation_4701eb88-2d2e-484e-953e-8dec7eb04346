package com.wanlianyida.user.interfaces.facade.platform;

import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.platform.api.model.query.CompanyConfigQuery;
import com.wanlianyida.user.application.service.platform.PlatformCompanyConfigAppService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

import javax.annotation.Resource;

/**
 * 平台公司配置
 */
@RequestMapping("/companyConfig")
@RestController
public class PlatformCompanyConfigController {

    @Resource
    private PlatformCompanyConfigAppService platformCompanyConfigAppService;

    /**
     * 获取平台企业配置表
     */
    @PostMapping("/selectConfigByCompanyId")
    public ResultMode<Map<String, Object>> selectConfigByCompanyId(@Validated @RequestBody CompanyConfigQuery query) {
        return platformCompanyConfigAppService.selectConfigByCompanyId(query);
    }

}
