package com.wanlianyida.user.interfaces.facade.bi;

import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.dto.bi.WorkbenchStatisticsDTO;
import com.wanlianyida.user.application.model.query.bi.BiMetadataQuery;
import com.wanlianyida.user.application.model.query.bi.WorkbenchStatisticsQuery;
import com.wanlianyida.user.application.service.bi.WorkbenchStatisticsAppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年06月16日 16:34
 */
@Api("工作台数据统计")
@RestController
@RequestMapping("/workbench")
public class WorkbenchStatisticsController {

    @Resource
    private WorkbenchStatisticsAppService workbenchStatisticsAppService;

    @ApiOperation("数据统计")
//    @PostMapping("/statistics")
    public ResultMode<?> statistics(@RequestBody BiMetadataQuery query) {
        WorkbenchStatisticsDTO result = workbenchStatisticsAppService.statistics(query);
        return ResultMode.success(result);
    }


    @ApiOperation("数据统计")
    @PostMapping("/statistics-v2")
    public ResultMode<?> statisticsV2(@RequestBody WorkbenchStatisticsQuery query) {
        return workbenchStatisticsAppService.statisticsV2(query);
    }
}
