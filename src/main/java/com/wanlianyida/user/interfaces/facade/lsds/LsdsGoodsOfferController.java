package com.wanlianyida.user.interfaces.facade.lsds;

import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.command.lsds.OfferAddCommand;
import com.wanlianyida.user.application.model.dto.lsds.OfferDetailsQueryDTO;
import com.wanlianyida.user.application.model.dto.lsds.OfferGoodsListQueryDTO;
import com.wanlianyida.user.application.model.dto.lsds.OfferListQueryDTO;
import com.wanlianyida.user.application.model.query.lsds.OfferDetailsQuery;
import com.wanlianyida.user.application.model.query.lsds.OfferGoodsListQuery;
import com.wanlianyida.user.application.model.query.lsds.OfferListQuery;
import com.wanlianyida.user.application.service.lsds.LsdsGoodsOfferAppService;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 企业货源报价
 */
@Slf4j
@RequestMapping("/lsds-goods-offer")
@RestController
public class LsdsGoodsOfferController {


    @Resource
    private LsdsGoodsOfferAppService lsdsGoodsOfferAppService;

    /**
     * 报价单列表
     */
     @PostMapping("/offer-list-query")
     public ResultMode<OfferListQueryDTO> offerListQuery(@RequestBody PagingInfo<OfferListQuery> pageInfo){
        return lsdsGoodsOfferAppService.offerListQuery(pageInfo);
    }

    /**
     * 查询报价货源详情
     */
    @PostMapping("/offer-details-query")
    public ResultMode<OfferDetailsQueryDTO> offerDetailsQuery(@RequestBody @Validated OfferDetailsQuery query){
        return lsdsGoodsOfferAppService.offerDetailsQuery(query);
    }


    /**
     * 我要承运-货源列表
     */
    @PostMapping("/offer-goods-list-query")
    public ResultMode<OfferGoodsListQueryDTO> offerGoodsListQuery(@RequestBody PagingInfo<OfferGoodsListQuery> pageInfo){
        return lsdsGoodsOfferAppService.offerGoodsListQuery(pageInfo);
    }

    /**
     * 新增报价单
     * @param command
     * @return
     */
    @RequestMapping(value = "/offer-add", produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
    public ResultMode offerAdd(@RequestBody @ApiParam OfferAddCommand command){
        lsdsGoodsOfferAppService.offerAdd(command);
        return ResultMode.success();
    }
}
