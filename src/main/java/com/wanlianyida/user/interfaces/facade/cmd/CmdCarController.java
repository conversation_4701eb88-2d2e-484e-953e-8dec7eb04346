package com.wanlianyida.user.interfaces.facade.cmd;

import com.wanlianyida.cmd.api.model.dto.CmdCarDTO;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.query.cmd.ExterCmdCarQuery;
import com.wanlianyida.user.application.service.cmd.CmdCarAppService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/cmd/cmd-car")
public class CmdCarController {

    @Resource
    private CmdCarAppService cmdCarAppService;

    /**
     * 模糊查询车牌信息
     */
    @PostMapping("/cmd-car-page")
    public ResultMode<CmdCarDTO> cmdCarPage(@RequestBody PagingInfo<ExterCmdCarQuery> pagingInfo){
        return cmdCarAppService.cmdCarPage(pagingInfo);
    }


}
