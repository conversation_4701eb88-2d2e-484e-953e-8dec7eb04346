package com.wanlianyida.user.interfaces.facade.crm;

import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.framework.lgicore.utils.JwtUtil;
import com.wanlianyida.user.application.model.command.crm.AddressAddCommand;
import com.wanlianyida.user.application.model.command.crm.AddressDelAndAddCommand;
import com.wanlianyida.user.application.model.command.crm.AddressDelCommand;
import com.wanlianyida.user.application.model.command.crm.AddressEditCommand;
import com.wanlianyida.user.application.model.dto.crm.AddressAddDTO;
import com.wanlianyida.user.application.model.dto.crm.AddressDelAndAddCheckDTO;
import com.wanlianyida.user.application.model.dto.crm.AddressListDTO;
import com.wanlianyida.user.application.model.dto.crm.AssociationLineDTO;
import com.wanlianyida.user.application.model.query.crm.AddressCanEditCheckQuery;
import com.wanlianyida.user.application.model.query.crm.AddressListQuery;
import com.wanlianyida.user.application.model.query.crm.AssociationLineQuery;
import com.wanlianyida.user.application.service.crm.CrmAddressAppService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * crm 地址 相关 controller
 */
@RequestMapping("/crm/address")
@RestController
public class CrmAddressController {

    @Resource
    private CrmAddressAppService crmAddressAppService;

    /**
     * 地址是否可以编辑校验
     * @return
     */
    @PostMapping("/can-edit-check")
    public ResultMode addressCanEditCheck(@RequestBody @Validated AddressCanEditCheckQuery query){
        return crmAddressAppService.addressCanEditCheck(query);
    }

    /**
     * 常用地址列表
     * @return
     */
    @PostMapping("/list-query")
    public ResultMode<AddressListDTO> addressListQuery(@RequestBody PagingInfo<AddressListQuery> query){
        AddressListQuery filterModel = query.getFilterModel();
        filterModel.setCompanyId(JwtUtil.getCompanyIdByToken());
        filterModel.setStatus("10");
        return crmAddressAppService.addressListQuery(query);
    }

    /**
     * 常用地址新增
     * @return
     */
    @PostMapping("/add-address")
    public ResultMode<AddressAddDTO> addressAdd(@RequestBody AddressAddCommand command){
        return crmAddressAppService.addressAdd(command);
    }

    /**
     * 地址编辑
     * @return
     */
    @PostMapping("/edit-address")
    public ResultMode addressEdit(@RequestBody @Validated AddressEditCommand command){
        return crmAddressAppService.addressEdit(command);
    }


    /**
     * 查看是否已经关联了线路
     * @return
     */
    @PostMapping("/query-association-line")
    public ResultMode<AssociationLineDTO> queryAssociationLine(@RequestBody @Validated AssociationLineQuery query){
        return crmAddressAppService.queryAssociationLine(query);
    }


    /**
     * 地址删除
     * @return
     */
    @PostMapping("/del-address")
    public ResultMode addressDel(@RequestBody @Validated AddressDelCommand command){
        return crmAddressAppService.addressDel(command);
    }

    /**
     * 地址删除并且新增前检查
     * @return
     */
    @PostMapping("/del-and-add-check")
    public ResultMode<AddressDelAndAddCheckDTO> addressDelAndAddCheck(@RequestBody @Validated AddressDelCommand command){
        return crmAddressAppService.addressDelAndAddCheck(command);
    }

    /**
     * 地址删除并且新增
     * @return
     */
    @PostMapping("/del-and-add")
    public ResultMode<AddressAddDTO> addressDelAndAdd(@RequestBody AddressDelAndAddCommand command){
        return crmAddressAppService.addressDelAndAdd(command);
    }

}
