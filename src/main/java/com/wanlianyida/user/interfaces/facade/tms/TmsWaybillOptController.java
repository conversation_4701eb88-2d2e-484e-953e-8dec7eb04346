package com.wanlianyida.user.interfaces.facade.tms;

import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.tms.api.model.command.TmsWaybillUpdCommand;
import com.wanlianyida.user.application.service.tms.TmsWaybillOptAppService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 运单操作
 */
@RestController
@RequestMapping("/tmsWaybillOpt")
public class TmsWaybillOptController {

    @Resource
    private TmsWaybillOptAppService tmsWaybillOptAppService;

    /**
     * 更新运单信息
     */
    @PostMapping("/updateWaybillInfo")
    public ResultMode updateWaybillInfo(@RequestBody @Validated TmsWaybillUpdCommand command) {
        return tmsWaybillOptAppService.updateWaybillInfo(command);
    }

}
