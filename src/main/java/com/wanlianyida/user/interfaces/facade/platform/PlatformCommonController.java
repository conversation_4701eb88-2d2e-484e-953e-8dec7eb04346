package com.wanlianyida.user.interfaces.facade.platform;

import cn.hutool.json.JSONUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.dto.platform.PlatformCityListDTO;
import com.wanlianyida.user.application.model.dto.platform.PlatformDictionaryDTO;
import com.wanlianyida.user.application.model.dto.platform.PlatformParameterDTO;
import com.wanlianyida.user.application.model.dto.platform.TonAfterPointDTO;
import com.wanlianyida.user.application.model.query.platform.DictQuery;
import com.wanlianyida.user.application.model.query.platform.PlatformCityListQuery;
import com.wanlianyida.user.application.model.query.platform.PlatformParameterQuery;
import com.wanlianyida.user.application.service.platform.PlatformCommonAppService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 * 平台字典 controller
 */
@Slf4j
@RestController
@RequestMapping(value = "/plat-common")
public class PlatformCommonController {

    @Resource
    private PlatformCommonAppService platformCommonAppService;

    /**
     * 字典值查询
     * @param query
     * @return
     */
    @PostMapping("/dict-query")
    public ResultMode<Map<String, List<PlatformDictionaryDTO>>> dictQuery(@RequestBody DictQuery query){
        log.info("query#请求参数：{}", JSONUtil.toJsonStr(query));
        Map<String, List<PlatformDictionaryDTO>> listMap = platformCommonAppService.dictQuery(query);
        return ResultMode.success(listMap);
    }

    /**
     * 数值保留小数后位位数
     * @return
     */
    @PostMapping("/ton-after-Point-query")
    public ResultMode<TonAfterPointDTO> tonAfterPointQuery(){
        List<TonAfterPointDTO> tonAfterPointDTOS = platformCommonAppService.tonAfterPointQuery();
        return ResultMode.success(tonAfterPointDTOS);
    }


    /**
     * 城市列表查询
     * @param query
     * @return
     */
    @PostMapping("/city-list-query")
    public ResultMode<PlatformCityListDTO> cityListQuery(@RequestBody PlatformCityListQuery query){
        List<PlatformCityListDTO> platformCityListDTOS = platformCommonAppService.cityListQuery(query);
        return ResultMode.success(platformCityListDTOS);
    }

    /**
     * 平台参数查询
     * @param query
     * @return
     */
    @PostMapping("/parameter-query")
    public ResultMode<PlatformParameterDTO> parameterQuery(@RequestBody PlatformParameterQuery query){
        log.info("parameterQuery#请求参数：{}", JSONUtil.toJsonStr(query));
        List<PlatformParameterDTO> platformParameterDTOS = platformCommonAppService.parameterQuery(query);
        return ResultMode.success(platformParameterDTOS);
    }

}
