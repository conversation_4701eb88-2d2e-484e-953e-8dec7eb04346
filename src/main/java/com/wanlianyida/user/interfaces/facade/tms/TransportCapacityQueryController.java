package com.wanlianyida.user.interfaces.facade.tms;

import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.tms.api.model.dto.TmsRecommendedCapacityDTO;
import com.wanlianyida.tms.api.model.dto.TransportCarDTO;
import com.wanlianyida.tms.api.model.query.CarTransportCapacityQuery;
import com.wanlianyida.tms.api.model.query.TmsRecommendedCapacityQuery;
import com.wanlianyida.user.application.service.tms.TransportCapacityQueryAppService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 运力查询
 *
 * <AUTHOR>
 * @since 2025/2/28 13:54
 */
@RestController
@RequestMapping("/transportCapacityQuery")
public class TransportCapacityQueryController {

    @Resource
    private TransportCapacityQueryAppService transportCapacityQueryAppService;

    /**
     * 运力推荐查询（派车、服务单派车）
     */
    @PostMapping("/recommendPage")
    public ResultMode<TmsRecommendedCapacityDTO> recommendPage(@RequestBody PagingInfo<TmsRecommendedCapacityQuery> pagingInfo) {
        return transportCapacityQueryAppService.recommendPage(pagingInfo);
    }

    /**
     * 自有运力和平台运力车辆查询（派车、服务单派车）
     */
    @PostMapping("/carQueryPage")
    public ResultMode<TransportCarDTO> carQueryPage(@RequestBody PagingInfo<CarTransportCapacityQuery> pagingInfo) {
        return transportCapacityQueryAppService.carQueryPage(pagingInfo);
    }

}
