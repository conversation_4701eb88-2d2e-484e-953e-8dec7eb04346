package com.wanlianyida.user.interfaces.facade.support;

import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.service.support.HolidaySchedulingApplicationService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 催缴运费
 */
@RestController
@RequestMapping(value = "/external/holiday-scheduling")
public class HolidaySchedulingController {

    @Resource
    private HolidaySchedulingApplicationService holidaySchedulingApplicationService;

    @PostMapping("/get-scheduling")
    public ResultMode getScheduling() {
        return holidaySchedulingApplicationService.getScheduling();
    }

}
