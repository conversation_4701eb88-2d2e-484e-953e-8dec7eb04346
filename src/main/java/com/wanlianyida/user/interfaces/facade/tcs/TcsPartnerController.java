package com.wanlianyida.user.interfaces.facade.tcs;

import cn.hutool.json.JSONUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.command.tcs.TcsAddPartnerCommand;
import com.wanlianyida.user.application.model.command.tcs.TcsDeletePartnerCommand;
import com.wanlianyida.user.application.model.dto.tcs.PartnerListDTO;
import com.wanlianyida.user.application.model.dto.tcs.TcsPartnerConfigDTO;
import com.wanlianyida.user.application.model.query.tcs.PartnerListQuery;
import com.wanlianyida.user.application.model.query.tcs.TcsPartnerConfigQuery;
import com.wanlianyida.user.application.service.tcs.TcsPartnerAppService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * tcs 服务伙伴
 */
@Slf4j
@RestController
@RequestMapping("/tcs-partner")
public class TcsPartnerController {

    @Resource
    private TcsPartnerAppService tcsPartnerAppService;

    /**
     * 查询订单货源对应的服务伙伴
     * @param query
     * @return
     */
    @PostMapping("/partner-config-query")
    public ResultMode<TcsPartnerConfigDTO> partnerConfigQuery(@RequestBody @Validated TcsPartnerConfigQuery query) {
        log.info("partnerConfigQuery#请求参数：{}", JSONUtil.toJsonStr(query));
        List<TcsPartnerConfigDTO> tcsPartnerConfigDTOS = tcsPartnerAppService.partnerConfigQuery(query);
        return ResultMode.success(tcsPartnerConfigDTOS);
    }

    /**
     * 新增伙伴
     * @param command
     * @return
     */
    @PostMapping("/partner-add")
    public ResultMode partnerAdd(@RequestBody @Validated TcsAddPartnerCommand command) {
        log.info("partnerAdd#请求参数:{}", JSONUtil.toJsonStr(command));
        tcsPartnerAppService.partnerAdd(command);
        return ResultMode.success();
    }

    /**
     * 删除伙伴
     * @param command
     * @return
     */
    @PostMapping("/partner-delete")
    public ResultMode partnerDelete(@RequestBody @Validated TcsDeletePartnerCommand command) {
        log.info("partnerAdd#请求参数:{}", JSONUtil.toJsonStr(command));
        tcsPartnerAppService.partnerDelete(command);
        return ResultMode.success();
    }

    /**
     * 查询伙伴列表
     * @param query
     * @return
     */
    @PostMapping("/partner-list-query")
    public ResultMode<PartnerListDTO> partnerListQuery(@RequestBody PartnerListQuery query){
        log.info("partnerListQuery#请求参数:{}", JSONUtil.toJsonStr(query));
        List<PartnerListDTO> partnerListDTOS = tcsPartnerAppService.partnerListQuery(query);
        return ResultMode.success(partnerListDTOS);
    }





}
