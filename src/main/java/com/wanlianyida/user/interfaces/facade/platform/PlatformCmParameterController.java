package com.wanlianyida.user.interfaces.facade.platform;

import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.query.platform.ExterParaValueByOperIdAndParaKeyQuery;
import com.wanlianyida.user.application.service.platform.PlatformCmParameterAppService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping(value = "/plat-cm-parameter")
public class PlatformCmParameterController {

    @Resource
    private PlatformCmParameterAppService platformCmParameterAppService;

    /**
     * 根据操作主体id和参数字典key值查询参数值
     * @param query
     * @return
     */
    @PostMapping("/para-value-by-operId-and-para-key")
    public ResultMode<String> getParaValueByOperIdAndParaKey(@RequestBody @Validated ExterParaValueByOperIdAndParaKeyQuery query)  {
        return platformCmParameterAppService.getParaValueByOperIdAndParaKey(query);
    }



}
