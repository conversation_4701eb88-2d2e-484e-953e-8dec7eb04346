package com.wanlianyida.user.interfaces.facade.platform;

import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.platform.api.model.dto.CompanyMemberDTO;
import com.wanlianyida.platform.api.model.dto.FindPlfUserDTO;
import com.wanlianyida.platform.api.model.dto.PlfUsrCompanyMemberDTO;
import com.wanlianyida.user.application.model.command.platform.ExternalPlfUsrAgreeOrRefuseBindCommand;
import com.wanlianyida.user.application.model.command.platform.ExternalPlfUsrBindCommand;
import com.wanlianyida.user.application.model.command.platform.ExternalPlfUsrUnBindCommand;
import com.wanlianyida.user.application.model.dto.platform.PlfUsrByMobileDTO;
import com.wanlianyida.user.application.model.query.platform.ExternalCompanyMemberQuery;
import com.wanlianyida.user.application.model.query.platform.ExternalFindPlfUsrByMobileQuery;
import com.wanlianyida.user.application.model.query.platform.ExternalPlfUsrCompanyMemberQuery;
import com.wanlianyida.user.application.service.platform.PlfCompanyMemberPlfUsrAppService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 *企业员工与平台用户关系
 */
@Slf4j
@RestController
@RequestMapping(value = "/plf-company-member-plf-usr")
public class PlfCompanyMemberPlfUsrController {

    @Resource
    private PlfCompanyMemberPlfUsrAppService plfCompanyMemberPlfUsrAppService;

    /**
     * 获取企业员工列表
     */
    @PostMapping(value = "/company-member-page")
    public ResultMode<CompanyMemberDTO> companyMemberPage(@RequestBody PagingInfo<ExternalCompanyMemberQuery> pageInfo) {
        return plfCompanyMemberPlfUsrAppService.companyMemberPage(pageInfo);
    }


    /**
     * 获取平台个人关联的企业员工列表
     */
    @PostMapping(value = "/plf-usr-company-member-page")
    public ResultMode<PlfUsrCompanyMemberDTO> plfUsrCompanyMemberPage(@RequestBody PagingInfo<ExternalPlfUsrCompanyMemberQuery> pageInfo) {
        return plfCompanyMemberPlfUsrAppService.plfUsrCompanyMemberPage(pageInfo);
    }

    /**
     * 获取当前企业的员工绑定的个人账户信息
     */
    @PostMapping(value = "/find-plf-user")
    public ResultMode<FindPlfUserDTO> findPlfUser() {
        return plfCompanyMemberPlfUsrAppService.findPlfUser();
    }


    /**
     * 通过手机号获取中台个人账户信息
     * 入参手机号
     */
    @PostMapping(value = "/find-plf-usr-by-mobile")
    public ResultMode<PlfUsrByMobileDTO> findPlfUsrByMobile(@RequestBody @Validated ExternalFindPlfUsrByMobileQuery query) {
        return plfCompanyMemberPlfUsrAppService.findPlfUsrByMobile(query);
    }


    /**
     *绑定接口-发起邀请 bind
     */
    @PostMapping(value = "/bind")
    public ResultMode<?> bind(@RequestBody @Validated ExternalPlfUsrBindCommand command) {
        return plfCompanyMemberPlfUsrAppService.bind(command);
    }

    /**
     * 解绑 unbind
     */
    @PostMapping(value = "/unbind")
    public ResultMode<?> unbind(@RequestBody @Validated ExternalPlfUsrUnBindCommand command) {
        return plfCompanyMemberPlfUsrAppService.unbind(command);
    }

    /**
     * 同意/拒绝绑定agree-bind
     */
    @PostMapping(value = "/agree-or-refuse-bind")
    public ResultMode<?> agreeOrRefuseBind(@RequestBody @Validated ExternalPlfUsrAgreeOrRefuseBindCommand command) {
        return plfCompanyMemberPlfUsrAppService.agreeOrRefuseBind(command);
    }

}
