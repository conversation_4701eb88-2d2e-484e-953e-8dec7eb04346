package com.wanlianyida.user.interfaces.facade.oms;

import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.dto.oms.ThirdOrderPageListDTO;
import com.wanlianyida.user.application.model.dto.oms.ThirdOrderDetailQueryDTO;
import com.wanlianyida.user.application.model.query.oms.ThirdOrderDetailQuery;
import com.wanlianyida.user.application.model.query.oms.ThirdOrderPageQuery;
import com.wanlianyida.user.application.service.oms.OmsThirdOrderAppService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RequestMapping("/oms-third-order")
@RestController
public class OmsThirdOrderController {

    @Resource
    private OmsThirdOrderAppService omsThirdOrderAppService;

    /**
     * 三方订单列表查询
     */
    @PostMapping("/third-order-page-query")
    public ResultMode<ThirdOrderPageListDTO> thirdOrderPageQuery(@RequestBody PagingInfo<ThirdOrderPageQuery> pagingInfo) {
        return omsThirdOrderAppService.thirdOrderPageQuery(pagingInfo);
    }

    /**
     * 三方订单详情查询
     */
    @PostMapping("/third-order-detail-query")
    public ResultMode<ThirdOrderDetailQueryDTO> orderDetailQuery(@RequestBody @Validated ThirdOrderDetailQuery query) {
        return omsThirdOrderAppService.thirdOrderDetailQuery(query);
    }

}
