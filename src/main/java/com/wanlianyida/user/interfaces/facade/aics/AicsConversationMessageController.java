package com.wanlianyida.user.interfaces.facade.aics;

import com.wanlianyida.baseaiadv.api.model.command.AicsConversationMessageCommand;
import com.wanlianyida.baseaiadv.api.model.command.AicsConversationMessageUpdateCommand;
import com.wanlianyida.baseaiadv.api.model.command.MessageEvaluateCommand;
import com.wanlianyida.baseaiadv.api.model.command.MessageFeedbackCommand;
import com.wanlianyida.baseaiadv.api.model.dto.AicsConversationMessageDTO;
import com.wanlianyida.baseaiadv.api.model.query.AicsConversationMessageQuery;
import com.wanlianyida.fssmodel.PagingInfo;
import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.user.application.service.aics.AicsConversationMessageAppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025年05月15日 10:18
 */
@Api("会话消息")
@RestController
@RequestMapping("/conversationMessage")
public class AicsConversationMessageController {

    @Resource
    private AicsConversationMessageAppService conversationMessageAppService;

    @ApiOperation("保存消息")
    @PostMapping("/save")
    public ResponseMessage<AicsConversationMessageDTO> save(@RequestBody @Validated AicsConversationMessageCommand command) {
        return conversationMessageAppService.save(command);
    }

    @ApiOperation("点赞/取消点赞")
    @PostMapping("/upvote")
    public ResponseMessage<?> upvote(@RequestBody @Validated MessageEvaluateCommand command) {
        return conversationMessageAppService.upvote(command);
    }

    @ApiOperation("点踩/取消点踩")
    @PostMapping("/trample")
    public ResponseMessage<?> trample(@RequestBody @Validated MessageEvaluateCommand command) {
        return conversationMessageAppService.trample(command);
    }

    @ApiOperation("反馈")
    @PostMapping("/feedback")
    public ResponseMessage<?> feedback(@RequestBody @Validated MessageFeedbackCommand command) {
        return conversationMessageAppService.feedback(command);
    }

    @ApiOperation("查询消息列表")
    @PostMapping("/queryMessageList")
    public ResponseMessage<List<AicsConversationMessageDTO>> queryMessageList(@RequestBody @Validated PagingInfo<AicsConversationMessageQuery> query) {
        return conversationMessageAppService.queryMessageList(query);
    }

    @ApiOperation("重试")
    @PostMapping("/retry")
    public ResponseMessage<AicsConversationMessageDTO> retry(@RequestBody @Validated AicsConversationMessageUpdateCommand command) {
        return conversationMessageAppService.retry(command);
    }
}
