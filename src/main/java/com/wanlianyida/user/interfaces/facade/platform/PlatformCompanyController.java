package com.wanlianyida.user.interfaces.facade.platform;

import cn.hutool.json.JSONUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.command.platform.CompanyLinkmanInfoCommand;
import com.wanlianyida.user.application.model.dto.platform.BatchConfigDTO;
import com.wanlianyida.user.application.model.dto.platform.CompanyInfoDTO;
import com.wanlianyida.user.application.model.dto.platform.CompanyLinkmanInfoDTO;
import com.wanlianyida.user.application.model.query.platform.BatchConfigQuery;
import com.wanlianyida.user.application.model.query.platform.CompanyInfoQuery;
import com.wanlianyida.user.application.model.query.platform.CompanyLinkmanInfoQuery;
import com.wanlianyida.user.application.service.platform.PlatformCompanyAppService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 * 平台企业 controller
 */
@Slf4j
@RestController
@RequestMapping(value = "/plat-company")
public class PlatformCompanyController {


    @Resource
    private PlatformCompanyAppService platformCompanyAppService;

    /**
     * 企业详情
     * @param query
     * @return
     */
    @PostMapping("/info-query")
    public ResultMode<CompanyInfoDTO> infoQuery(@RequestBody @Validated CompanyInfoQuery query){
        log.info("infoQuery#查询请求参数：{}", JSONUtil.toJsonStr(query));
        CompanyInfoDTO companyInfoDTO = platformCompanyAppService.infoQuery(query);
        return ResultMode.success(companyInfoDTO);
    }

    /**
     * 批量查询配置信息
     * @param query
     * @return
     */
    @PostMapping("/batch-config-query")
    public ResultMode<Map<String, List<BatchConfigDTO>>> batchConfigQuery(@Validated @RequestBody BatchConfigQuery query){
        log.info("batchConfigQuery#查询请求参数：{}", JSONUtil.toJsonStr(query));
        Map<String, List<BatchConfigDTO>> listMap = platformCompanyAppService.batchConfigQuery(query);
        return ResultMode.success(listMap);
    }

    /**
     * 查询企业联系人
     * @param query
     * @return
     */
    @PostMapping("/company-linkman-list-query")
    public ResultMode<CompanyLinkmanInfoDTO> companyLinkmanListQuery(@RequestBody CompanyLinkmanInfoQuery query){
        log.info("companyLinkmanListQuery#查询企业联系人请求参数:{}", JSONUtil.toJsonStr(query));
        return platformCompanyAppService.companyLinkmanListQuery(query);
    }

    /**
     * 新增企业联系人
     * @param command
     * @return
     */
    @PostMapping("/company-linkman-add")
    public ResultMode companyLinkmanAdd(@RequestBody CompanyLinkmanInfoCommand command) {
        log.info("companyLinkmanAdd#新增企业联系人请求参数:{}", JSONUtil.toJsonStr(command));
        return platformCompanyAppService.companyLinkmanAdd(command);
    }



}
