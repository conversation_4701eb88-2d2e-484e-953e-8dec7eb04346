package com.wanlianyida.user.interfaces.facade.tms;

import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.dto.tms.OrderListQueryDTO;
import com.wanlianyida.user.application.model.query.tms.OrderListQuery;
import com.wanlianyida.user.application.service.tms.TmsOrderAppService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RequestMapping("/tms-order")
@RestController
public class TmsOrderController {


    @Resource
    private TmsOrderAppService tmsOrderAppService;

    /**
     * 查询订单列表
     * @param pageInfo
     * @return
     */
    @PostMapping("/order-list-query")
    public ResultMode<OrderListQueryDTO> orderListQuery(@RequestBody PagingInfo<OrderListQuery> pageInfo) {
        return tmsOrderAppService.orderListQuery(pageInfo);
    }
}
