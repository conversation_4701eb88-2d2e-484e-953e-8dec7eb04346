package com.wanlianyida.user.interfaces.facade.aics;

import com.wanlianyida.baseaiadv.api.model.command.LoginCommand;
import com.wanlianyida.baseaiadv.api.model.dto.ValidDTO;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.dto.aics.AicsLoginDTO;
import com.wanlianyida.user.application.service.aics.AicsAuthorityAppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025年05月15日 10:00
 */
@Api("鉴权")
@RestController
@RequestMapping("/simulation/authority")
public class AicsAuthorityController {

    @Resource
    private AicsAuthorityAppService authorityAppService;

    @ApiOperation("模拟登录")
    @PostMapping("/login")
    public ResultMode<AicsLoginDTO> login(@RequestBody @Validated LoginCommand command) {
        return ResultMode.success(authorityAppService.login(command));
    }

    @ApiOperation("白名单校验")
    @PostMapping("/experienceValid")
    public ResultMode<ValidDTO> experienceValid(@RequestBody @Validated LoginCommand command) {
        return ResultMode.success(authorityAppService.experienceValid(command));
    }
}
