package com.wanlianyida.user.interfaces.facade.tcs;

import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.tcs.api.model.dto.TcsCarLicenseVO;
import com.wanlianyida.tcs.api.model.query.TcsCarPlateNoAndColorFilter;
import com.wanlianyida.user.application.service.tcs.TcsCarQueryAppService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 车辆监控
 */
@RestController
@RequestMapping("/tcsCarQuery")
public class TcsCarQueryController {

    @Resource
    private TcsCarQueryAppService tcsCarQueryAppService;

    /**
     * 车牌号和颜色 查询 行驶证信息- 包含照片附件信息
     */
    @PostMapping("/carLicenseAttachment")
    public ResultMode<TcsCarLicenseVO> carLicenseAttachment(@RequestBody TcsCarPlateNoAndColorFilter query) {
        return tcsCarQueryAppService.carLicenseAttachment(query);
    }
}
