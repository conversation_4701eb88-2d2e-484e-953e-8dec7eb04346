package com.wanlianyida.user.interfaces.facade.cont;

import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.dto.cont.CheckContractSignDTO;
import com.wanlianyida.user.application.model.dto.cont.ContractTemplateBatchDTO;
import com.wanlianyida.user.application.model.query.cont.CheckContractSignQuery;
import com.wanlianyida.user.application.model.query.cont.ContractTemplateBatchQuery;
import com.wanlianyida.user.application.model.query.cont.NeedSignContractQuery;
import com.wanlianyida.user.application.service.cont.ContContractAppService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 * 合同协议 controller
 */
@Slf4j
@RestController
@RequestMapping(value = "/cont-contract")
public class ContContractController {

    @Resource
    private ContContractAppService contContractAppService;

    /**
     * 批量查询协议模板
     * @param query
     * @return
     */
    @PostMapping("/batch-template-query")
    public ResultMode<ContractTemplateBatchDTO> batchTemplateQuery (@RequestBody ContractTemplateBatchQuery query){
        List<ContractTemplateBatchDTO> contractTemplateBatchDTOS = contContractAppService.batchTemplateQuery(query);
        return ResultMode.success(contractTemplateBatchDTOS);
    }

    /**
     * 需要签署的协议
     * @param query
     * @return
     */
    @PostMapping("/need-sign-contract")
    public ResultMode<Map<String, Object>> needSignContract(@RequestBody NeedSignContractQuery query){
        Map<String, Object> stringObjectMap = contContractAppService.needSignContract(query);
        return ResultMode.success(stringObjectMap);
    }

    /**
     * 校验合同是否允许发货/调度
     */
    @PostMapping("/check-contract-sign")
    public ResultMode<CheckContractSignDTO> checkContractSign(@RequestBody @Validated CheckContractSignQuery query) {
        return contContractAppService.checkContractSign(query);
    }

}
