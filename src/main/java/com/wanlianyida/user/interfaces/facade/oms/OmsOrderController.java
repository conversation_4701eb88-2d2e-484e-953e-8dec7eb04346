package com.wanlianyida.user.interfaces.facade.oms;

import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.dto.oms.CarrierOrderPageListDTO;
import com.wanlianyida.user.application.model.dto.oms.OmsOrderAddressDTO;
import com.wanlianyida.user.application.model.dto.oms.OrderDetailQueryDTO;
import com.wanlianyida.user.application.model.dto.oms.ShipperOrderPageListDTO;
import com.wanlianyida.user.application.model.query.oms.CarrierOrderPageQuery;
import com.wanlianyida.user.application.model.query.oms.OrderAddressQuery;
import com.wanlianyida.user.application.model.query.oms.OrderDetailQuery;
import com.wanlianyida.user.application.model.query.oms.ShipperOrderPageQuery;
import com.wanlianyida.user.application.service.oms.OmsOrderAppService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RequestMapping("/oms-order")
@RestController
public class OmsOrderController {

    @Resource
    private OmsOrderAppService omsOrderAppService;

    /**
     * 查询订单详情
     * @param query
     * @return
     */
    @PostMapping("/order-detail-query")
    public ResultMode<OrderDetailQueryDTO> orderDetailQuery(@RequestBody @Validated OrderDetailQuery query){
        return omsOrderAppService.orderDetailQuery(query);
    }

    /**
     * 我要发货-订单列表查询
     */
    @PostMapping("/shipper-order-page-query")
    public ResultMode<ShipperOrderPageListDTO> shipperOrderPageQuery(@RequestBody PagingInfo<ShipperOrderPageQuery> pagingInfo){
        return omsOrderAppService.shipperOrderPageQuery(pagingInfo);
    }

    /**
     * 我要承运-订单列表查询
     */
    @PostMapping("/carrier-order-page-query")
    public ResultMode<CarrierOrderPageListDTO> carrierOrderPageQuery(@RequestBody PagingInfo<CarrierOrderPageQuery> pagingInfo){
        return omsOrderAppService.carrierOrderPageQuery(pagingInfo);
    }

    /**
     * 根据orderId获取订单地址信息
     */
    @PostMapping("/order-address-query")
    public ResultMode<OmsOrderAddressDTO> orderAddressQuery(@RequestBody @Validated OrderAddressQuery query){
        return omsOrderAppService.orderAddressQuery(query);
    }

}
