package com.wanlianyida.user.interfaces.facade.cont;

import com.wanlianyida.basecont.api.model.dto.ContractSignTemplateDTO;
import com.wanlianyida.basecont.api.model.dto.OssKeyContractSignFlowDTO;
import com.wanlianyida.basecont.api.model.query.ContractSignFlowQuery;
import com.wanlianyida.basecont.api.model.query.ContractSignTemplateQuery;
import com.wanlianyida.basecont.api.model.query.ContractWillSignQuery;
import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.user.infrastructure.exchange.ContExchangeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import javax.annotation.Resource;

/**
 * 相同相关 controller
 */
@Slf4j
@RestController
@RequestMapping("/cont")
public class ContController {

    @Resource
    private ContExchangeService contExchangeService;

    /**
     * 合同签署流程通用查询
     * @param query
     * @return
     */
    @PostMapping("/queryContractSignFlowByCondition")
    public ResponseMessage<List<OssKeyContractSignFlowDTO>> queryContractSignFlowByCondition(@RequestBody ContractSignFlowQuery query){
        return contExchangeService.queryContractSignFlowByCondition(query);
    }

    /**
     * 通过模板id获取模板对象
     *
     * @param query
     * @return {@link ResponseMessage }<{@link ContractSignTemplateDTO }>
     */
    @PostMapping("/getContractSignTemplateById")
    public ResponseMessage<ContractSignTemplateDTO> getContractSignTemplateById(@RequestBody ContractSignTemplateQuery query) {
        return contExchangeService.getContractSignTemplateById(query);
    }


    /**
     * 合同意愿签
     * @param query
     * @return
     */
    @PostMapping("/getWillSignPageUrl")
    public ResponseMessage<List<String>> getWillSignPageUrl(@RequestBody ContractWillSignQuery query){
        return contExchangeService.getWillSignPageUrl(query);
    }
}
