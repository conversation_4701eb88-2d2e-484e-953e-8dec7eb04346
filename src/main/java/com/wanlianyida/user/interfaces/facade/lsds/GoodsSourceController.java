package com.wanlianyida.user.interfaces.facade.lsds;

import cn.hutool.json.JSONUtil;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.lsds.api.model.command.GoodsIdCommand;
import com.wanlianyida.lsds.api.model.command.publish.PublishOneClickCommand;
import com.wanlianyida.lsds.api.model.dto.GoodsSourceDetailDTO;
import com.wanlianyida.lsds.api.model.dto.GoodsSourceListDTO;
import com.wanlianyida.lsds.api.model.query.GoodsSourceListQuery;
import com.wanlianyida.lsds.api.model.query.GoodsSourceQuery;
import com.wanlianyida.user.application.model.command.lsds.publish.GoodsSourcePublishCommand;
import com.wanlianyida.user.application.service.lsds.GoodsSourceAppService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 货源接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/goodsSource")
@Slf4j
public class GoodsSourceController {

    @Resource
    private GoodsSourceAppService goodsSourceAppService;

    /**
     * 获取货源列表
     */
    @PostMapping(value = "/queryPage")
    public ResultMode<GoodsSourceListDTO> queryPage(@RequestBody PagingInfo<GoodsSourceListQuery> query) {
        return goodsSourceAppService.queryPage(query);
    }

    /**
     * 发布货源
     */
    @PostMapping("/publish")
    public ResultMode publish(@RequestBody @Validated GoodsSourcePublishCommand command) {
        log.info("goodsSource publish入数：{}", JSONUtil.toJsonStr(command));
        return goodsSourceAppService.publish(command);
    }

    /**
     * 保存草稿箱后列表一键发布
     */
    @PostMapping("/publishOneClick")
    public ResultMode publishOneClick(@RequestBody @Validated PublishOneClickCommand command) {
        log.info("goodsSource publishOneClick：{}", JSONUtil.toJsonStr(command));
        return goodsSourceAppService.publishOneClick(command);
    }

    /**
     * 查询货源详情
     */
    @PostMapping(value = "/queryDetail")
    public ResultMode<GoodsSourceDetailDTO> queryDetail(@RequestBody @Validated GoodsSourceQuery query) {
        return goodsSourceAppService.queryDetail(query);
    }

    @ApiOperation("删除货源")
    @PostMapping("/deleteGoods")
    public ResultMode<?> deleteGoods(@RequestBody @Validated GoodsIdCommand command) {
        return goodsSourceAppService.deleteGoods(command);
    }

    @ApiOperation("关闭货源")
    @PostMapping("/closeGoods")
    public ResultMode<?> closeGoods(@RequestBody @Validated GoodsIdCommand command) {
        return goodsSourceAppService.closeGoods(command);
    }

}
