package com.wanlianyida.user.interfaces.facade.tms;

import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.tms.api.model.dto.PlateNumberDTO;
import com.wanlianyida.tms.api.model.dto.TmsWaybillDetailDTO;
import com.wanlianyida.user.application.model.dto.tms.WaybillListQueryDTO;
import com.wanlianyida.user.application.model.query.tms.ExternalPlateNumberQuery;
import com.wanlianyida.user.application.model.query.tms.SignWaybillListQuery;
import com.wanlianyida.user.application.model.query.tms.WaybillListQuery;
import com.wanlianyida.user.application.service.tms.TmsWaybillAppService;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RequestMapping("/tms-waybill")
@RestController
public class TmsWaybillController {


    @Resource
    private TmsWaybillAppService tmsWaybillAppService;

    /**
     * 查询运单列表
     * @param pageInfo
     * @return
     */
    @PostMapping("/waybill-list-query")
    public ResultMode<WaybillListQueryDTO> waybillListQuery(@RequestBody @ApiParam PagingInfo<WaybillListQuery> pageInfo){
        return tmsWaybillAppService.waybillListQuery(pageInfo);
    }

    /**
     * 签收运单列表
     * @param query
     * @return
     */
    @PostMapping(value = "/sign-waybill-list")
    public ResultMode<TmsWaybillDetailDTO> signWaybillList(@RequestBody @Validated SignWaybillListQuery query){
        return tmsWaybillAppService.signWaybillList(query);
    }

    /**
     * 运单车牌号列表
     */
    @PostMapping(value = "/waybill-plate-number-page")
    public ResultMode<PlateNumberDTO> waybillPlateNumberPage(@RequestBody @Validated PagingInfo<ExternalPlateNumberQuery> externalQueryPageInfo) {
        return tmsWaybillAppService.waybillPlateNumberPage(externalQueryPageInfo);
    }

}
