package com.wanlianyida.user.interfaces.facade.qrs;

import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.command.qrs.IdentifyCodeGoodsBindCommand;
import com.wanlianyida.user.application.service.qrs.QrsIdentifyCodeGoodsAppService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年08月06日 15:47
 */
@RestController
@RequestMapping("/identify-code-goods")
public class QrsIdentifyCodeGoodsController {

    @Resource
    private QrsIdentifyCodeGoodsAppService identityCodeGoodsAppService;

    @ApiOperation("绑定货源码")
    @RequestMapping("/bind")
    public ResultMode<String> bind(@RequestBody IdentifyCodeGoodsBindCommand command) {
        return identityCodeGoodsAppService.bind(command);
    }

}
