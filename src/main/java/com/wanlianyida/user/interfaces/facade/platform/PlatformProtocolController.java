package com.wanlianyida.user.interfaces.facade.platform;

import cn.hutool.json.JSONUtil;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.dto.platform.ProtocolBatchDTO;
import com.wanlianyida.user.application.model.query.platform.ProtocolBatchQuery;
import com.wanlianyida.user.application.service.platform.PlatformProtocolAppService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * 平台协议 controller
 */
@Slf4j
@RestController
@RequestMapping(value = "/plat-protocol")
public class PlatformProtocolController {


    @Resource
    private PlatformProtocolAppService platformProtocolAppService;


    /**
     * 批量查询
     * @param pageInfo
     * @return
     */
    /**
     * 批量查询
     * @param pageInfo
     * @return
     */
    @PostMapping("/batch-query")
    public ResultMode<ProtocolBatchDTO> batchQuery(@RequestBody PagingInfo<ProtocolBatchQuery> pageInfo){
        log.info("batchQuery#查询请求参数：{}", JSONUtil.toJsonStr(pageInfo));
        List<ProtocolBatchDTO> protocolBatchDTOS = platformProtocolAppService.batchQuery(pageInfo);
        return ResultMode.success(protocolBatchDTOS);
    }
}
