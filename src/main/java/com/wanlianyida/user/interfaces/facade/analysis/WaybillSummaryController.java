package com.wanlianyida.user.interfaces.facade.analysis;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.dto.analysis.WaybillSummaryDTO;
import com.wanlianyida.user.application.model.query.analysis.WaybillSummaryQuery;
import com.wanlianyida.user.application.service.analysis.WaybillSummaryAppService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

import javax.annotation.Resource;

/**
 * 统一分析运单汇总
 */
@Slf4j
@RequestMapping("/waybillSummary")
@RestController
public class WaybillSummaryController {

    @Resource
    private WaybillSummaryAppService waybillSummaryAppService;

    /**
     * 托运人运单数据汇总
     * @param pagingInfo
     * @return
     */
    @PostMapping("/shipperWaybillList")
    public ResultMode<Map<String, Object>> shipperWaybillList(@RequestBody PagingInfo<Map<String, Object>> pagingInfo){
        log.info("shipperWaybillList#请求参数:{}", JSONUtil.toJsonStr(pagingInfo));
        Map<String, Object> filterModel = pagingInfo.getFilterModel();
        if(CollUtil.isEmpty(filterModel)){
            return ResultMode.fail("参数为空");
        }
        return waybillSummaryAppService.shipperWaybillList(pagingInfo);
    }

    @ApiOperation("运单数据汇总")
    @PostMapping("/waybillList")
    public ResultMode<WaybillSummaryDTO> waybillList(@RequestBody PagingInfo<WaybillSummaryQuery> queryPagingInfo) {
        WaybillSummaryQuery filterModel = queryPagingInfo.getFilterModel();
        if(ObjectUtil.isEmpty(filterModel)){
            return ResultMode.fail("参数为空");
        }
        return waybillSummaryAppService.waybillList(queryPagingInfo);
    }
}
