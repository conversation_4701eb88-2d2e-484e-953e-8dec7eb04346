package com.wanlianyida.user.interfaces.facade.log;

import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.dto.log.OperRecordDTO;
import com.wanlianyida.user.application.model.query.log.OperRecordQuery;
import com.wanlianyida.user.application.service.log.LogOperationRecordAppService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 操作記錄
 */
@RequestMapping("/log")
@RestController
public class LogOperationRecordController {

    @Resource
    private LogOperationRecordAppService logOperationRecordAppService;

    /**
     * 操作日志分页查询
     * @param query
     * @return
     */
    @PostMapping("/oper-record-query")
    public ResultMode<OperRecordDTO> operRecordQuery(@RequestBody @Validated PagingInfo<OperRecordQuery> query){
        return logOperationRecordAppService.operRecordQuery(query);
    }
}
