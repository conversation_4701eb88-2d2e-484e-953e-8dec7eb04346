package com.wanlianyida.user.interfaces.facade.tcs;

import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.dto.tcs.AssignDriverTransportCapacityDTO;
import com.wanlianyida.user.application.model.dto.tcs.DriverTransportCapacityDTO;
import com.wanlianyida.user.application.model.query.tcs.AssignDriverTransportCapacityQuery;
import com.wanlianyida.user.application.model.query.tcs.DriverTransportCapacityQuery;
import com.wanlianyida.user.application.service.tcs.DriverTransportCapacityQueryAppService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 司机运力查询
 *
 * <AUTHOR>
 * @date 2025/03/04
 */
@RestController
@RequestMapping("/driverTransportCapacityQuery")
public class DriverTransportCapacityQueryController {

    @Resource
    private DriverTransportCapacityQueryAppService driverTransportCapacityQueryAppService;

    /**
     * 3pl公路订单管理-派车-选车辆-待选司机分页+运单补录-派车-选车辆-待选司机分页
     *
     * @param pagingInfo
     * @return {@link ResultMode }<{@link DriverTransportCapacityDTO }>
     */
    @PostMapping("/driversQueryPage")
    public ResultMode<DriverTransportCapacityDTO> driversQueryPage(@RequestBody PagingInfo<DriverTransportCapacityQuery> pagingInfo) {
        return driverTransportCapacityQueryAppService.driversQueryPage(pagingInfo);
    }

    /**
     * 3pl司机货源-发布-指定运力-待选司机分页
     *
     * @param pagingInfo
     * @return {@link ResultMode }<{@link AssignDriverTransportCapacityDTO }>
     */
    @PostMapping("/assignDriversQueryPage")
    public ResultMode<AssignDriverTransportCapacityDTO> assignDriversQueryPage(@RequestBody PagingInfo<AssignDriverTransportCapacityQuery> pagingInfo) {
        return driverTransportCapacityQueryAppService.assignDriversQueryPage(pagingInfo);
    }

}
