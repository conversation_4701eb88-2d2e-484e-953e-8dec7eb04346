package com.wanlianyida.user.interfaces.facade.aics;

import com.wanlianyida.baseaiadv.api.model.command.ConversationCommand;
import com.wanlianyida.baseaiadv.api.model.command.ConversationUpdateCommand;
import com.wanlianyida.baseaiadv.api.model.dto.ConversationDTO;
import com.wanlianyida.baseaiadv.api.model.query.ConversationQuery;
import com.wanlianyida.fssmodel.IdCommand;
import com.wanlianyida.fssmodel.PagingInfo;
import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.user.application.service.aics.ConversationAppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description
 * @Date 2025年04月16日 16:30
 */
@Api("会话")
@RestController
@RequestMapping("/conversation")
public class ConversationController {

    @Resource
    private ConversationAppService conversationAppService;

    @ApiOperation("创建会话")
    @PostMapping("/create")
    public ResponseMessage<ConversationDTO> create(@RequestBody @Validated ConversationCommand command) {
        return conversationAppService.create(command);
    }

    @ApiOperation("查询会话列表")
    @PostMapping("/queryConversationList")
    public ResponseMessage<List<ConversationDTO>> queryConversationList(@RequestBody @Validated ConversationQuery query) {
        return conversationAppService.queryConversationList(query);
    }

    @ApiOperation("分页查询会话列表")
    @PostMapping("/pageConversationList")
    public ResponseMessage<List<ConversationDTO>> pageConversationList(@RequestBody @Validated PagingInfo<ConversationQuery> pageQuery) {
        return conversationAppService.pageConversationList(pageQuery);
    }

    @ApiOperation("删除会话")
    @PostMapping("/delete")
    public ResponseMessage<?> delete(@RequestBody @Validated IdCommand command) {
        return conversationAppService.delete(command);
    }

    @ApiOperation("更新会话标题")
    @PostMapping("/updateTitle")
    public ResponseMessage<?> updateTitle(@RequestBody @Validated ConversationUpdateCommand command) {
        return conversationAppService.updateTitle(command);
    }

}
