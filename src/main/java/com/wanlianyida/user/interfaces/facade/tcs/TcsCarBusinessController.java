package com.wanlianyida.user.interfaces.facade.tcs;

import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.dto.tcs.GuacheDTO;
import com.wanlianyida.user.application.model.query.tcs.GuacheQuery;
import com.wanlianyida.user.application.service.tcs.TcsCarBusinessAppService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 车辆审核信息表
 */
@RestController
@RequestMapping("/tcsCarBusiness")
public class TcsCarBusinessController {

    @Resource
    private TcsCarBusinessAppService tcsCarBusinessAppService;

    /**
     * 车辆挂车列表
     */
    @PostMapping(value = "/queryCompanyCarPage")
    public ResultMode<GuacheDTO> queryCompanyCarPage(@RequestBody PagingInfo<GuacheQuery> pagingInfo){
        return tcsCarBusinessAppService.queryCompanyCarPage(pagingInfo);
    }


}
