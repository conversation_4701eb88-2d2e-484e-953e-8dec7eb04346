package com.wanlianyida.user.interfaces.facade.gps;

import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.query.gps.GpsCarMonitViewQuery;
import com.wanlianyida.user.application.service.gps.GspCarMonitAppService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 车辆监控
 */
@RestController
@RequestMapping("/gpsCarMonit")
public class GspCarMonitController {

    @Resource
    private GspCarMonitAppService gspCarMonitAppService;

    /**
     * 车辆监控查看
     * @param query
     * @return
     */
    @PostMapping("/carMonitView")
    public ResultMode carMonitView(@RequestBody GpsCarMonitViewQuery query){
        return gspCarMonitAppService.carMonitView(query);
    }
}
