package com.wanlianyida.user.interfaces.facade.platform;

import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.command.platform.ForgetPasswordCommand;
import com.wanlianyida.user.application.model.command.platform.ResetPasswordCommand;
import com.wanlianyida.user.application.model.command.platform.UpdatePasswordCommand;
import com.wanlianyida.user.application.service.platform.PlatformEmployeeAppService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RequestMapping("/platform-employee")
@RestController
public class PlatformEmployeeController {

    @Resource
    private PlatformEmployeeAppService platformEmployeeAppService;

    /**
     * 更新密码
     * @param command
     * @return
     */
    @PostMapping("/update-password")
    public ResultMode updatePassword(@RequestBody UpdatePasswordCommand command) {
        return platformEmployeeAppService.updatePassword(command);
    }

    /**
     * 重置密码
     * @param command
     * @return
     */
    @PostMapping("/reset-password")
    public ResultMode resetPassword(@RequestBody ResetPasswordCommand command) {
        return platformEmployeeAppService.resetPassword(command);
    }

    /**
     * 重置密码
     * @param command
     * @return
     */
    @PostMapping("/forget-password")
    public ResultMode forgetPassword(@RequestBody ForgetPasswordCommand command) {
        return platformEmployeeAppService.forgetPassword(command);
    }
}
