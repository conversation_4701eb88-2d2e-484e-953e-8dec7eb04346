package com.wanlianyida.user.interfaces.facade.tms;

import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.tms.api.model.dto.FreightReminderDTO;
import com.wanlianyida.tms.api.model.dto.FreightReminderTimeDTO;
import com.wanlianyida.tms.api.model.query.FreightReminderQuery;
import com.wanlianyida.tms.api.model.query.FreightReminderTimeQuery;
import com.wanlianyida.user.application.model.command.tms.ExterFreightCommunicateCommand;
import com.wanlianyida.user.application.service.tms.FreightReminderAppService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 催缴运费 Controller
 */
@RequestMapping(value = "/freight_reminder")
@RestController
public class FreightReminderController {

    @Resource
    private FreightReminderAppService freightReminderAppService;


    /**
     * 查询运费催缴和回复记录
     */
    @PostMapping("/query_freight_reminder")
    ResultMode<FreightReminderDTO> queryFreightReminder(@RequestBody @Validated FreightReminderQuery query) {
        return freightReminderAppService.queryFreightReminder(query);
    }

    /**
     * 催付回复
     */
    @PostMapping("/communicate_freight")
    ResultMode communicateFreight(@RequestBody @Validated ExterFreightCommunicateCommand command) {
        return freightReminderAppService.communicateFreight(command);
    }

    /**
     * 查询运费催付时间
     */
    @PostMapping("/get_freight_reminder_time")
    ResultMode<FreightReminderTimeDTO> getFreightReminderTime(@RequestBody @Validated FreightReminderTimeQuery query) {
        return ResultMode.success(freightReminderAppService.getFreightReminderTime(query));
    }
}
