package com.wanlianyida.user.infrastructure.constant;

/**
 * 企业员工与平台用户关系表常量类
 *
 * <AUTHOR>
 * @Date 2025年01月
 */
public class PlfCompanyMemberPlfUsrRelConstants {

    /**
     * 绑定状态枚举
     */
    public static class BindStatus {
        /**
         * 未绑定
         */
        public static final Integer UNBOUND = 10;

        /**
         * 待确定
         */
        public static final Integer PENDING = 20;

        /**
         * 绑定成功
         */
        public static final Integer BOUND = 30;

        /**
         * 已解除
         */
        public static final Integer DISCONNECTED = 40;

        /**
         * 拒绝绑定
         */
        public static final Integer REJECTED = 50;

    }

    /**
     * 绑定状态描述
     */
    public static class BindStatusDesc {
        public static final String UNBOUND = "未绑定";
        public static final String PENDING = "待确定";
        public static final String BOUND = "绑定成功";
        public static final String DISCONNECTED = "已解除";
        public static final String REJECTED = "拒绝绑定";
        public static final String UNKNOWN = "未知状态";
    }

    /**
     * 获取绑定状态描述
     *
     * @param bindStatus 绑定状态
     * @return 状态描述
     */
    public static String getBindStatusDesc(Integer bindStatus) {
        if (bindStatus == null) {
            return BindStatusDesc.UNKNOWN;
        }
        switch (bindStatus) {
            case 10:
                return BindStatusDesc.UNBOUND;
            case 20:
                return BindStatusDesc.PENDING;
            case 30:
                return BindStatusDesc.BOUND;
            case 40:
                return BindStatusDesc.DISCONNECTED;
            case 50:
                return BindStatusDesc.REJECTED;
            default:
                return BindStatusDesc.UNKNOWN;
        }
    }

    /**
     * 判断是否为有效的绑定状态
     *
     * @param bindStatus 绑定状态
     * @return 是否有效
     */
    public static boolean isValidBindStatus(Integer bindStatus) {
        if (bindStatus == null) {
            return false;
        }
        return bindStatus == BindStatus.UNBOUND ||
               bindStatus == BindStatus.PENDING ||
               bindStatus == BindStatus.BOUND ||
               bindStatus == BindStatus.DISCONNECTED ||
               bindStatus == BindStatus.REJECTED;
    }

    /**
     * 判断是否为可操作的绑定状态
     *
     * @param bindStatus 绑定状态
     * @return 是否可操作
     */
    public static boolean isOperableBindStatus(Integer bindStatus) {
        if (bindStatus == null) {
            return false;
        }
        return bindStatus == BindStatus.PENDING ||
               bindStatus == BindStatus.BOUND;
    }
}
