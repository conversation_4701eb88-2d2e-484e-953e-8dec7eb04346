package com.wanlianyida.user.infrastructure.config;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年06月17日 09:51
 */
@Data
@Component
@ConfigurationProperties(prefix = "bi.workbench")
public class BiWorkbenchUrlConfig {

    @ApiModelProperty("今日发布货源")
    private String issueGoods;

    @ApiModelProperty("进行中订单")
    private String runningOrder;

    @ApiModelProperty("待接单订单")
    private String waitingReceiveOrder;

    @ApiModelProperty("派车中订单")
    private String assigningVehicleOrder;

    @ApiModelProperty("运输中运单")
    private String transportingWaybill;

    @ApiModelProperty("待结算运单")
    private String waitingSettleWaybill;

    @ApiModelProperty("待审核运单")
    private String waitingAuditWaybill;

    @ApiModelProperty("审核未通过运单")
    private String auditRejectWaybill;

    @ApiModelProperty("待支付运单")
    private String waitingPaymentWaybill;

    @ApiModelProperty("待开票运单")
    private String waitingInvoiceWaybill;

    @ApiModelProperty("超时未卸货")
    private String noUnloadExpire;

    @ApiModelProperty("超时未签收")
    private String noSignExpire;

    @ApiModelProperty("超时未结算")
    private String noSettleExpire;

    @ApiModelProperty("超时未支付")
    private String noPaymentExpire;

    @ApiModelProperty("超时未开票")
    private String noInvoiceExpire;

    @ApiModelProperty("待预审")
    private String waitingPreAudit;

    @ApiModelProperty("付款待审核")
    private String paymentWaitingAudit;

    @ApiModelProperty("开票待审核")
    private String invoiceWaitingAudit;

    @ApiModelProperty("新签约供应商")
    private String newShipper;

    @ApiModelProperty("新签约承运商")
    private String newCustomer;

    @ApiModelProperty("新签约司机")
    private String newCarrier;

    @ApiModelProperty("新签约车辆")
    private String newVehicle;

    @ApiModelProperty("汇总数据")
    private String collectData;
}
