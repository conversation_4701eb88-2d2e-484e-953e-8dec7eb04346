package com.wanlianyida.user.infrastructure.config;

import com.wanlianyida.framework.lgiauth.interceptor.UserSessionInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport;

import javax.annotation.Resource;

@Configuration
public class WebInterceptorConfig extends WebMvcConfigurationSupport {

    @Resource
    private UserSessionInterceptor userSessionInterceptor;


    @Override
    protected void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(userSessionInterceptor).addPathPatterns("/**")
                .excludePathPatterns("/plat-company-member-plf-usr/plf-usr-company-member-page")
                .excludePathPatterns("/plat-company-member-plf-usr/unbind")
                .excludePathPatterns("/plat-company-member-plf-usr/agree-or-refuse-bind");
        super.addInterceptors(registry);
    }
}
