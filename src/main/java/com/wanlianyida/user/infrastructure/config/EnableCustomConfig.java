package com.wanlianyida.user.infrastructure.config;

import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

import java.lang.annotation.*;

@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@EnableEurekaClient
@EnableFeignClients(basePackages = {
        "com.wanlianyida.gps.api.inter",
        "com.wanlianyida.mms.api.inter",
        "com.wanlianyida.tms.api.inter",
        "com.wanlianyida.tcs.api.inter",
        "com.wanlianyida.eval.api.inter",
        "com.wanlianyida.platform.api.inter",
        "com.wanlianyida.bi.api.inter",
        "com.wanlianyida.basecont.api.inter",
        "com.wanlianyida.baseaiadv.api.inter",
        "com.wanlianyida.file.api",
        "com.wanlianyida.bi.api.inter",
        "com.wanlianyida.lsds.api.inter",
        "com.wanlianyida.crm.api.inter",
        "com.wanlianyida.aggregator.api.inter",
        "com.wanlianyida.woa.api.inter",
        "com.wanlianyida.oms.api.inter",
        "com.wanlianyida.qrs.api.inter",
        "com.wanlianyida.fssbaselog.api.inter",
        "com.wanlianyida.cmd.api.inter",
        "com.wanlianyida.base.its.api.inter",
        "com.wanlianyida.support.api.inter",
        "com.wanlianyida.rms.api.inter",
        "com.wanlianyida.bms.api.inter",
        "com.wanlianyida.dms.api.inter",
        "com.wanlianyida.basemdm.api.inter",

})
@EnableAspectJAutoProxy(exposeProxy = true)
@SpringBootApplication(scanBasePackages ={
        "com.wanlianyida.user",
        "com.wanlianyida.framework"})
public @interface EnableCustomConfig {
}
