package com.wanlianyida.user.infrastructure.exception;

import lombok.Getter;

/**
 * 参数错误
 * 命名规则：PT+3位服务标识+3位错误码，
 * 例如：PT-USER-001
 *
 * 业务异常
 * 命名规则：BU+3位服务标识+3位错误码，
 * 例如：BU-USER-001
 */
@Getter
public enum UserErrorCode {

    PT_USER_001("PT-USER-001", "参数错误"),
    PT_USER_002("PT-USER-002", "未获取到登录企业信息"),
    PT_USER_003("PT-USER-003", "地址信息不允许修改"),
    PT_USER_004("PT-USER-004", "时间区间不能超过30天"),
    WAYBILL_NOT_EXIST("PT-USER-005", "运单信息不存在"),
    COMPANY_NOT_EXIST("PT-USER-006", "公司信息不存在"),

    UNKNOWN_USER_TYPE("PT-USER-951", "未知的用户类型"),
    TMS_ORDER_NOT_EXIST("PT-USER-007", "订单信息不存在"),
    RISK_REQUEST_FAIL("PT-USER-008", "调用风控异常"),
    RISK_CALCULATE_FAIL("PT-USER-009", "风控测算失败"),
    PLF_USER_NOT_EXIST("PT-USER-010", "未查询到个人用户信息"),
    DELIVER_CONTRACT_NOT_EXIST("PT-USER-011", "您与【%s】暂无业务合同，请维护合同后再操作。如有问题，请联系运营"),//发货的阻断提示,展示运营主体全程
    DISPATCH_CONTRACT_NOT_EXIST("PT-USER-012", "您与托运方【%s】暂无业务合同，请维护合同后再操作。如有问题，请联系运营"),//托运方企业简称
    DISPATCH_CONTRACT_EXPIRED("PT-USER-013", "您与托运方【%s】暂无业务合同。请尽快维护合同，以免影响操作。如有问题，请联系运营"),//托运方企业简称

    ;

    private String code;

    private String msg;

    UserErrorCode(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
