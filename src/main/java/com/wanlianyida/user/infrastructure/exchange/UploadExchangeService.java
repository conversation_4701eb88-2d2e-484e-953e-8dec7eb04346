package com.wanlianyida.user.infrastructure.exchange;

import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.map.MapUtil;
import com.wanlianyida.file.api.IUploadService;
import com.wanlianyida.file.query.FileUrlsQuery;
import com.wanlianyida.fssmodel.ResponseMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 * upload 业务接口
 */
@Slf4j
@Service
public class UploadExchangeService {

    @Resource
    private IUploadService uploadService;


    /**
     * url批量转换
     */
    public Map<String, String> getUrls(List<String> urls) {
        if (IterUtil.isEmpty(urls)) {
            return null;
        }

        ResponseMessage<Map<String, String>> responseMessage = uploadService.getUrls(new FileUrlsQuery(urls));
        if (responseMessage == null || MapUtil.isEmpty(responseMessage.getModel())) {
            return null;
        }

        return responseMessage.getModel();
    }

    /**
     * url批量转换-wlyd水印
     */
    public Map<String, String> convertUrlsWLYD(List<String> urls) {
        if (IterUtil.isEmpty(urls)) {
            return null;
        }

        FileUrlsQuery query = new FileUrlsQuery(urls);
        ResponseMessage<Map<String, String>> responseMessage = uploadService.getWatermarkUrls(query);
        if (MapUtil.isEmpty(responseMessage.getModel())) {
            return null;
        }

        return responseMessage.getModel();
    }

}
