package com.wanlianyida.user.infrastructure.exchange;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.lsds.api.inter.GoodsConfigInter;
import com.wanlianyida.lsds.api.inter.GoodsSourceInter;
import com.wanlianyida.lsds.api.inter.LsdsGoodsOfferInter;
import com.wanlianyida.lsds.api.model.command.GoodsIdCommand;
import com.wanlianyida.lsds.api.model.command.LineGoodsAddressUpdateCommand;
import com.wanlianyida.lsds.api.model.command.lsdsGoodsOfferCommand;
import com.wanlianyida.lsds.api.model.command.publish.PublishGoodsSourceAgainCommand;
import com.wanlianyida.lsds.api.model.command.publish.PublishGoodsSourceCommand;
import com.wanlianyida.lsds.api.model.command.publish.PublishOneClickCommand;
import com.wanlianyida.lsds.api.model.dto.*;
import com.wanlianyida.lsds.api.model.query.*;
import com.wanlianyida.user.application.model.command.crm.AddressEditCommand;
import com.wanlianyida.user.application.model.command.lsds.OfferAddCommand;
import com.wanlianyida.user.application.model.dto.crm.LineInfoDTO;
import com.wanlianyida.user.application.model.dto.lsds.LineValidGoodsDTO;
import com.wanlianyida.user.application.model.dto.lsds.OfferDetailsQueryDTO;
import com.wanlianyida.user.application.model.dto.lsds.OfferGoodsListQueryDTO;
import com.wanlianyida.user.application.model.dto.lsds.OfferListQueryDTO;
import com.wanlianyida.user.application.model.query.lsds.OfferDetailsQuery;
import com.wanlianyida.user.application.model.query.lsds.OfferGoodsListQuery;
import com.wanlianyida.user.application.model.query.lsds.OfferListQuery;
import com.wanlianyida.user.application.model.query.lsds.RoundingModeQuery;
import com.wanlianyida.user.infrastructure.enums.CrmLineTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

@Slf4j
@Service
public class LsdsExchangeService {

    @Resource
    private GoodsSourceInter goodsSourceInter;

    @Resource
    private LsdsGoodsOfferInter lsdsGoodsOfferInter;

    @Resource
    private GoodsConfigInter goodsConfigInter;


    /**
     * 获取货源列表
     */
    public ResultMode<GoodsSourceListDTO> queryPage(PagingInfo<GoodsSourceListQuery> query) {
        return goodsSourceInter.queryPage(query);
    }

    /**
     * 查询货源详情
     */
    public ResultMode<GoodsSourceDetailDTO> queryDetail(GoodsSourceQuery query) {
        return goodsSourceInter.queryDetail(query);
    }

    /**
     * 发布货源
     */
    public ResultMode<PublishGoodsSourceDTO> publish(PublishGoodsSourceCommand command, String goodsId) {
        if (StrUtil.isNotBlank(goodsId)) {
            PublishGoodsSourceAgainCommand updateCommand = BeanUtil.toBean(command, PublishGoodsSourceAgainCommand.class);
            updateCommand.setGoodsId(goodsId);
            return goodsSourceInter.publishAgain(updateCommand);
        }
        return goodsSourceInter.publish(command);
    }

    /**
     * 保存草稿箱后列表一键发布
     */
    public ResultMode publishOneClick(PublishOneClickCommand command) {
        return goodsSourceInter.publishOneClick(command);
    }

    public ResultMode<?> deleteGoods(GoodsIdCommand command) {
        return goodsSourceInter.deleteGoods(command);
    }

    public ResultMode<?> closeGoods(GoodsIdCommand command) {
        return goodsSourceInter.closeGoods(command);
    }

    /**
     * 报价单列表
     * @param pageInfo
     * @return
     */
    public ResultMode<OfferListQueryDTO> offerListQuery(PagingInfo<OfferListQuery> pageInfo) {
        GoodsOfferListPageQuery query = BeanUtil.toBean(pageInfo.getFilterModel(), GoodsOfferListPageQuery.class);
        PagingInfo<GoodsOfferListPageQuery> pagingInfo = new PagingInfo<>();
        BeanUtil.copyProperties(pageInfo, pagingInfo, CopyOptions.create().ignoreNullValue().ignoreError());
        pagingInfo.setFilterModel(query);
        ResultMode<GoodsOfferListPageDTO> resultMode = lsdsGoodsOfferInter.getGoodsOfferListPage(pagingInfo);
        if (!resultMode.isSucceed()) {
            return ResultMode.fail(resultMode.getErrCode(),resultMode.getErrMsg());
        }
        if (IterUtil.isEmpty(resultMode.getModel()) || ObjUtil.isNull(CollUtil.getFirst(resultMode.getModel()))) {
            return ResultMode.success();
        }
        List<OfferListQueryDTO> dtoList = BeanUtil.copyToList(resultMode.getModel(), OfferListQueryDTO.class);
        ResultMode<OfferListQueryDTO> result = new ResultMode<>();
        result.setTotal(resultMode.getTotal());
        result.setModel(dtoList);
        return result;
    }

    /**
     * 查询报价货源详情
     * @param query
     * @return
     */
    public ResultMode<OfferDetailsQueryDTO> offerDetailsQuery(OfferDetailsQuery query) {
        LsdsGoodsGetDetailsQuery lsdsGoodsGetDetailsQuery = BeanUtil.toBean(query, LsdsGoodsGetDetailsQuery.class);
        ResultMode<LsdsGoodsGetDetailsDTO> resultMode = lsdsGoodsOfferInter.lsdsGoodsGetDetails(lsdsGoodsGetDetailsQuery);
        if (!resultMode.isSucceed()) {
            return ResultMode.fail(resultMode.getErrCode(),resultMode.getErrMsg());
        }
        if (IterUtil.isEmpty(resultMode.getModel()) || ObjUtil.isNull(CollUtil.getFirst(resultMode.getModel()))) {
            return ResultMode.success();
        }
        OfferDetailsQueryDTO dto = BeanUtil.toBean(resultMode.getModel().get(0), OfferDetailsQueryDTO.class);
        ResultMode<OfferDetailsQueryDTO> result = new ResultMode<>();
        result.getModel().add(dto);
        return result;
    }

    /**
     * 我要承运-货源列表
     * @param pageInfo
     * @return
     */
    public ResultMode<OfferGoodsListQueryDTO> offerGoodsListQuery(PagingInfo<OfferGoodsListQuery> pageInfo) {
        GoodsListPageQuery query = BeanUtil.toBean(pageInfo.getFilterModel(), GoodsListPageQuery.class);
        PagingInfo<GoodsListPageQuery> pagingInfo = new PagingInfo<>();
        BeanUtil.copyProperties(pageInfo, pagingInfo, CopyOptions.create().ignoreNullValue().ignoreError());
        pagingInfo.setFilterModel(query);
        ResultMode<GoodsListPageDTO> resultMode = lsdsGoodsOfferInter.getGoodsListPage(pagingInfo);
        if (!resultMode.isSucceed()) {
            return ResultMode.fail(resultMode.getErrCode(),resultMode.getErrMsg());
        }
        if (IterUtil.isEmpty(resultMode.getModel()) || ObjUtil.isNull(CollUtil.getFirst(resultMode.getModel()))) {
            return ResultMode.success();
        }
        List<OfferGoodsListQueryDTO> offerGoodsListQueryDTOS = BeanUtil.copyToList(resultMode.getModel(), OfferGoodsListQueryDTO.class);
        ResultMode<OfferGoodsListQueryDTO> result = new ResultMode<>();
        result.setTotal(resultMode.getTotal());
        result.setModel(offerGoodsListQueryDTOS);
        return result;
    }

    /**
     * 新增报价单
     * @param command
     * @return
     */
    public void offerAdd(OfferAddCommand command) {
        log.info("offerAdd#请求参数：{}", JSONUtil.toJsonStr(command));
        lsdsGoodsOfferCommand lsdsGoodsOfferCommand = BeanUtil.copyProperties(command, lsdsGoodsOfferCommand.class);
        lsdsGoodsOfferInter.lsdsGoodsOfferAdd(lsdsGoodsOfferCommand);
    }

    /**
     * 查询企业配置
     * @param query
     * @return
     */
    public Map<String, Integer> roundingModeQuery(RoundingModeQuery query) {
        log.info("roundingModeQuery#请求参数：{}", JSONUtil.toJsonStr(query));
        ResultMode<Map<String, Integer>> resultMode = goodsConfigInter.getRoundingMode(query.getBizIds());
        if (IterUtil.isEmpty(resultMode.getModel()) || ObjUtil.isNull(CollUtil.getFirst(resultMode.getModel()))) {
            return null;
        }
        return resultMode.getModel().get(0);
    }


    public List<LineValidGoodsDTO> lineValidGoodsQuery(List<String> lineIdList) {
        if(CollUtil.isEmpty(lineIdList)){
            return null;
        }
        LineValidGoodsQuery query = new LineValidGoodsQuery();
        query.setLineIds(lineIdList);
        ResultMode<com.wanlianyida.lsds.api.model.dto.LineValidGoodsDTO> resultMode = goodsSourceInter.lineValidGoodsQuery(query);
        if(ObjUtil.isNull(resultMode.getModel()) || !resultMode.isSucceed() || CollUtil.isEmpty(resultMode.getModel())){
            log.info("lineValidGoodsQuery#查询有效货源请求参数:{},返回结果", JSONUtil.toJsonStr(lineIdList),JSONUtil.toJsonStr(resultMode));
            return null;
        }
        List<com.wanlianyida.lsds.api.model.dto.LineValidGoodsDTO> model = resultMode.getModel();
        return BeanUtil.copyToList(model, LineValidGoodsDTO.class);
    }

    /**
     * 构建更新goods address 信息
     * @param lineGoodsMap
     * @param lineIds
     * @param command
     */
    public void lineGoodsAddressUpdate(Map<String, List<String>> lineGoodsMap, List<LineInfoDTO> lineIds, AddressEditCommand command) {

        List<LineGoodsAddressUpdateCommand.LineData> lineDateList = new ArrayList<>();
        for (LineInfoDTO lineId : lineIds) {
            List<String> goodsIds = lineGoodsMap.get(lineId.getLineId());
            if(CollUtil.isEmpty(goodsIds)){
                continue;
            }
            for (String goodsId : goodsIds) {
                LineGoodsAddressUpdateCommand.LineData lineDate = new LineGoodsAddressUpdateCommand.LineData();
                lineDate.setGoodsId(goodsId);
                String lineType = lineId.getLineType();
                if(StrUtil.equals(lineType, CrmLineTypeEnum.TYPE_10.getType())){
                    lineDate.setSendAddrId(lineId.getSendAddrId());
                    lineDate.setStartSendLinker(command.getStartSendLinker());
                    lineDate.setStartSendPhoneNumber(command.getStartSendPhoneNumber());
                }else {
                    lineDate.setReceiveAddrId(lineId.getSendAddrId());
                    lineDate.setEndReceiveLinker(command.getStartSendLinker());
                    lineDate.setEndReceivePhoneNumber(command.getStartSendPhoneNumber());
                }
                lineDateList.add(lineDate);
            }
        }
        if(CollUtil.isEmpty(lineDateList)){
            return;
        }
        LineGoodsAddressUpdateCommand lineGoodsAddressUpdateCommand = new LineGoodsAddressUpdateCommand();
        lineGoodsAddressUpdateCommand.setLineDateList(lineDateList);
        ResultMode resultMode = goodsSourceInter.lineGoodsAddressUpdate(lineGoodsAddressUpdateCommand);
        log.info("更新货源联系人信息返回结果:{}",JSONUtil.toJsonStr(resultMode));
    }
}
