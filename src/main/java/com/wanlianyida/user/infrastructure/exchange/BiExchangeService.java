package com.wanlianyida.user.infrastructure.exchange;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjUtil;
import com.wanlianyida.bi.api.inter.BIListInter;
import com.wanlianyida.bi.api.inter.waybill.BiWaybillInter;
import com.wanlianyida.bi.api.model.query.BIListQuery;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.dto.bi.MetadataDTO;
import com.wanlianyida.user.application.model.query.bi.BiMetadataQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

@Slf4j
@Service
public class BiExchangeService {

    @Resource
    private BiWaybillInter biWaybillInter;
    @Resource
    private BIListInter biListInter;

    /**
     * 查询托运人运单列表
     * @param pagingInfo
     * @return
     */
    public ResultMode<Map<String, Object>> shipperWaybillList(PagingInfo<Map<String, Object>> pagingInfo){
        if(ObjUtil.isNull(pagingInfo)){
            return ResultMode.fail("参数为空");
        }
        Map<String, Object> map = BeanUtil.toBean(pagingInfo.getFilterModel(), Map.class);
        map.put("pageNum",pagingInfo.getCurrentPage());
        map.put("pageSize",pagingInfo.getPageLength());
        map.put("returnTotalNum",true);
        return biWaybillInter.shipperWaybillList(map);
    }

    /**
     * 工作台数据统计
     */
    public MetadataDTO workbenchStatistics(BiMetadataQuery query, String url){
        Map<String, Object> param = new HashMap<>();
//        if (StrUtil.isNotBlank(query.getShipperCompanyId())) {
//            param.put("shipperCompanyId", query.getShipperCompanyId());
//        }
//        if (StrUtil.isNotBlank(query.getCarrierCompanyId())) {
//            param.put("carrierCompanyId", query.getCarrierCompanyId());
//        }
//        if (StrUtil.isNotBlank(query.getNetworkMainBodyId())) {
//            param.put("networkMainBodyId", query.getNetworkMainBodyId());
//        }
        BIListQuery biListQuery = new BIListQuery();
        biListQuery.setUrl(url);
        biListQuery.setBiParam(param);
        ResultMode<Map<String, Object>> resultMode = biListInter.queryBaseListData(biListQuery);
        log.info("工作台数据统计#返回参数:{}", resultMode);
        if(resultMode.isSucceed() && CollectionUtil.isNotEmpty(resultMode.getModel())){
            return BeanUtil.mapToBean(resultMode.getModel().get(0), MetadataDTO.class, true, CopyOptions.create().ignoreError());
        }
        return null;
    }
}
