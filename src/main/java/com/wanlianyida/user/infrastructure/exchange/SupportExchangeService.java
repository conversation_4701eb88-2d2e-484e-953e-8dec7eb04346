package com.wanlianyida.user.infrastructure.exchange;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.support.api.inter.HolidaySchedulingInter;
import com.wanlianyida.support.api.inter.UserConfigInter;
import com.wanlianyida.support.api.model.command.WorkbenchDateScopeCommand;
import com.wanlianyida.support.api.model.query.HolidaySchedulingQuery;
import com.wanlianyida.support.api.model.query.UserConfigQuery;
import com.wanlianyida.user.application.model.dto.support.UserConfigDTO;
import com.wanlianyida.user.application.model.query.support.ExternalHolidaySchedulingQuery;
import org.springframework.stereotype.Service;

import java.util.Date;

import javax.annotation.Resource;
/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年07月25日 13:11
 */
@Service
public class SupportExchangeService {

    @Resource
    private UserConfigInter userConfigInter;

    @Resource
    private HolidaySchedulingInter holidaySchedulingInter;

    public ResultMode getScheduling() {
        HolidaySchedulingQuery query = new HolidaySchedulingQuery();
        query.setSchedulingDate(new Date());
        return holidaySchedulingInter.getScheduling(query);
    }

    public ResultMode<?> saveWorkbenchDateScope(WorkbenchDateScopeCommand command) {
        ResultMode<?> resultMode = userConfigInter.saveWorkbenchDateScope(command);
        if (!resultMode.isSucceed()) {
            return ResultMode.fail(resultMode.getCode(), resultMode.getMessage());
        }
        return ResultMode.success();
    }

    public ResultMode<UserConfigDTO> getWorkbenchDateScope(UserConfigQuery query) {
        ResultMode<com.wanlianyida.support.api.model.dto.UserConfigDTO> resultMode = userConfigInter.getWorkbenchDateScope(query);
        if (!resultMode.isSucceed()) {
            return ResultMode.fail(resultMode.getCode(),resultMode.getMessage());
        }
        if (CollectionUtil.isEmpty(resultMode.getModel())) {
            return ResultMode.success();
        }
        return ResultMode.success(BeanUtil.toBean(resultMode.getModel().get(0), UserConfigDTO.class));
    }

    public ResultMode getScheduling(ExternalHolidaySchedulingQuery query) {
        return holidaySchedulingInter.getScheduling(BeanUtil.toBean(query, HolidaySchedulingQuery.class));
    }
}
