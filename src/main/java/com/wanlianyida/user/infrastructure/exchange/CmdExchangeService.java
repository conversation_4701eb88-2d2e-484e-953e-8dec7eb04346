package com.wanlianyida.user.infrastructure.exchange;

import com.wanlianyida.cmd.api.inter.CmdCarInter;
import com.wanlianyida.cmd.api.model.dto.CmdCarDTO;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class CmdExchangeService {

    @Resource
    private CmdCarInter cmdCarInter;


    public ResultMode<CmdCarDTO> cmdCarPage(PagingInfo pagingInfo) {
        return cmdCarInter.queryByCondition(pagingInfo);
    }
}
