package com.wanlianyida.user.infrastructure.exchange;

import cn.hutool.core.bean.BeanUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.qrs.api.inter.QrsIdentifyCodeDisplayConfigInter;
import com.wanlianyida.qrs.api.inter.QrsIdentifyCodeGoodsInter;
import com.wanlianyida.qrs.api.model.command.IdentifyCodeDisplayConfigCommand;
import com.wanlianyida.qrs.api.model.command.IdentifyCodeGoodsBindCommand;
import com.wanlianyida.qrs.api.model.query.IdentifyCodeDisplayConfigByShareCodeQuery;
import com.wanlianyida.qrs.api.model.query.IdentifyCodeDisplayConfigQuery;
import com.wanlianyida.user.application.model.dto.qrs.IdentifyCodeDisplayConfigDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年07月03日 13:21
 */
@Service
public class QrsExchangeService {

    @Resource
    private QrsIdentifyCodeDisplayConfigInter identifyCodeDisplayConfigInter;
    @Resource
    private QrsIdentifyCodeGoodsInter identifyCodeGoodsInter;


    public ResultMode<IdentifyCodeDisplayConfigDTO> queryByShareCode(IdentifyCodeDisplayConfigByShareCodeQuery apiQuery) {
        ResultMode<com.wanlianyida.qrs.api.model.dto.IdentifyCodeDisplayConfigDTO> resultMode = identifyCodeDisplayConfigInter.queryByShareCode(apiQuery);
        if (!resultMode.isSucceed()) {
            return ResultMode.fail(resultMode.getCode(),resultMode.getErrMsg());
        }
        return ResultMode.success(BeanUtil.toBean(resultMode.getModel().get(0), IdentifyCodeDisplayConfigDTO.class));
    }

    public ResultMode<IdentifyCodeDisplayConfigDTO> queryDisplayConfig(IdentifyCodeDisplayConfigQuery apiQuery) {
        ResultMode<com.wanlianyida.qrs.api.model.dto.IdentifyCodeDisplayConfigDTO> resultMode = identifyCodeDisplayConfigInter.query(apiQuery);
        if (!resultMode.isSucceed()) {
            return ResultMode.fail(resultMode.getCode(),resultMode.getErrMsg());
        }
        return ResultMode.success(BeanUtil.toBean(resultMode.getModel().get(0), IdentifyCodeDisplayConfigDTO.class));
    }

    public ResultMode<String> insertDisplayConfig(IdentifyCodeDisplayConfigCommand apiCommand) {
        ResultMode<String> resultMode = identifyCodeDisplayConfigInter.insert(apiCommand);
        if (!resultMode.isSucceed()) {
            return ResultMode.fail(resultMode.getCode(),resultMode.getErrMsg());
        }
        return ResultMode.success(resultMode.getModel().get(0));
    }

    public ResultMode<String> bind(IdentifyCodeGoodsBindCommand command) {
        ResultMode<String> result = identifyCodeGoodsInter.bind(command);
        if (!result.isSucceed()) {
            return ResultMode.fail(result.getCode(),result.getErrMsg());
        }
        return ResultMode.success(result.getModel().get(0));
    }
}
