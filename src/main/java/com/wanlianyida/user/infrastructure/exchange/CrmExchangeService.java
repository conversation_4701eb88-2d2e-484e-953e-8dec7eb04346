package com.wanlianyida.user.infrastructure.exchange;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.crm.api.inter.CrmCompanyLineAddressInter;
import com.wanlianyida.crm.api.inter.CrmCustomerInter;
import com.wanlianyida.crm.api.inter.CrmLineCoreInter;
import com.wanlianyida.crm.api.model.command.CreateCrmCompanyLineCommand;
import com.wanlianyida.crm.api.model.command.CrmCommonAddressCommand;
import com.wanlianyida.crm.api.model.dto.CrmCompanyLineAddressDTO;
import com.wanlianyida.crm.api.model.dto.CrmCustomerDTO;
import com.wanlianyida.crm.api.model.dto.CrmDisAndTimeDTO;
import com.wanlianyida.crm.api.model.query.*;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.command.crm.AddressAddCommand;
import com.wanlianyida.user.application.model.command.crm.AddressDelAndAddCommand;
import com.wanlianyida.user.application.model.command.crm.AddressDelCommand;
import com.wanlianyida.user.application.model.command.crm.AddressEditCommand;
import com.wanlianyida.user.application.model.dto.crm.*;
import com.wanlianyida.user.application.model.query.crm.AddressCanEditCheckQuery;
import com.wanlianyida.user.application.model.query.crm.AddressListQuery;
import com.wanlianyida.user.application.model.query.crm.AssociationLineQuery;
import com.wanlianyida.user.infrastructure.enums.LineTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Slf4j
@Service
public class CrmExchangeService {

    @Resource
    private CrmCompanyLineAddressInter crmCompanyLineAddressInter;
    @Resource
    private CrmCustomerInter crmCustomerInter;

    @Resource
    private CrmLineCoreInter crmLineCoreInter;

    /**
     * 创建线路
     *
     * @param companyId
     * @param lineId
     * @param sendAddrShortName
     * @param receiveAddrShortName
     * @param startLineId
     * @param endLineId
     * @param isTransaction 是否转交易
     * @return
     */
    public ResultMode<CrmCompanyLineAddressDTO> createCrmCompanyLine(String companyId, String lineId,
                                                                     String sendAddrShortName, String receiveAddrShortName,
                                                                     String startLineId, String endLineId, boolean isTransaction) {
        if (StrUtil.isEmpty(lineId)) {
            ResultMode resultMode = createCrmCompanyLine(sendAddrShortName + "-"
                    + receiveAddrShortName, startLineId, endLineId, LineTypeEnum.RELEASE_GOODS.getCode(), companyId);
            if (!resultMode.isSucceed()) {
                return resultMode;
            }
            lineId = resultMode.getModel().get(0).toString();
        }
        CrmCompanyLineAddressDTO lineDTO = getCrmCompanyLine(lineId);
        if (lineDTO == null) {
            return ResultMode.fail("未找到对应的线路");
        }
        if (!StrUtil.equals(lineDTO.getCompanyId(), companyId) && !isTransaction) {
            return ResultMode.fail("线路" + lineDTO.getLineShortName() + "在该企业下不存在");
        }
        return ResultMode.success(lineDTO);
    }

    /**
     * 创建线路
     *
     * @param lineShortname
     * @param sendAddrId
     * @param receiveAddrId
     * @param lineSource
     * @param companyId
     * @return
     */
    public ResultMode createCrmCompanyLine(String lineShortname, String sendAddrId, String receiveAddrId, String lineSource, String companyId) {
        CreateCrmCompanyLineCommand command = new CreateCrmCompanyLineCommand();
        command.setLineShortName(lineShortname);
        command.setSendAddrId(sendAddrId);
        command.setReceiveAddrId(receiveAddrId);
        command.setLineSource(lineSource);
        command.setCompanyId(companyId);
        return crmCompanyLineAddressInter.createCrmCompanyLine(command);
    }

    /**
     * 获取线路信息
     *
     * @param lineId
     * @return
     */
    public CrmCompanyLineAddressDTO getCrmCompanyLine(String lineId) {
        ResultMode<CrmCompanyLineAddressDTO> lineAddressModel = crmCompanyLineAddressInter.getLineAddressByLineId(new CrmCompanyLineAddressQuery(lineId));
        return CollUtil.getFirst(lineAddressModel.getModel());
    }

    /**
     * 根据客户ID查询客户信息
     */
    public CrmCustomerDTO getCustomerById(String customerId) {
        if (StrUtil.isEmpty(customerId)) {
            return null;
        }
        ResultMode<CrmCustomerDTO> resultMode = crmCustomerInter.getBeanByKey(customerId);
        if (!resultMode.isSucceed() || IterUtil.isEmpty(resultMode.getModel())) {
            return null;
        }
        return IterUtil.getFirst(resultMode.getModel());
    }

    public ResultMode<AddressListDTO> addressListQuery(PagingInfo<AddressListQuery> query) {
        log.info("addressListQuery#查询请求参数：{}",JSONUtil.toJsonStr(query));
        PagingInfo<CrmCompanyLineAddressPagingQuery> pagingInfo = JSONUtil.toBean(JSONUtil.toJsonStr(query), new TypeReference<PagingInfo<CrmCompanyLineAddressPagingQuery>>() {
        }, false);
        ResultMode<CrmCompanyLineAddressDTO> resultMode = crmCompanyLineAddressInter.crmCompanyLineAddressPaging(pagingInfo);
        if(ObjUtil.isEmpty(query) || !resultMode.isSucceed() || IterUtil.isEmpty(resultMode.getModel())) {
            return ResultMode.success();
        }
        return JSONUtil.toBean(JSONUtil.toJsonStr(resultMode), new TypeReference<ResultMode<AddressListDTO>>() {
        }, false);
    }


    /**
     * 地址是否可以编辑校验
     * @param query
     * @return
     */
    public ResultMode addressCanEditCheck(AddressCanEditCheckQuery query) {
        log.info("addressCanEditCheck#请求参数:{}",JSONUtil.toJsonStr(query));
        CrmCommonAddressQuery commonAddressQuery = BeanUtil.toBean(query, CrmCommonAddressQuery.class);
        return crmCompanyLineAddressInter.crmCommonAddressLineCheck(commonAddressQuery);
    }

    /**
     * 常用地址新增
     * @return
     */
    public ResultMode<AddressAddDTO> addressAdd(AddressAddCommand command) {
        log.info("addressAdd#新增地址请求参数:{}",JSONUtil.toJsonStr(command));
        CrmCommonAddressCommand crmCommonAddressDTO = BeanUtil.toBean(command, CrmCommonAddressCommand.class);
        ResultMode resultMode = crmCompanyLineAddressInter.crmCommonAddressAdd(crmCommonAddressDTO);
        if(ObjUtil.isNull(resultMode)) {
            log.info("addressAdd#新增地址失败:{}",JSONUtil.toJsonStr(resultMode));
            return ResultMode.fail("新增地址失败");
        }
        if(!resultMode.isSucceed() || IterUtil.isEmpty(resultMode.getModel())){
            return ResultMode.fail(resultMode.getErrMsg());
        }
        Object lineId = IterUtil.getFirst(resultMode.getModel());
        Object notNullLineId = Opt.ofNullable(lineId).orElse("");
        AddressAddDTO addressAddDTO = new AddressAddDTO().setLineId(notNullLineId.toString());
        return ResultMode.success(addressAddDTO);
    }

    /**
     * 地址编辑
     * @return
     */
    public ResultMode addressEdit(AddressEditCommand command) {
        log.info("addressEdit#请求参数：{}",JSONUtil.toJsonStr(command));
        CrmCommonAddressCommand commonAddressCommand = BeanUtil.toBean(command, CrmCommonAddressCommand.class);
        return crmCompanyLineAddressInter.crmCommonAddressUpdate(commonAddressCommand);
    }

    /**
     * 地址删除
     * @return
     */
    public ResultMode addressDel(AddressDelCommand command) {
        log.info("addressDel#请求参数:{}",JSONUtil.toJsonStr(command));
        return crmCompanyLineAddressInter.crmCompanyLineAddressLocationDel(command.getLineId());
    }

    /**
     * 查看是否已经创建了线路
     * @return
     */
    public ResultMode<AssociationLineDTO> queryAssociationLine(AssociationLineQuery query) {
        log.info("queryAssociationLine#请求参数：{}",JSONUtil.toJsonStr(query));
        ResultMode<String> resultMode = crmCompanyLineAddressInter.crmCompanyLineAddressByLineId(query.getLineId());
        if(ObjUtil.isNull(resultMode)) {
            return ResultMode.fail("查询地址关联线路为空");
        }
        if(!resultMode.isSucceed() || CollUtil.isEmpty(resultMode.getModel())) {
            return ResultMode.fail(resultMode.getErrMsg());
        }
        AssociationLineDTO associationLineDTO = new AssociationLineDTO().setLineNumber(IterUtil.getFirst(resultMode.getModel()));
        return ResultMode.success(associationLineDTO);
    }

    /**
     * 地址删除并且新增前检查
     * @return
     */
    public ResultMode<AddressDelAndAddCheckDTO> addressDelAndAddCheck(AddressDelCommand command) {
        log.info("addressDelAndAddCheck#请求参数：{}",JSONUtil.toJsonStr(command));
        ResultMode resultMode = crmCompanyLineAddressInter.addAndDelAddrLineCheck(command.getLineId());
        if(ObjUtil.isNull(resultMode)) {
            return ResultMode.fail("查询失败");
        }
        if(!resultMode.isSucceed() || IterUtil.isEmpty(resultMode.getModel())) {
            return ResultMode.fail(resultMode.getErrMsg());
        }

        Object msg = IterUtil.getFirst(resultMode.getModel());
        Object notNullMsg = Opt.ofNullable(msg).orElse("");
        AddressDelAndAddCheckDTO addressDelAndAddCheckDTO = new AddressDelAndAddCheckDTO().setMsg(notNullMsg.toString());
        return ResultMode.success(addressDelAndAddCheckDTO);
    }

    /**
     * 地址删除并且新增
     * @return
     */
    public ResultMode<AddressAddDTO> addressDelAndAdd(AddressDelAndAddCommand command) {
        log.info("addressDelAndAdd#请求参数:{}",JSONUtil.toJsonStr(command));

        CrmCommonAddressCommand companyLineCommand = BeanUtil.toBean(command, CrmCommonAddressCommand.class);
        ResultMode resultMode = crmCompanyLineAddressInter.addAndDelAddrLineInfo(companyLineCommand);
        if(ObjUtil.isNull(resultMode)) {
            return ResultMode.fail("新增删除失败");
        }
        if(!resultMode.isSucceed() || IterUtil.isEmpty(resultMode.getModel())) {
            return ResultMode.fail(resultMode.getErrMsg());
        }
        Object lineId = IterUtil.getFirst(resultMode.getModel());
        Object notNullLineId = Opt.ofNullable(lineId).orElse("");
        AddressAddDTO addressAddDTO = new AddressAddDTO().setLineId(notNullLineId.toString());
        return ResultMode.success(addressAddDTO);
    }

    /**
     * 线路查询列表
     * @param lineId
     * @return
     */
    public List<LineInfoDTO> lineListQuery(String lineId) {
        log.info("lineListQuery#查询线路列表请求参数:{}",lineId);
        CrmLineListQuery crmLineListQuery = new CrmLineListQuery();
        crmLineListQuery.setLineId(lineId);
        ResultMode<CrmCompanyLineAddressDTO> resultMode = crmCompanyLineAddressInter.lineListQuery(crmLineListQuery);
        if(ObjUtil.isNull(resultMode) || !resultMode.isSucceed() || IterUtil.isEmpty(resultMode.getModel())) {
            log.info("lineListQuery#查询线路列表失败");
            return new ArrayList<>();
        }
        return resultMode.getModel().stream().map(ls -> {
            LineInfoDTO lineInfoDTO = new LineInfoDTO();
            lineInfoDTO.setLineId(ls.getLineId());
            lineInfoDTO.setLineType(ls.getLineType());
            lineInfoDTO.setSendAddrId(ls.getSendAddrId());
            lineInfoDTO.setStartSendLinker(ls.getStartSendLinker());
            lineInfoDTO.setStartSendPhoneNumber(ls.getStartSendPhoneNumber());
            lineInfoDTO.setSendShortName(ls.getSendShortName());
            return lineInfoDTO;
        }).collect(Collectors.toList());
    }

    public CrmDisAndTimeDTO getDisAndTime(CrmGetDisAndTimeQuery query){
        ResultMode<CrmDisAndTimeDTO> resultMode = crmLineCoreInter.getDisAndTime(query);
        if(ObjUtil.isNull(resultMode) || !resultMode.isSucceed() || IterUtil.isEmpty(resultMode.getModel())) {
            return new CrmDisAndTimeDTO();
        }
        return IterUtil.getFirst(resultMode.getModel());
    }
}
