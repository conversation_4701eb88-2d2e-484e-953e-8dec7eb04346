package com.wanlianyida.user.infrastructure.exchange;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.framework.lgicore.utils.JwtUtil;
import com.wanlianyida.oms.api.model.dto.LineValidOrderDTO;
import com.wanlianyida.tms.api.inter.*;
import com.wanlianyida.tms.api.model.command.EndAddressChangeCommand;
import com.wanlianyida.tms.api.model.command.LineOrderAddressUpdateCommand;
import com.wanlianyida.tms.api.model.command.TmsWaybillUpdCommand;
import com.wanlianyida.tms.api.model.command.TransportCarCommand;
import com.wanlianyida.tms.api.model.dto.*;
import com.wanlianyida.tms.api.model.query.*;
import com.wanlianyida.user.application.model.command.crm.AddressEditCommand;
import com.wanlianyida.user.application.model.dto.crm.LineInfoDTO;
import com.wanlianyida.user.application.model.dto.tms.OrderListQueryDTO;
import com.wanlianyida.user.application.model.dto.tms.WaybillListQueryDTO;
import com.wanlianyida.user.application.model.query.tms.*;
import com.wanlianyida.user.application.model.query.tms.SignWaybillListQuery;
import com.wanlianyida.user.infrastructure.enums.CrmLineTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 * TMS业务接口
 */
@Slf4j
@Service
public class TmsExchangeService {

    @Resource
    private TmsTranCapacityRecommondInter tmsTranCapacityRecommondInter;

    @Resource
    private TmsWaybillOptInter tmsWaybillOptInter;

    @Resource
    private TmsOrderInter tmsOrderInter;

    @Resource
    private TmsWaybillInter tmsWaybillInter;

    @Resource
    private WaybillChangeRecordInter waybillChangeRecordInter;

    @Resource
    private TmsWaybillAddressInter tmsWaybillAddressInter;

    @Resource
    private TmsAddressInter tmsAddressInter;

    @Resource
    private TmsWaybillStatusInter tmsWaybillStatusInter;

    /**
     * 运力推荐数据
     */
    public ResultMode<TmsRecommendedCapacityDTO> tranCapacityRecommendDataPage(PagingInfo<TmsRecommendedCapacityQuery> pageInfo) {
        return tmsTranCapacityRecommondInter.tranCapacityRecommendDataPage(pageInfo);
    }

    /**
     * 处理自有运力和平台运力车辆数据
     */
    public List<TransportCarDTO> handleCarTransport(List<TransportCarCommand> list) {
        ResultMode<TransportCarDTO> resultMode = tmsTranCapacityRecommondInter.handleCarTransport(list);
        if (!resultMode.isSucceed() || IterUtil.isEmpty(resultMode.getModel())) {
            return null;
        }

        return resultMode.getModel();
    }

    /**
     * 更新运单信息
     */
    public ResultMode updateWaybillInfo(TmsWaybillUpdCommand command) {
        log.info("updateWaybillInfo#command->{}", JSONUtil.toJsonStr(command));
        return tmsWaybillOptInter.updateWaybillInfo(command);
    }

    /**
     * 查询订单列表
     * @param pageInfo
     * @return
     */
    public ResultMode<OrderListQueryDTO> orderListQuery(PagingInfo<OrderListQuery> pageInfo) {
        TmsOrderListQuery query = BeanUtil.toBean(pageInfo.getFilterModel(), TmsOrderListQuery.class);
        PagingInfo<TmsOrderListQuery> pagingInfo = new PagingInfo<>();
        BeanUtil.copyProperties(pageInfo, pagingInfo, CopyOptions.create().ignoreNullValue().ignoreError());
        pagingInfo.setFilterModel(query);
        ResultMode<TmsOrderListDTO> resultMode = tmsOrderInter.orderList(pagingInfo);
        if (!resultMode.isSucceed()) {
            return ResultMode.fail(resultMode.getErrCode(),resultMode.getErrMsg());
        }
        if (IterUtil.isEmpty(resultMode.getModel()) || ObjUtil.isNull(CollUtil.getFirst(resultMode.getModel()))) {
            return ResultMode.success();
        }
        List<OrderListQueryDTO> dtoList = BeanUtil.copyToList(resultMode.getModel(), OrderListQueryDTO.class);
        ResultMode<OrderListQueryDTO> result = new ResultMode<>();
        result.setTotal(resultMode.getTotal());
        result.setModel(dtoList);
        return result;
    }

    /**
     * 查询运单列表
     * @param pageInfo
     * @return
     */
    public ResultMode<WaybillListQueryDTO> waybillListQuery(PagingInfo<WaybillListQuery> pageInfo) {
        TmsWaybillPagingQuery query = BeanUtil.toBean(pageInfo.getFilterModel(), TmsWaybillPagingQuery.class);
        query.setCreateDate(pageInfo.getFilterModel().getCreateDateStart());
        PagingInfo<TmsWaybillPagingQuery> pagingInfo = new PagingInfo<>();
        BeanUtil.copyProperties(pageInfo, pagingInfo, CopyOptions.create().ignoreNullValue().ignoreError());
        pagingInfo.setFilterModel(query);
        ResultMode<TmsWaybillPagingDTO> resultMode = tmsWaybillInter.tmsWaybillPaging(pagingInfo);
        if (!resultMode.isSucceed()) {
            return ResultMode.fail(resultMode.getErrCode(),resultMode.getErrMsg());
        }
        if (IterUtil.isEmpty(resultMode.getModel()) || ObjUtil.isNull(CollUtil.getFirst(resultMode.getModel()))) {
            return ResultMode.success();
        }
        List<WaybillListQueryDTO> dtoList = BeanUtil.copyToList(resultMode.getModel(), WaybillListQueryDTO.class);
        ResultMode<WaybillListQueryDTO> result = new ResultMode<>();
        result.setTotal(resultMode.getTotal());
        result.setModel(dtoList);
        return result;
    }
    /**
     * 修改目的地
     * @param command
     * @return
     */
    public ResultMode<?> addEndAddressChange(EndAddressChangeCommand command) {

        return waybillChangeRecordInter.addEndAddressChange(command);
    }

    /**
     * 获取最新变更记录-根据审核类型
     * @param query
     * @return
     */
    public ResultMode<WaybillChangeRecordDTO> getLatestChangeRecord(ExterLatestChangeRecordQuery query) {

        return waybillChangeRecordInter.getLatestChangeRecord(BeanUtil.toBean(query, LatestChangeRecordQuery.class));
    }


    /**
     * 获取运单详情
     * @param waybillId
     * @return
     */
    public TmsWaybillDetailDTO getWaybillDetail(String waybillId) {

        TmsWaybillDetailQuery query = new TmsWaybillDetailQuery();
        query.setWaybillId(waybillId);
        ResultMode<TmsWaybillDetailDTO> resultMode = tmsWaybillInter.getDetailById(query);
        if (!resultMode.isSucceed() || IterUtil.isEmpty(resultMode.getModel())) {
            return null;
        }
        return IterUtil.getFirst(resultMode.getModel());
    }

    /**
     * 获取运单地址详情
     * @param waybillAddressId
     * @return
     */
    public TmsWaybillAddressDTO getWaybillAddressDetail(String waybillAddressId) {
        WaybillAddressDetailQuery query = new WaybillAddressDetailQuery();
        query.setWaybillAddressId(waybillAddressId);
        ResultMode<TmsWaybillAddressDTO> resultMode = tmsWaybillAddressInter.getWaybillAddressDetail(query);
        if (!resultMode.isSucceed() || IterUtil.isEmpty(resultMode.getModel())) {
            return null;
        }
        return IterUtil.getFirst(resultMode.getModel());
    }

    /**
     * 更新地址信息
     * @param lineOrderMap
     * @param lineIds
     * @param command
     */
    public void lineOrderAddressUpdate(Map<String, List<LineValidOrderDTO>> lineOrderMap, List<LineInfoDTO> lineIds, AddressEditCommand command) {
        List<LineOrderAddressUpdateCommand.LineData> lineDateList = new ArrayList<>();
        for (LineInfoDTO lineInfo : lineIds) {
            List<LineValidOrderDTO> lineOrders = lineOrderMap.get(lineInfo.getLineId());
            if(CollUtil.isEmpty(lineOrders)) {
                continue;
            }
            for (LineValidOrderDTO lineOrder : lineOrders) {
                LineOrderAddressUpdateCommand.LineData lineDate = new LineOrderAddressUpdateCommand.LineData();
                lineDate.setOrderId(lineOrder.getOrderId());
                String lineType = lineInfo.getLineType();
                if(StrUtil.equals(lineType, CrmLineTypeEnum.TYPE_10.getType())){
                    lineDate.setSendAddrId(lineInfo.getSendAddrId());
                    lineDate.setStartSendLinker(command.getStartSendLinker());
                    lineDate.setStartSendPhoneNumber(command.getStartSendPhoneNumber());
                }else if(StrUtil.equals(lineType, CrmLineTypeEnum.TYPE_20.getType())) {
                    lineDate.setReceiveAddrId(lineInfo.getSendAddrId());
                    lineDate.setEndReceiveLinker(command.getStartSendLinker());
                    lineDate.setEndReceivePhoneNumber(command.getStartSendPhoneNumber());
                }else {
                    continue;
                }
                lineDateList.add(lineDate);
            }
        }
        if(CollUtil.isEmpty(lineDateList)){
            return;
        }
        LineOrderAddressUpdateCommand lineGoodsAddressUpdateCommand = new LineOrderAddressUpdateCommand();
        lineGoodsAddressUpdateCommand.setLineDateList(lineDateList);
        ResultMode resultMode = tmsAddressInter.lineGoodsAddressUpdate(lineGoodsAddressUpdateCommand);
        log.info("更新订单联系人信息返回结果:{}",JSONUtil.toJsonStr(resultMode));
    }


    /**
     * 签收运单列表
     * @param query
     * @return
     */
    public ResultMode<TmsWaybillDetailDTO> signWaybillList(SignWaybillListQuery query) {
        com.wanlianyida.tms.api.model.query.SignWaybillListQuery bean = BeanUtil.toBean(query, com.wanlianyida.tms.api.model.query.SignWaybillListQuery.class);
        log.info("signWaybillList#请求参数:{}",JSONUtil.toJsonStr(bean));
        return tmsWaybillStatusInter.signWaybillList(bean);
    }

    public ResultMode<PlateNumberDTO> waybillPlateNumberPage(PagingInfo<ExternalPlateNumberQuery> externalQueryPageInfo) {
        PlateNumberQuery query = BeanUtil.toBean(externalQueryPageInfo.getFilterModel(), PlateNumberQuery.class);
        query.setRealityCompanyId(JwtUtil.getCompanyIdByToken());
        //如果时间没有传值,默认去最近两年
        if(ObjUtil.isNull(query.getCreateStartDate()) && ObjUtil.isNull(query.getCreateEndDate())){
            Date nowDate = new Date();
            query.setCreateStartDate(DateUtil.offsetMonth(nowDate,-24));
            query.setCreateEndDate(nowDate);
        }
        PagingInfo<PlateNumberQuery> pagingInfo = new PagingInfo<>();
        BeanUtil.copyProperties(externalQueryPageInfo, pagingInfo, CopyOptions.create().ignoreNullValue().ignoreError());
        pagingInfo.setFilterModel(query);
        return tmsWaybillInter.waybillPlateNumberPage(pagingInfo);
    }
}
