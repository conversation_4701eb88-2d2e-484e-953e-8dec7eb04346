package com.wanlianyida.user.infrastructure.exchange;

import cn.hutool.core.collection.CollUtil;
import com.wanlianyida.bms.api.inter.BmsNetworkFreightPaymentInter;
import com.wanlianyida.bms.api.inter.BmsWaybillAccountInter;
import com.wanlianyida.bms.api.inter.BmsWaybillPoolInter;
import com.wanlianyida.bms.api.model.dto.BmsNetworkFreightPaymentDTO;
import com.wanlianyida.bms.api.model.dto.BmsWaybillAccountDTO;
import com.wanlianyida.bms.api.model.dto.BmsWaybillPoolDTO;
import com.wanlianyida.bms.api.model.query.BmsWaybillAccountQuery;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;

import javax.annotation.Resource;

@Slf4j
@Service
public class BmsExchangeService {


    @Resource
    private BmsWaybillPoolInter bmsWaybillPoolInter;
    @Resource
    private BmsNetworkFreightPaymentInter bmsNetworkFreightPaymentInter;
    @Resource
    private BmsWaybillAccountInter bmsWaybillAccountInter;


    public BmsWaybillPoolDTO getBmsWaybillPoolByWaybillId(String waybillId) {
        ResultMode<BmsWaybillPoolDTO> resultMode = bmsWaybillPoolInter.getbmsWayBillPoolByWayBillIds(Arrays.asList(waybillId));
        if (resultMode.isSucceed() && CollUtil.isNotEmpty(resultMode.getModel())) {
            return CollUtil.getFirst(resultMode.getModel());
        }
        return null;
    }

    public BmsNetworkFreightPaymentDTO getBmsNetworkFreightPaymentByWaybillId(String waybillId) {
        ResultMode<BmsNetworkFreightPaymentDTO> resultMode = bmsNetworkFreightPaymentInter.getBybusBillIds(Arrays.asList(waybillId));
        if (resultMode.isSucceed() && CollUtil.isNotEmpty(resultMode.getModel())) {
            return CollUtil.getFirst(resultMode.getModel());
        }
        return null;
    }

    public BmsWaybillAccountDTO getBmsWaybillAccountByWaybillId(String waybillId) {
        ResultMode<BmsWaybillAccountDTO> resultMode = bmsWaybillAccountInter.getBmsWaybillAccountByWaybillId(new BmsWaybillAccountQuery(waybillId));
        if (resultMode.isSucceed() && CollUtil.isNotEmpty(resultMode.getModel())) {
            return CollUtil.getFirst(resultMode.getModel());
        }
        return null;
    }


}
