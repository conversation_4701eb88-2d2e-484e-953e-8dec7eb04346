package com.wanlianyida.user.infrastructure.exchange;

import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.platform.api.model.dto.PlatformUmDriverReceiptInfoDTO;
import com.wanlianyida.tcs.api.inter.TcsCarBusinessInter;
import com.wanlianyida.tcs.api.inter.TcsDriverBusinessInter;
import com.wanlianyida.tcs.api.model.dto.TcsCarLicenseVO;
import com.wanlianyida.tcs.api.model.dto.TcsCompanyCarCapacityVO;
import com.wanlianyida.tcs.api.model.dto.TcsCompanyDriverCapacityVO;
import com.wanlianyida.tcs.api.model.dto.TcsDriverVO;
import com.wanlianyida.tcs.api.model.query.TcsCarPlateNoAndColorFilter;
import com.wanlianyida.tcs.api.model.query.TcsCarTypeQuery;
import com.wanlianyida.tcs.api.model.query.TcsDriverFilter;
import com.wanlianyida.tms.api.model.dto.TransportCarDTO;
import com.wanlianyida.tms.api.model.query.CarTransportCapacityQuery;
import com.wanlianyida.user.application.assembler.AssignDriverTransportCapacityAssemble;
import com.wanlianyida.user.application.assembler.CarTransportCapacityAssemble;
import com.wanlianyida.user.application.assembler.DriverTransportCapacityAssemble;
import com.wanlianyida.user.application.model.dto.tcs.AssignDriverTransportCapacityDTO;
import com.wanlianyida.user.application.model.dto.tcs.DriverTransportCapacityDTO;
import com.wanlianyida.user.application.model.query.tcs.AssignDriverTransportCapacityQuery;
import com.wanlianyida.user.application.model.query.tcs.DriverTransportCapacityQuery;
import com.wanlianyida.user.infrastructure.enums.LgiOrchUserEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * TCS业务接口
 */
@Slf4j
@Service
public class TcsExchangeService {

    @Resource
    private TcsCarBusinessInter tcsCarBusinessInter;
    @Resource
    private TcsDriverBusinessInter tcsDriverBusinessInter;

    @Resource
    private PlatformExchangeService platformExchangeService;

    /**
     * 根据车牌号+颜色批量查询车辆信息
     */
    public List<TcsCarLicenseVO> queryCarLicenseListByFilter(List<TcsCarPlateNoAndColorFilter> list) {
        if (IterUtil.isEmpty(list)) {
            return null;
        }

        ResultMode<TcsCarLicenseVO> resultMode = tcsCarBusinessInter.queryCarLicenseListByFilter(list);
        if (!resultMode.isSucceed() || IterUtil.isEmpty(resultMode.getModel())) {
            return null;
        }

        return resultMode.getModel();
    }

    /**
     * 自有运力和平台运力车辆查询（派车、服务单派车）
     */
    public ResultMode<TransportCarDTO> queryCompanyCarCapacity(PagingInfo<CarTransportCapacityQuery> pagingInfo) {
        log.info("queryCompanyCarCapacity#pagingInfo->{}", JSONUtil.toJsonStr(pagingInfo));
        // 入参转换
        ResultMode<TcsCompanyCarCapacityVO> resultModeExchange = tcsCarBusinessInter.queryCompanyCarCapacity(CarTransportCapacityAssemble.changeFilter(pagingInfo));
        //转换出参
        return CarTransportCapacityAssemble.changeResultMode(resultModeExchange);
    }

    /**
     * 发布司机货源查指定司机运力
     * @param pagingInfo
     * @return {@link ResultMode }<{@link AssignDriverTransportCapacityDTO }>
     */
    public ResultMode<AssignDriverTransportCapacityDTO> queryAssignDriverCapacity(PagingInfo<AssignDriverTransportCapacityQuery> pagingInfo) {
        log.info("queryAssignDriverCapacity#pagingInfo->{}", JSONUtil.toJsonStr(pagingInfo));
        //转换入参
        ResultMode<TcsCompanyDriverCapacityVO> resultModeExchange = tcsDriverBusinessInter.queryCompanyDriverCapacity(AssignDriverTransportCapacityAssemble.changeFilter(pagingInfo));
        //转换出参
        return AssignDriverTransportCapacityAssemble.changeResultMode(resultModeExchange);
    }

    /**
     * 派车查司机运力
     * @param pagingInfo
     * @return {@link ResultMode }<{@link DriverTransportCapacityDTO }>
     */
    public ResultMode<DriverTransportCapacityDTO> queryDriversCapacity(PagingInfo<DriverTransportCapacityQuery> pagingInfo) {
        log.info("queryDriversCapacity#pagingInfo->{}", JSONUtil.toJsonStr(pagingInfo));
        //转换入参
        ResultMode<TcsCompanyDriverCapacityVO> resultModeExchange = tcsDriverBusinessInter.queryCompanyDriverCapacity(DriverTransportCapacityAssemble.changeFilter(pagingInfo));
        //转换出参
        return DriverTransportCapacityAssemble.changeResultMode(resultModeExchange);
    }

    /**
     * 根据车牌号和颜色查询行驶证信息集合包含附件信息（批量）
     */
    public List<TcsCarLicenseVO> queryCarLicenseAttachmentByFilter(List<TcsCarPlateNoAndColorFilter> list) {
        if (IterUtil.isEmpty(list)) {
            return null;
        }

        ResultMode<TcsCarLicenseVO> resultMode = tcsCarBusinessInter.queryCarLicenseAttachmentByFilter(list);
        if (!resultMode.isSucceed() || IterUtil.isEmpty(resultMode.getModel())) {
            return null;
        }

        return resultMode.getModel();
    }

    /**
     * 根据车牌号和颜色查询行驶证信息集合包含附件信息
     */
    public TcsCarLicenseVO queryCarLicenseAttachment(TcsCarPlateNoAndColorFilter filter) {
        List<TcsCarLicenseVO> list = queryCarLicenseAttachmentByFilter(Arrays.asList(filter));
        if (IterUtil.isEmpty(list)) {
            return null;
        }

        return IterUtil.getFirst(list);
    }

    /**
     * driverId集合 查询 司机基础信息集合
     */
    public List<TcsDriverVO> queryBasicListByDriverCodeList(List<String> driverIdList) {
        if (IterUtil.isEmpty(driverIdList)) {
            return null;
        }

        TcsDriverFilter filter = new TcsDriverFilter();
        filter.setDriverCodeList(driverIdList);
        ResultMode<TcsDriverVO> resultMode = tcsDriverBusinessInter.queryBasicListByDriverCodeList(filter);
        if (!resultMode.isSucceed() || IterUtil.isEmpty(resultMode.getModel())) {
            return null;
        }

        return resultMode.getModel();
    }

    /**
     * 获取司机的注册状态 11-线上 、21-线下, 12-网络
     */
    public Map<String, String> getDriversLoginStatus(List<String> driverIdList) {
        if (IterUtil.isEmpty(driverIdList)) {
            return null;
        }
        List<TcsDriverVO> tcsDriverVOList = this.queryBasicListByDriverCodeList(driverIdList);
        if (IterUtil.isEmpty(tcsDriverVOList)) {
            return null;
        }

        List<String> userBaseIdList = tcsDriverVOList.stream().filter(vo -> StrUtil.isNotBlank(vo.getUserBaseId())).map(TcsDriverVO::getUserBaseId).collect(Collectors.toList());
        List<PlatformUmDriverReceiptInfoDTO> driverReceiptInfoList = platformExchangeService.getDriverReceiptInfoByUserBaseIdList(userBaseIdList);
        Map<String, PlatformUmDriverReceiptInfoDTO> driverReceiptInfoMap = driverReceiptInfoList.stream().collect(Collectors.toMap(PlatformUmDriverReceiptInfoDTO::getUserBaseId, Function.identity(), (v1, v2) -> v1));
        Map<String, String> map = new HashMap<>();
        for (TcsDriverVO vo : tcsDriverVOList) {
            if (StrUtil.isNotBlank(vo.getUserBaseId())) {
                map.put(vo.getDriverId(), LgiOrchUserEnum.DriverLoginStatusEnum.ONLINE.getCode());
                if (ObjUtil.isNotNull(driverReceiptInfoMap.get(vo.getUserBaseId()))) {
                    map.put(vo.getDriverId(), LgiOrchUserEnum.DriverLoginStatusEnum.NET.getCode());
                }
            } else {
                map.put(vo.getDriverId(), LgiOrchUserEnum.DriverLoginStatusEnum.OFFLINE.getCode());
            }
        }

        return map;
    }


    /**
     * 车辆车辆信息
     * @param pagingInfo
     * @return
     */
    public ResultMode<TcsCarLicenseVO> queryCompanyCarPage(PagingInfo<TcsCarTypeQuery> pagingInfo){
        return tcsCarBusinessInter.queryCompanyCarPage(pagingInfo);
    }

}
