package com.wanlianyida.user.infrastructure.exchange;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.ObjUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.woa.api.inter.WoaLogisticsPlanInter;
import com.wanlianyida.woa.api.inter.WoaOfferPlanInterInter;
import com.wanlianyida.woa.api.inter.WoaWaybillClueInter;
import com.wanlianyida.woa.api.model.command.WoaLogisticsPlanCommand;
import com.wanlianyida.woa.api.model.command.WoaOfferPlanCommand;
import com.wanlianyida.woa.api.model.dto.WoaLogisticsPlanDTO;
import com.wanlianyida.woa.api.model.dto.WoaOfferPlanDTO;
import com.wanlianyida.woa.api.model.query.WoaLogisticsPlanQuery;
import com.wanlianyida.woa.api.model.query.WoaOfferPlanQuery;
import com.wanlianyida.woa.api.model.vo.WoaWaybillClueCntVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

@Slf4j
@Service
public class WoaExchangeService {

    @Resource
    private WoaWaybillClueInter woaWaybillClueInter;
    @Resource
    private WoaLogisticsPlanInter woaLogisticsPlanInter;
    @Resource
    private WoaOfferPlanInterInter woaOfferPlanInterInter;
    /**
     * 推荐运力增值服务统计
     */
    public List<WoaWaybillClueCntVO> cntByBizIds(List<String> goodsIds) {
        ResultMode<WoaWaybillClueCntVO> resultMode = woaWaybillClueInter.cntByBizIds(goodsIds);
        if (ObjUtil.isNotNull(resultMode) && resultMode.isSucceed() && CollUtil.isNotEmpty(resultMode.getModel())) {
            return resultMode.getModel();
        }
        return null;
    }

    /**
     * 获取物流计划单
     */
    public WoaLogisticsPlanDTO getLogisticsPlanByPlanId(Long planId) {
        ResultMode<WoaLogisticsPlanDTO> resultMode = woaLogisticsPlanInter.selectById(new WoaLogisticsPlanQuery(planId));
        if(ObjUtil.isNull(resultMode) || !resultMode.isSucceed() || CollUtil.isEmpty(resultMode.getModel())){
            return null;
        }
        return IterUtil.getFirst(resultMode.getModel());
    }

    /**
     * 保存物流计划单
     */
    public void saveWoaLogisticsPlan(Long planId, String goodsId) {
        woaLogisticsPlanInter.save(new WoaLogisticsPlanCommand(planId, goodsId));
    }


    /**
     * 获取报价计划单
     */
    public WoaOfferPlanDTO getOfferPlanByPlanId(Long planId) {
        ResultMode<WoaOfferPlanDTO> resultMode = woaOfferPlanInterInter.selectById(new WoaOfferPlanQuery(planId));
        if(ObjUtil.isNull(resultMode) || !resultMode.isSucceed() || CollUtil.isEmpty(resultMode.getModel())){
            return null;
        }
        return IterUtil.getFirst(resultMode.getModel());
    }

    /**
     * 保存报价计划单
     */
    public void saveWoaOfferPlan(Long planId, String goodsId) {
        WoaOfferPlanCommand command=new WoaOfferPlanCommand();
        command.setId(planId);
        command.setGoodsId(goodsId);
        //司机货源
        command.setGoodsType("10");
        woaOfferPlanInterInter.save(command);
    }
}
