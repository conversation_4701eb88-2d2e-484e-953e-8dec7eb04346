package com.wanlianyida.user.infrastructure.exchange;

import cn.hutool.core.collection.IterUtil;
import com.wanlianyida.eval.api.inter.EvalDriverInfoInter;
import com.wanlianyida.eval.api.model.dto.EvalDriverInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * eval业务接口
 */
@Service
public class EvalExchangeService {

    @Resource
    private EvalDriverInfoInter evalDriverInfoInter;

    /**
     * 批量查询评价司机信息
     */
    public List<EvalDriverInfo> selectByDriverIds(List<String> driverIds) {
        if (IterUtil.isEmpty(driverIds)) {
            return null;
        }

        ResultMode<EvalDriverInfo> resultMode = evalDriverInfoInter.selectByDriverIds(driverIds);
        if (Objects.isNull(resultMode) || !resultMode.isSucceed() || IterUtil.isEmpty(resultMode.getModel())) {
            return null;
        }

        return resultMode.getModel();
    }

    /**
     * 批量查询评价司机信息
     */
    public Map<String, Double> getEvalDriver(List<String> driverIds) {
        List<EvalDriverInfo> evalDriverInfoList = this.selectByDriverIds(driverIds);
        if (IterUtil.isEmpty(evalDriverInfoList)) {
            return null;
        }

        return evalDriverInfoList.stream().collect(Collectors.toMap(EvalDriverInfo::getDriverId, EvalDriverInfo::getEvalDriverLevel, (v1, v2) -> v1));
    }

}
