package com.wanlianyida.user.infrastructure.exchange;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.basemdm.api.inter.MdmAccountInter;
import com.wanlianyida.basemdm.api.inter.MdmUserInfoInter;
import com.wanlianyida.basemdm.api.model.command.MdmPasswordCommand;
import com.wanlianyida.basemdm.api.model.dto.MdmUserInfoDTO;
import com.wanlianyida.basemdm.api.model.query.MdmUserRealNameQuery;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.fssmodel.ResponseMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class MdmExchangeService {

    @Resource
    private MdmUserInfoInter mdmUserInfoInter;

    @Resource
    private MdmAccountInter mdmAccountInter;

    /**
     * 通过手机号获取中台个人账户信息
     */
    public MdmUserInfoDTO findPlfUsrByMobile(String mobile) {
        MdmUserRealNameQuery param = new MdmUserRealNameQuery();
        param.setTelephone(mobile);
        param.setCreateSource("1");
        ResponseMessage<MdmUserInfoDTO> mdmUserInfoDTOResponseMessage = mdmUserInfoInter.queryRealNameByTelephone(param);
        log.info("findPlfUsrByMobile#请求参数:{},返回值：{}", JSONUtil.toJsonStr(param), JSONUtil.toJsonStr(mdmUserInfoDTOResponseMessage));
        if(ObjUtil.isNotNull(mdmUserInfoDTOResponseMessage) && mdmUserInfoDTOResponseMessage.isSucceed() && ObjUtil.isNotNull(mdmUserInfoDTOResponseMessage.getModel())){
            return mdmUserInfoDTOResponseMessage.getModel();
        }
        return null;
    }

    /**
     * 更新密码
     * @return
     */
    public ResultMode<String> changePassword(String oldPassword,String newPassword,String operatorId){
        MdmPasswordCommand passwordCommand = new MdmPasswordCommand();
        passwordCommand.setId(Long.parseLong(operatorId));
        passwordCommand.setAccountType(2);
        passwordCommand.setOldPassword(oldPassword);
        passwordCommand.setPassword(newPassword);
        ResponseMessage<Void> responseMessage = mdmAccountInter.changePassword(passwordCommand);
        log.info("changePassword#请求参数:{},返回值：{}", JSONUtil.toJsonStr(passwordCommand), JSONUtil.toJsonStr(responseMessage));
        if(ObjUtil.isNotNull(responseMessage) && responseMessage.isSucceed()){
            return ResultMode.success(newPassword);
        }
        return ResultMode.fail(responseMessage.getMessage());
    }

}
