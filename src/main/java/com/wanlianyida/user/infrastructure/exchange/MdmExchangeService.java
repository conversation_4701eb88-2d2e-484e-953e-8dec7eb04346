package com.wanlianyida.user.infrastructure.exchange;

import cn.hutool.core.util.ObjUtil;
import com.wanlianyida.basemdm.api.inter.MdmUserInfoInter;
import com.wanlianyida.basemdm.api.model.dto.MdmUserInfoDTO;
import com.wanlianyida.basemdm.api.model.query.MdmUserRealNameQuery;
import com.wanlianyida.fssmodel.ResponseMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class MdmExchangeService {


    @Resource
    private MdmUserInfoInter mdmUserInfoInter;


    /**
     * 通过手机号获取中台个人账户信息
     */
    public MdmUserInfoDTO findPlfUsrByMobile(String mobile) {
        MdmUserRealNameQuery param = new MdmUserRealNameQuery();
        param.setTelephone(mobile);
        param.setSysType("1");
        param.setCreateSource("1");
        ResponseMessage<MdmUserInfoDTO> mdmUserInfoDTOResponseMessage = mdmUserInfoInter.queryRealNameByTelephone(param);
        if(ObjUtil.isNotNull(mdmUserInfoDTOResponseMessage) && mdmUserInfoDTOResponseMessage.isSucceed() && ObjUtil.isNotNull(mdmUserInfoDTOResponseMessage.getModel())){
            return mdmUserInfoDTOResponseMessage.getModel();
        }
        return null;
    }
}
