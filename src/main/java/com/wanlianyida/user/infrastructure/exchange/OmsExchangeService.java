package com.wanlianyida.user.infrastructure.exchange;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.oms.api.inter.OmsOrderAddressInter;
import com.wanlianyida.oms.api.inter.OmsOrderInter;
import com.wanlianyida.oms.api.inter.OmsOrderQueryInter;
import com.wanlianyida.oms.api.model.command.LineOrderAddressUpdateCommand;
import com.wanlianyida.oms.api.model.dto.LineValidOrderDTO;
import com.wanlianyida.oms.api.model.dto.OmsCarrierOrderPageListDTO;
import com.wanlianyida.oms.api.model.dto.OmsOrderDetailDTO;
import com.wanlianyida.oms.api.model.dto.OmsShipperOrderPageListDTO;
import com.wanlianyida.oms.api.model.query.LineValidOrderQuery;
import com.wanlianyida.oms.api.model.query.OmsCarrierOrderPageQuery;
import com.wanlianyida.oms.api.model.query.OmsOrderDetailQuery;
import com.wanlianyida.oms.api.model.query.OmsShipperOrderPageQuery;
import com.wanlianyida.user.application.model.command.crm.AddressEditCommand;
import com.wanlianyida.user.application.model.dto.crm.LineInfoDTO;
import com.wanlianyida.user.application.model.dto.oms.CarrierOrderPageListDTO;
import com.wanlianyida.user.application.model.dto.oms.OmsOrderAddressDTO;
import com.wanlianyida.user.application.model.dto.oms.OrderDetailQueryDTO;
import com.wanlianyida.user.application.model.dto.oms.ShipperOrderPageListDTO;
import com.wanlianyida.user.application.model.query.oms.CarrierOrderPageQuery;
import com.wanlianyida.user.application.model.query.oms.OrderDetailQuery;
import com.wanlianyida.user.application.model.query.oms.ShipperOrderPageQuery;
import com.wanlianyida.user.infrastructure.enums.CrmLineTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

/**
 * OMS业务接口
 */
@Slf4j
@Service
public class OmsExchangeService {

    @Resource
    private OmsOrderInter omsOrderInter;

    @Resource
    private OmsOrderAddressInter omsOrderAddressInter;

    @Resource
    private OmsOrderQueryInter omsOrderQueryInter;


    /**
     * 查询订单详情
     * @param orderDetailQuery
     * @return
     */
    public ResultMode<OrderDetailQueryDTO> orderDetailQuery(OrderDetailQuery orderDetailQuery) {

        OmsOrderDetailQuery query = BeanUtil.toBean(orderDetailQuery, OmsOrderDetailQuery.class);
        ResultMode<OmsOrderDetailDTO> resultMode = omsOrderInter.queryOmsOrderDetail(query);
        if (!resultMode.isSucceed()) {
            return ResultMode.fail(resultMode.getErrCode(),resultMode.getErrMsg());
        }
        if (IterUtil.isEmpty(resultMode.getModel()) || ObjUtil.isNull(CollUtil.getFirst(resultMode.getModel()))) {
            return ResultMode.success();
        }
        OrderDetailQueryDTO dto = BeanUtil.toBean(resultMode.getModel().get(0), OrderDetailQueryDTO.class);
        ResultMode<OrderDetailQueryDTO> result = new ResultMode<>();
        result.getModel().add(dto);
        return result;
    }

    /**
     * 查询订单对应有效货源
     * @param lineIdList
     * @return
     */
    public List<LineValidOrderDTO> lineValidOrderQuery(List<String> lineIdList) {
        if(CollUtil.isEmpty(lineIdList)){
            return null;
        }
        LineValidOrderQuery query  = new LineValidOrderQuery();
        query.setLineIds(lineIdList);
        ResultMode<LineValidOrderDTO> resultMode = omsOrderAddressInter.lineValidGoodsQuery(query);
        if (ObjUtil.isNull(resultMode) || !resultMode.isSucceed() || CollUtil.isEmpty(resultMode.getModel())) {
            log.info("lineValidOrderQuery#查询有效订单请求参数：{},查询请求结果：{}", JSONUtil.toJsonStr(lineIdList),JSONUtil.toJsonStr(resultMode));
            return null;
        }
        return BeanUtil.copyToList(resultMode.getModel(), LineValidOrderDTO.class);
    }

    /**
     * 构建更新 order address 信息
     * @param lineOrderMap
     * @param lineIds
     * @param command
     */
    public void lineOrderAddressUpdate(Map<String, List<LineValidOrderDTO>> lineOrderMap, List<LineInfoDTO> lineIds, AddressEditCommand command) {

        List<LineOrderAddressUpdateCommand.LineData> lineDateList = new ArrayList<>();
        for (LineInfoDTO lineInfo : lineIds) {
            List<LineValidOrderDTO> lineOrders = lineOrderMap.get(lineInfo.getLineId());
            if(CollUtil.isEmpty(lineOrders)){
                continue;
            }
            for (LineValidOrderDTO lineOrder : lineOrders) {
                LineOrderAddressUpdateCommand.LineData lineDate = new LineOrderAddressUpdateCommand.LineData();
                lineDate.setOrderId(lineOrder.getOrderId());
                String lineType = lineInfo.getLineType();
                if(StrUtil.equals(lineType, CrmLineTypeEnum.TYPE_10.getType())){
                    lineDate.setSendAddrId(lineInfo.getSendAddrId());
                    lineDate.setStartSendLinker(command.getStartSendLinker());
                    lineDate.setStartSendPhoneNumber(command.getStartSendPhoneNumber());
                }else if(StrUtil.equals(lineType, CrmLineTypeEnum.TYPE_20.getType())){
                    lineDate.setReceiveAddrId(lineInfo.getSendAddrId());
                    lineDate.setEndReceiveLinker(command.getStartSendLinker());
                    lineDate.setEndReceivePhoneNumber(command.getStartSendPhoneNumber());
                }else {
                    continue;
                }
                lineDateList.add(lineDate);
            }
        }
        if(CollUtil.isEmpty(lineDateList)){
            return;
        }
        LineOrderAddressUpdateCommand lineGoodsAddressUpdateCommand = new LineOrderAddressUpdateCommand();
        lineGoodsAddressUpdateCommand.setLineDateList(lineDateList);
        ResultMode resultMode = omsOrderAddressInter.lineGoodsAddressUpdate(lineGoodsAddressUpdateCommand);
        log.info("更新订单联系人信息返回结果:{}",JSONUtil.toJsonStr(resultMode));
    }

    /**
     * 我要发货-订单列表查询
     */
    public ResultMode<ShipperOrderPageListDTO> shipperOrderPageQuery(PagingInfo<ShipperOrderPageQuery> pagingInfo) {
        OmsShipperOrderPageQuery query = BeanUtil.toBean(pagingInfo.getFilterModel(), OmsShipperOrderPageQuery.class);
        PagingInfo<OmsShipperOrderPageQuery> page = new PagingInfo<>(query,
                pagingInfo.currentPage, pagingInfo.pageLength, pagingInfo.isCountTotal());
        ResultMode<OmsShipperOrderPageListDTO> resultMode = omsOrderQueryInter.shipperOrderPage(page);

        if (!resultMode.isSucceed()) {
            return ResultMode.fail(resultMode.getErrCode(), resultMode.getErrMsg());
        }
        if (IterUtil.isEmpty(resultMode.getModel())) {
            return ResultMode.success();
        }

        ResultMode<ShipperOrderPageListDTO> result = new ResultMode<>();
        result.setModel(BeanUtil.copyToList(resultMode.getModel(), ShipperOrderPageListDTO.class));
        result.setTotal(resultMode.getTotal());
        return result;
    }

    /**
     * 我要承运-订单列表查询
     */
    public ResultMode<CarrierOrderPageListDTO> carrierOrderPageQuery(PagingInfo<CarrierOrderPageQuery> pagingInfo) {
        OmsCarrierOrderPageQuery query = BeanUtil.toBean(pagingInfo.getFilterModel(), OmsCarrierOrderPageQuery.class);
        PagingInfo<OmsCarrierOrderPageQuery> page = new PagingInfo<>(query,
                pagingInfo.currentPage, pagingInfo.pageLength, pagingInfo.isCountTotal());

        ResultMode<OmsCarrierOrderPageListDTO> resultMode = omsOrderQueryInter.carrierOrderPage(page);

        if (!resultMode.isSucceed()) {
            return ResultMode.fail(resultMode.getErrCode(), resultMode.getErrMsg());
        }
        if (IterUtil.isEmpty(resultMode.getModel())) {
            return ResultMode.success();
        }

        ResultMode<CarrierOrderPageListDTO> result = new ResultMode<>();
        result.setModel(BeanUtil.copyToList(resultMode.getModel(), CarrierOrderPageListDTO.class));
        result.setTotal(resultMode.getTotal());
        return result;
    }

    /**
     * 根据orderId获取订单地址信息
     */
    public OmsOrderAddressDTO orderAddressQuery(String orderId) {
        if (StrUtil.isBlank(orderId)) {
            return null;
        }
        ResultMode<com.wanlianyida.oms.api.model.dto.OmsOrderAddressDTO> resultMode = omsOrderAddressInter.getByOrderId(orderId);
        if (ObjUtil.isNull(resultMode) || !resultMode.isSucceed() || IterUtil.isEmpty(resultMode.getModel())) {
            return null;
        }

        return BeanUtil.toBean(IterUtil.getFirst(resultMode.getModel()), OmsOrderAddressDTO.class);
    }
}
