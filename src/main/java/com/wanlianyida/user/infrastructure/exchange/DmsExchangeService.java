package com.wanlianyida.user.infrastructure.exchange;

import cn.hutool.core.bean.BeanUtil;
import com.wanlianyida.dms.api.inter.aggregator.DmsWaybillSummaryInter;
import com.wanlianyida.dms.api.model.dto.aggregator.WaybillSummaryResDTO;
import com.wanlianyida.dms.api.model.query.aggregator.DmsWaybillSummaryListQuery;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.user.application.model.dto.analysis.WaybillSummaryDTO;
import com.wanlianyida.user.application.model.query.analysis.WaybillSummaryQuery;
import com.wanlianyida.user.infrastructure.config.ItsConfig;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR> @Date 2025/9/2 20:44
 * @Description
 */
@Service
public class DmsExchangeService {

    @Resource
    private DmsWaybillSummaryInter dmsWaybillSummaryInter;
    @Resource
    private ItsConfig itsConfig;
    public ResultMode<WaybillSummaryDTO> waybillList(PagingInfo<WaybillSummaryQuery> waybillSummaryQueryPagingInfo) {
        PagingInfo<DmsWaybillSummaryListQuery> queryPagingInfo = new PagingInfo<>();
        queryPagingInfo.setCurrentPage(waybillSummaryQueryPagingInfo.getCurrentPage());
        queryPagingInfo.setPageLength(waybillSummaryQueryPagingInfo.getPageLength());
        queryPagingInfo.setCountTotal(true);
        DmsWaybillSummaryListQuery waybillSummaryListQuery = BeanUtil.toBean(waybillSummaryQueryPagingInfo.getFilterModel(), DmsWaybillSummaryListQuery.class);
        waybillSummaryListQuery.setGoodsCustomerName(waybillSummaryQueryPagingInfo.getFilterModel().getCustomerName());
        waybillSummaryListQuery.setAppCode(itsConfig.getAppCode());
        queryPagingInfo.setFilterModel(waybillSummaryListQuery);
        ResultMode<WaybillSummaryResDTO> resultMode = dmsWaybillSummaryInter.waybillList(queryPagingInfo);
        if (!resultMode.isSucceed()) {
            return ResultMode.fail("运单数据汇总查询失败");
        }
        ResultMode<WaybillSummaryDTO> waybillSummaryDTOResultMode = new ResultMode<>();
        waybillSummaryDTOResultMode.setTotal(resultMode.getTotal());
        List<WaybillSummaryDTO> list = BeanUtil.copyToList(resultMode.getModel(),WaybillSummaryDTO.class);
        waybillSummaryDTOResultMode.setModel(list);
        return waybillSummaryDTOResultMode;
    }
}
