package com.wanlianyida.user.infrastructure.exchange;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.basecont.api.inter.*;
import com.wanlianyida.basecont.api.model.command.ContractSyncCommand;
import com.wanlianyida.basecont.api.model.dto.ContractDetailDTO;
import com.wanlianyida.basecont.api.model.dto.ContractEsignSignflowDTO;
import com.wanlianyida.basecont.api.model.dto.ContractSignTemplateDTO;
import com.wanlianyida.basecont.api.model.dto.OssKeyContractSignFlowDTO;
import com.wanlianyida.basecont.api.model.query.*;
import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.user.application.model.dto.cont.ContractTemplateBatchDTO;
import com.wanlianyida.user.application.model.query.cont.ContractTemplateBatchQuery;
import com.wanlianyida.user.application.model.query.cont.NeedSignContractQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;

@Slf4j
@Component
public class ContExchangeService {

    @Resource
    private ContractSignFlowInter contractSignFlowInter;

    @Resource
    private ContractSignTemplateInter contractSignTemplateInter;

    @Resource
    private ContractWillSignInter contractWillSignInter;

    @Resource
    private ContractBussInter contractBussInter;

    @Resource
    private GtspContractOnlineInter gtspContractOnlineInter;

    /**
     * 合同签署流程通用查询
     * @param query
     * @return
     */
    public ResponseMessage<List<OssKeyContractSignFlowDTO>> queryContractSignFlowByCondition(ContractSignFlowQuery query){
        log.info("queryContractSignFlowByCondition#查询请求参数:{}", JSONUtil.toJsonStr(query));
        return contractSignFlowInter.queryContractSignFlowByCondition(query);
    }

    /**
     * 通过模板id获取模板对象
     *
     * @param query
     * @return {@link ResponseMessage }<{@link ContractSignTemplateDTO }>
     */
    public ResponseMessage<ContractSignTemplateDTO> getContractSignTemplateById(ContractSignTemplateQuery query){
        log.info("getContractSignTemplateById#查询请求参数query:{}", JSONUtil.toJsonStr(query));
        ResponseMessage<List<ContractSignTemplateDTO>> responseMessage = contractSignTemplateInter.getContractSignTemplateById(query);
        if (responseMessage.isSucceed() && CollUtil.isNotEmpty(responseMessage.getModel())) {
            return ResponseMessage.success(CollUtil.getFirst(responseMessage.getModel()));
        }
        return ResponseMessage.success();
    }


    /**
     * 合同意愿签
     * @param query
     * @return
     */
    public ResponseMessage<List<String>> getWillSignPageUrl(ContractWillSignQuery query){
        log.info("getWillSignPageUrl#请求参数：{}",JSONUtil.toJsonStr(query));
        return contractWillSignInter.getWillSignPageUrl(query);
    }

    /**
     * 批量查询协议模板
     * @param query
     * @return
     */
    public List<ContractTemplateBatchDTO> batchTemplateQuery(ContractTemplateBatchQuery query) {
        log.info("query#查询请求参数：{}", JSONUtil.toJsonStr(query));
        List<ContractTemplateBatchDTO>  list = new ArrayList<>();
        List<String> templateIdList = query.getTemplateIdList();
        templateIdList.stream().forEach(templateId -> {
            ResponseMessage<List<String>> responseMessage = contractBussInter.queryDetailUrlByTemplateId(templateId, null);
            if(ObjUtil.isNotEmpty(responseMessage) && responseMessage.isSucceed() && !CollUtil.isEmpty(responseMessage.getModel())){
                String url = responseMessage.getModel().get(0);
                ContractTemplateBatchDTO contractTemplateBatchDTO = new ContractTemplateBatchDTO();
                contractTemplateBatchDTO.setTemplateId(templateId);
                contractTemplateBatchDTO.setUrl(url);
                list.add(contractTemplateBatchDTO);
            }
        });
        return list;
    }

    /**
     * 需要签署的协议
     * @param query
     * @return
     */
    public Map<String, Object> needSignContract(NeedSignContractQuery query) {
        log.info("needSignContract#请求参数:{}", JSONUtil.toJsonStr(query));
        ContractOperationMainBodyRequestQuery contractOperationMainBodyRequestQuery = BeanUtil.toBean(query, ContractOperationMainBodyRequestQuery.class);
        ResponseMessage<List<Map<String, Object>>> resultModel = contractSignTemplateInter.findEsignSignBymainBodyId(contractOperationMainBodyRequestQuery);
        if(ObjUtil.isNull(resultModel) || !resultModel.isSucceed() || resultModel.getModel() == null){
            log.info("needSignContract#返回结果:{}", JSONUtil.toJsonStr(resultModel));
            return null;
        }
        return resultModel.getModel().get(0);
    }

    /**
     * 根据业务Ids查询合同集合
     */
    public List<ContractEsignSignflowDTO> queryEsignFlowByBizIds(List<String> orderIdList) {
        if (IterUtil.isEmpty(orderIdList)) {
            return null;
        }

        ContractEsignSignFlowQuery query = new ContractEsignSignFlowQuery();
        query.setBizIds(orderIdList);
        ResponseMessage<List<ContractEsignSignflowDTO>> responseMessage = contractSignFlowInter.queryEsignFlowByBizIds(query);
        if(ObjUtil.isNull(responseMessage) || !responseMessage.isSucceed() || IterUtil.isEmpty(responseMessage.getModel())){
            return null;
        }

        return responseMessage.getModel();
    }

    /**
     * 根据业务Ids查询合同集合
     */
    public Map<String, ContractEsignSignflowDTO> queryEsignFlowMapByBizIds(List<String> orderIdList) {
        List<ContractEsignSignflowDTO> list = queryEsignFlowByBizIds(orderIdList);
        if (IterUtil.isEmpty(list)) {
            return null;
        }
        return list.stream().collect(Collectors.toMap(ContractEsignSignflowDTO::getBizId, v -> v));
    }


    /**
     * 获取合同
     * @param query
     * @return
     */
    public ContractDetailDTO queryContractDetailByCondition(ContractListQuery query) {
        ResponseMessage<List<ContractDetailDTO>> responseMessage = gtspContractOnlineInter.list(query);
        log.info("queryContractDetailByCondition#请求参数:{}#返回结果:{}", JSONUtil.toJsonStr(query),JSONUtil.toJsonStr(responseMessage));
        if(ObjUtil.isNull(responseMessage) || !responseMessage.isSucceed() || IterUtil.isEmpty(responseMessage.getModel())){
            return null;
        }
        return CollUtil.getFirst(responseMessage.getModel());
    }


    /**
     * 同步物流信息
     */
    public void syncLogisticsInfo(String contractId, String goodsId,String orderId) {
        ResponseMessage<Void> voidResponseMessage = gtspContractOnlineInter.syncLogisticsInfo(createContractSyncCommand(contractId, goodsId, orderId));
        log.info("syncLogisticsInfo#请求参数:contractId:{},goodsId:{},orderId:{}#返回结果:{}", contractId,goodsId,orderId,JSONUtil.toJsonStr(voidResponseMessage));
    }

    private ContractSyncCommand createContractSyncCommand(String contractId, String goodsId,String orderId) {
        ContractSyncCommand command = new ContractSyncCommand();
        ContractSyncCommand.SyncInfo syncInfo = new ContractSyncCommand.SyncInfo();
        syncInfo.setContractId(Long.valueOf(contractId));
        syncInfo.setOrderId(Long.valueOf(orderId));
        syncInfo.setGoodsId(Long.valueOf(goodsId));
        command.setSyncInfoList(Collections.singletonList(syncInfo));
        return command;
    }

}
