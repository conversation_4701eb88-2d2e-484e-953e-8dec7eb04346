package com.wanlianyida.user.infrastructure.exchange;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.gps.api.inter.GpsDeviceInter;
import com.wanlianyida.gps.api.model.query.GpsDeviceOwnerQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class GpsExchangeService {

    @Resource
    private GpsDeviceInter gpsDeviceInter;


    /**
     * 查询设备或者车俩邦迪的设备是公司id
     * @param query
     * @return
     */
    public String getDeviceOwner(GpsDeviceOwnerQuery query) {
        ResultMode<String> resultMode = gpsDeviceInter.getDeviceOwner(query);
        if(ObjUtil.isNull(resultMode) || !resultMode.isSucceed() || CollUtil.isEmpty(resultMode.getModel())){
            return null;
        }
        return resultMode.getModel().get(0);
    }
}
