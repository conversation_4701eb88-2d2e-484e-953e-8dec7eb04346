package com.wanlianyida.user.infrastructure.exchange;

import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.rms.api.inter.api.TransportDistanceInter;
import com.wanlianyida.rms.api.inter.model.command.TransportDistanceParamCommand;
import com.wanlianyida.rms.api.inter.model.dto.RiskExecInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年08月07日 17:26
 */
@Slf4j
@Service
public class RmsExchangeService {

    @Resource
    private TransportDistanceInter transportDistanceInter;


    /**
     * 测算网络货运业务运输距离
     */
    public ResultMode<RiskExecInfoDTO> distanceRuleCalculate(TransportDistanceParamCommand command) {
        log.info("【绑定货源码】网络货运业务运输距离风控测算入参：{}", command);
        ResultMode<RiskExecInfoDTO> resultMode = transportDistanceInter.ruleCalculate(command);
        log.info("【绑定货源码】网络货运业务运输距离风控测算出参：{}", resultMode);
        return resultMode;
    }
}
