package com.wanlianyida.user.infrastructure.exchange;

import cn.hutool.core.util.ObjUtil;
import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.fssuserauth.api.inter.platform.PlatformInfoInter;
import com.wanlianyida.fssuserauth.api.model.dto.LoginSystemDTO;
import com.wanlianyida.fssuserauth.api.model.query.PlatformInfoSystemQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

@Slf4j
@Service
public class UserAuthExchangeService {


    @Resource
    private PlatformInfoInter platformInfoInter;

    /**
     * 通过手机号获取中台个人账户信息
     */
    public List<LoginSystemDTO> listSystem(String loginId) {
        PlatformInfoSystemQuery param = new PlatformInfoSystemQuery();
        param.setLoginId(loginId);
        ResponseMessage<List<LoginSystemDTO>> mdmUserInfoDTOResponseMessage = platformInfoInter.listSystem(param);
        if(ObjUtil.isNotNull(mdmUserInfoDTOResponseMessage) && mdmUserInfoDTOResponseMessage.isSucceed() && ObjUtil.isNotNull(mdmUserInfoDTOResponseMessage.getModel())){
            return mdmUserInfoDTOResponseMessage.getModel();
        }
        return new ArrayList<>();
    }
}
