package com.wanlianyida.user.infrastructure.exchange;

import com.wanlianyida.base.its.api.inter.BigDataCommonInter;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.user.infrastructure.config.ItsConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年07月24日 15:12
 */
@Slf4j
@Component
public class ItsExchangeService {

    @Resource
    private ItsConfig itsConfig;
    @Resource
    private BigDataCommonInter bigDataCommonInter;

    public ResultMode<Map<String, Object>> callRequest(Map<String, Map<String, Object>> params) {
        Map<String, String> headers = new HashMap<>();
        headers.put("appCode", itsConfig.getAppCode());
        log.info("【工作台数据统计】调用大数据接口请求参数：{}", params);
        ResponseMessage<Map<String, Object>> responseMessage = bigDataCommonInter.callRequest(params, headers);
        log.info("【工作台数据统计】调用大数据接口响应：{}", responseMessage);
        if (!responseMessage.isSucceed()){
            return ResultMode.fail(responseMessage.getCode(), responseMessage.getMessage());
        }
        return ResultMode.success(responseMessage.getModel());
    }
}
