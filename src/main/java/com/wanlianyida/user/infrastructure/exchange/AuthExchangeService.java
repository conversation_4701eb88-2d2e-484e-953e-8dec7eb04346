package com.wanlianyida.user.infrastructure.exchange;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.basemdm.api.inter.MdmAccountInter;
import com.wanlianyida.basemdm.api.model.command.MdmPasswordCommand;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.user.application.model.command.platform.ForgetPasswordCommand;
import com.wanlianyida.user.application.model.command.platform.ResetPasswordCommand;
import com.wanlianyida.user.application.model.command.platform.UpdatePasswordCommand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class AuthExchangeService {

    @Resource
    private MdmAccountInter mdmAccountInter;


    /**
     * 更新密码
     * @param command
     * @return
     */
    public ResultMode<String> updatePassword(UpdatePasswordCommand command){
        MdmPasswordCommand passwordCommand = new MdmPasswordCommand();
        passwordCommand.setOldPassword(command.getPassword());
        passwordCommand.setPassword(command.getNewPassword());
        ResponseMessage<Void> responseMessage = mdmAccountInter.changePassword(passwordCommand);
        log.info("updatePassword#请求参数:{},返回值：{}", JSONUtil.toJsonStr(passwordCommand), JSONUtil.toJsonStr(responseMessage));
        if(ObjUtil.isNotNull(responseMessage) && responseMessage.isSucceed()){
            return ResultMode.success();
        }
        return ResultMode.fail(responseMessage.getMessage());
    }

    /**
     * 重置密码
     * @param command
     * @return
     */
    public ResultMode resetPassword(ResetPasswordCommand command) {
        MdmPasswordCommand passwordCommand = new MdmPasswordCommand();
        passwordCommand.setPassword(command.getNewPassword());
        ResponseMessage<Void> responseMessage = mdmAccountInter.changePassword(passwordCommand);
        log.info("resetPassword#请求参数:{},返回值：{}", JSONUtil.toJsonStr(passwordCommand), JSONUtil.toJsonStr(responseMessage));
        if(ObjUtil.isNotNull(responseMessage) && responseMessage.isSucceed()){
            return ResultMode.success();
        }
        return ResultMode.fail(responseMessage.getMessage());
    }

    /**
     * 忘记密码
     * @param command
     * @return
     */
    public ResultMode forgetPassword(ForgetPasswordCommand command) {
        MdmPasswordCommand passwordCommand = new MdmPasswordCommand();
        passwordCommand.setPassword(command.getNewPassword());
        ResponseMessage<Void> responseMessage = mdmAccountInter.changePassword(passwordCommand);
        log.info("forgetPassword#请求参数:{},返回值：{}", JSONUtil.toJsonStr(passwordCommand), JSONUtil.toJsonStr(responseMessage));
        if(ObjUtil.isNotNull(responseMessage) && responseMessage.isSucceed()){
            return ResultMode.success();
        }
        return ResultMode.fail(responseMessage.getMessage());
    }
}
