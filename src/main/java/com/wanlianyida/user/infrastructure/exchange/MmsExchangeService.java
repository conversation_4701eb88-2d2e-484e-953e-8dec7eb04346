package com.wanlianyida.user.infrastructure.exchange;

import cn.hutool.core.util.ObjUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.framework.lgicommon.enums.ResultCode;
import com.wanlianyida.mms.api.inter.MmsCouponInter;
import com.wanlianyida.mms.api.model.query.MmsCouponRuleQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

import javax.annotation.Resource;

@Slf4j
@Component
public class MmsExchangeService {

    @Resource
    private MmsCouponInter mmsCouponInter;


    public ResultMode<Map<String, String>> ruleInformation(MmsCouponRuleQuery query) {
        if(ObjUtil.isNull(query)){
            return ResultMode.fail(ResultCode.PT_PARAM_EMPTY.getCode(),ResultCode.PT_PARAM_EMPTY.getMsg());
        }
        return mmsCouponInter.ruleInformation(query);
    }
}
