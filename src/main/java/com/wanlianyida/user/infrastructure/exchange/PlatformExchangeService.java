package com.wanlianyida.user.infrastructure.exchange;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.framework.lgicore.utils.JwtUtil;
import com.wanlianyida.platform.api.inter.*;
import com.wanlianyida.platform.api.model.command.FindCompanyListByPlfUserCommand;
import com.wanlianyida.platform.api.model.command.PlfUsrAgreeOrRefuseBindCommand;
import com.wanlianyida.platform.api.model.command.PlfUsrBindCommand;
import com.wanlianyida.platform.api.model.command.PlfUsrUnBindCommand;
import com.wanlianyida.platform.api.model.dto.*;
import com.wanlianyida.platform.api.model.query.*;
import com.wanlianyida.user.application.model.command.crm.AddressAddCommand;
import com.wanlianyida.user.application.model.command.crm.AddressEditCommand;
import com.wanlianyida.user.application.model.command.platform.CompanyLinkmanInfoCommand;
import com.wanlianyida.user.application.model.command.platform.ExternalPlfUsrAgreeOrRefuseBindCommand;
import com.wanlianyida.user.application.model.command.platform.ExternalPlfUsrBindCommand;
import com.wanlianyida.user.application.model.command.platform.ExternalPlfUsrUnBindCommand;
import com.wanlianyida.user.application.model.dto.platform.CompanyLinkmanInfoDTO;
import com.wanlianyida.user.application.model.dto.platform.ProtocolBatchDTO;
import com.wanlianyida.user.application.model.query.platform.CompanyLinkmanInfoQuery;
import com.wanlianyida.user.application.model.query.platform.ExternalCompanyMemberQuery;
import com.wanlianyida.user.application.model.query.platform.ExternalPlfUsrCompanyMemberQuery;
import com.wanlianyida.user.application.model.query.platform.ProtocolBatchQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * platform exchange service impl
 */
@Slf4j
@Service
public class PlatformExchangeService {

    @Resource
    private PlatformUmDriverInter platformUmDriverInter;
    @Resource
    private PlatformUmUserbaseinfoInter platformUmUserbaseinfoInter;
    @Resource
    private PlatformUmCompanyInter platformUmCompanyInter;
    @Resource
    private PlatformCmOperationMainBodyInter platformCmOperationMainBodyInter;
    @Resource
    private PlatformCmPlatformParameterInter platformCmPlatformParameterInter;

    @Resource
    private PlatformCompanyConfigInter platformCompanyConfigInter;

    @Resource
    private PlatformCmProtocolInter platformCmProtocolInter;

    @Resource
    private PlatformCmParameterInter platformCmParameterInter;

    @Resource
    private PlatformCompanyLinkmanInter platformCompanyLinkmanInter;

    @Resource
    private PlfCompanyMemberPlfUsrInter plfCompanyMemberPlfUsrInter;
    /**
     * 司机用户id批量查询司机收款信息
     *
     * @param userBaseIdList
     * @return {@link List }<{@link PlatformUmDriverReceiptInfoDTO }>
     */
    public List<PlatformUmDriverReceiptInfoDTO> getDriverReceiptInfoByUserBaseIdList(List<String> userBaseIdList) {
        if (IterUtil.isEmpty(userBaseIdList)) {
            return Collections.emptyList();
        }
        PlatformUmDriverReceiptInfoQuery query = new PlatformUmDriverReceiptInfoQuery();
        query.setUserBaseIdList(userBaseIdList);
        log.info("getDriverReceiptInfoByUserBaseIdList#query->{}", JSONUtil.toJsonStr(query));
        ResultMode<PlatformUmDriverReceiptInfoDTO> resultMode = platformUmDriverInter.getDriverReceiptInfoByUserBaseIdList(query);
        if (Objects.isNull(resultMode) || !resultMode.isSucceed() || IterUtil.isEmpty(resultMode.getModel()) || ObjUtil.isNull(CollUtil.getFirst(resultMode.getModel()))) {
            return Collections.emptyList();
        }
        return resultMode.getModel();
    }

    /**
     * 批量查询司机基础信息
     * @param userBaseIdList
     * @return {@link List }<{@link PlatformUmUserbaseinfoDTO }>
     */
    public List<PlatformUmUserbaseinfoDTO> getDriverUserBaseByUserBaseIdList(List<String> userBaseIdList) {
        log.info("getDriverUserBaseByUserBaseIdList#userBaseIdList->{}", JSONUtil.toJsonStr(userBaseIdList));
        if (IterUtil.isEmpty(userBaseIdList)) {
            return Collections.emptyList();
        }
        PlatformUmUserbaseinfoQuery query = new PlatformUmUserbaseinfoQuery();
        query.setUserBaseIdList(userBaseIdList);
        ResultMode<PlatformUmUserbaseinfoDTO> resultMode = platformUmUserbaseinfoInter.getUserBaseInfoByUserBaseIdList(query);
        if (Objects.isNull(resultMode) || !resultMode.isSucceed() || IterUtil.isEmpty(resultMode.getModel()) || ObjUtil.isNull(CollUtil.getFirst(resultMode.getModel()))) {
            return Collections.emptyList();
        }
        return resultMode.getModel();
    }

    /**
     * 条件查询company
     */
    public PlatformUmCompanyDTO getCompanyByModel(String companyId) {
        PlatformUmCompanyQuery platformUmCompanyQuery = new PlatformUmCompanyQuery();
        platformUmCompanyQuery.setCompanyId(companyId);
        ResultMode<PlatformUmCompanyDTO> resultMode = platformUmCompanyInter.getCompanyByModel(platformUmCompanyQuery);
        if (!resultMode.isSucceed() || IterUtil.isEmpty(resultMode.getModel())) {
            return null;
        }
        return IterUtil.getFirst(resultMode.getModel());
    }

    /**
     * 根据公司ID查询平台运营主体管理表
     */
    public PlatformCmOperationMainBodyDTO getMainBodyInfoByCompanyId(String companyId) {
        if (StrUtil.isEmpty(companyId)) {
            return null;
        }
        ResultMode<PlatformCmOperationMainBodyDTO> resultMode = platformCmOperationMainBodyInter.getMainBodyInfoByCompanyId(companyId);
        if (!resultMode.isSucceed() || IterUtil.isEmpty(resultMode.getModel())) {
            return null;
        }
        return IterUtil.getFirst(resultMode.getModel());
    }
    /**
     * 根据id查询平台运营主体
     */
    public PlatformCmOperationMainBodyDetailDTO getMainBodyById(String id) {
        if(StrUtil.isEmpty(id)){
            return null;
        }
        ResultMode<PlatformCmOperationMainBodyDetailDTO> resultMode = platformCmOperationMainBodyInter.getMainBodyById(id);
        if (!resultMode.isSucceed() || IterUtil.isEmpty(resultMode.getModel())) {
            return null;
        }
        return IterUtil.getFirst(resultMode.getModel());
    }




    /**
     * 获取网络货运主体
     *
     * @param companyId
     * @return
     */
    public ResultMode getNetworkMainBody(String companyId) {
        ResultMode<NetworkMainBodyDTO> networkMainBodyResultMode = platformUmCompanyInter.getNetworkMainBody(companyId, "", "2");
        if (!networkMainBodyResultMode.isSucceed()) {
            return ResultMode.fail("未设置默认网络货运主体!");
        }

        List<NetworkMainBodyDTO> list = networkMainBodyResultMode.getModel().stream().filter(obj -> StrUtil.isNotBlank(obj.getDefaultMain()) && obj.getDefaultMain().equals("1")).collect(Collectors.toList());
        if (CollUtil.isEmpty(list)) {
            log.info("未查找到网络货运主体,手动审核！！");
            return null;
        }

        if (list.size() > 1) {
            log.info("网络货运主体不唯一,请手动审核！！");
            return null;
        }
        return ResultMode.success(CollUtil.getFirst(list));
    }

    /**
     * 查询参数配置
     */
    public String getParamValue(String paramCode) {
        if (StrUtil.isBlank(paramCode)) {
            return null;
        }
        ResultMode<PlatformCmPlatformParameterDTO> resultMode = platformCmPlatformParameterInter.getByParaCode(paramCode);
        if (ObjUtil.isNull(resultMode) || !resultMode.isSucceed() || CollUtil.isEmpty(resultMode.getModel())) {
            return null;
        }
        return IterUtil.getFirst(resultMode.getModel()).getParaValue();
    }

    public PlatformUmCompanyMainDTO getDiffPrice(String networkMainBodyId, String companyId) {
        if (!StrUtil.isAllNotBlank(networkMainBodyId, companyId)) {
            return null;
        }

        ResultMode<PlatformUmCompanyMainDTO> resultMode = platformUmCompanyInter.getDiffPrice(companyId, "2", networkMainBodyId);
        if (resultMode.isSucceed() && IterUtil.isNotEmpty(resultMode.getModel())) {
            return IterUtil.getFirst(resultMode.getModel());
        }

        return null;
    }

    public ResultMode<Map<String, Object>> selectConfigByCompanyId(CompanyConfigQuery query) {
        return platformCompanyConfigInter.selectConfigByCompanyId(query);
    }

    /**
     * 查询协议
     * @param queryPagingInfo
     * @return
     */
    public List<ProtocolBatchDTO> batchQuery(PagingInfo<ProtocolBatchQuery> queryPagingInfo) {
        log.info("batchQuery#查询协议:{}", JSONUtil.toJsonStr(queryPagingInfo));
        PagingInfo<PlatformCmProtocolQuery> pagingInfo = JSONUtil.toBean(JSONUtil.toJsonStr(queryPagingInfo), new TypeReference<PagingInfo<PlatformCmProtocolQuery>>() {
        }, false);
        ResultMode<PlatformCmProtocolDTO> resultMode = platformCmProtocolInter.platformCmProtocolPaging(pagingInfo);
        if (Objects.isNull(resultMode) || !resultMode.isSucceed() || IterUtil.isEmpty(resultMode.getModel()) || ObjUtil.isNull(CollUtil.getFirst(resultMode.getModel()))) {
            return Collections.emptyList();
        }
        return BeanUtil.copyToList(resultMode.getModel(),ProtocolBatchDTO.class);
    }

    /**
     * 新增企业联系人
     * @param command
     * @return
     */
    public ResultMode companyLinkmanAdd(CompanyLinkmanInfoCommand command) {
        log.info("companyLinkmanAdd#新增企业联系人请求参数:{}", JSONUtil.toJsonStr(command));
        com.wanlianyida.platform.api.model.command.CompanyLinkmanInfoCommand bean = BeanUtil.toBean(command, com.wanlianyida.platform.api.model.command.CompanyLinkmanInfoCommand.class);
        return platformCompanyLinkmanInter.addLinkman(bean);
    }

    /**
     * 根据公司id查询取整方式
     */
    public String getCompanyRoundMode(String companyId) {
        if (StrUtil.isBlank(companyId)) {
            return null;
        }

        ResultMode<String> resultMode = platformUmCompanyInter.getCompanyRoundMode(companyId);
        if (!resultMode.isSucceed() || IterUtil.isEmpty(resultMode.getModel())) {
            return null;
        }

        return IterUtil.getFirst(resultMode.getModel());
    }

    /**
     * 查询允许修改运单目的地标识
     * @return
     */
    public String getParaValueByOperIdAndParaKey(ParaValueByOperIdAndParaKeyQuery query) {

        com.wanlianyida.framework.lgicommon.entity.ResultMode<String> resultMode = platformCmParameterInter.getParaValueByOperIdAndParaKey(query);
        if (!resultMode.isSucceed() || IterUtil.isEmpty(resultMode.getModel())) {
            return "";
        }
        return resultMode.getModel().get(0);
    }


    /**
     * 新增联系人
     * @param command
     */
    public void addLinkman(AddressAddCommand command) {
        com.wanlianyida.platform.api.model.command.CompanyLinkmanInfoCommand companyLinkmanInfoCommand = new com.wanlianyida.platform.api.model.command.CompanyLinkmanInfoCommand();
        companyLinkmanInfoCommand.setCompanyId(JwtUtil.getTokenInfo().getCompanyId());
        companyLinkmanInfoCommand.setCreatorId(JwtUtil.getTokenInfo().getUserBaseId());
        companyLinkmanInfoCommand.setLinkType(40);
        companyLinkmanInfoCommand.setLinkName(command.getStartSendLinker());
        companyLinkmanInfoCommand.setLinkMobile(command.getStartSendPhoneNumber());
        ResultMode resultMode = platformCompanyLinkmanInter.addLinkman(companyLinkmanInfoCommand);
        log.info("新增联系人返回结果:{}",JSONUtil.toJsonStr(resultMode));
    }

    /**
     * 更新联系人
     * @param command
     */
    public void updateLinkman(AddressEditCommand command) {
        com.wanlianyida.platform.api.model.command.CompanyLinkmanInfoCommand companyLinkmanInfoCommand = new com.wanlianyida.platform.api.model.command.CompanyLinkmanInfoCommand();
        companyLinkmanInfoCommand.setId(command.getLinkmanId());
        companyLinkmanInfoCommand.setCompanyId(JwtUtil.getTokenInfo().getCompanyId());
        companyLinkmanInfoCommand.setCreatorId(JwtUtil.getTokenInfo().getUserBaseId());
        companyLinkmanInfoCommand.setLinkName(command.getStartSendLinker());
        companyLinkmanInfoCommand.setLinkMobile(command.getStartSendPhoneNumber());
        platformCompanyLinkmanInter.updateLinkman(companyLinkmanInfoCommand);
    }

    /**
     * 查询企业联系人
     * @param query
     * @return
     */
    public ResultMode<CompanyLinkmanInfoDTO> companyLinkmanListQuery(CompanyLinkmanInfoQuery query) {
        com.wanlianyida.platform.api.model.query.CompanyLinkmanInfoQuery infoQuery = BeanUtil.toBean(query, com.wanlianyida.platform.api.model.query.CompanyLinkmanInfoQuery.class);
        log.info("companyLinkmanListQuery#查询参数:{}", JSONUtil.toJsonStr(infoQuery));
        ResultMode<com.wanlianyida.platform.api.model.dto.CompanyLinkmanInfoDTO> resultMode = platformCompanyLinkmanInter.queryaLinkmanList(infoQuery);
        if(ObjUtil.isNull(resultMode)){
            return ResultMode.fail("查询企业联系人失败");
        }
        if(!resultMode.isSucceed()){
            return ResultMode.fail(resultMode.getErrMsg());
        }
        if(CollUtil.isEmpty(resultMode.getModel())){
            log.info("companyLinkmanListQuery#查询企业联系人为空");
            return ResultMode.success();
        }
        List<CompanyLinkmanInfoDTO> companyLinkmanInfoDTOS = BeanUtil.copyToList(resultMode.getModel(), CompanyLinkmanInfoDTO.class);
        return ResultMode.success(companyLinkmanInfoDTOS);
    }
    public List<PlatformUmUserbaseinfoDTO> getUserBaseInfoByUserBaseIdList(List<String> userBaseIdList) {
        PlatformUmUserbaseinfoQuery query = new PlatformUmUserbaseinfoQuery();
        query.setUserBaseIdList(userBaseIdList);
        ResultMode<PlatformUmUserbaseinfoDTO> resultMode = platformUmUserbaseinfoInter.getUserBaseInfoByUserBaseIdList(query);
        if (resultMode.isSucceed()) {
            return resultMode.getModel();
        }
        return null;
    }

    /**
     * 获取企业员工列表
     */
    public ResultMode<CompanyMemberDTO> companyMemberPage(PagingInfo<ExternalCompanyMemberQuery> pageInfo) {
        CompanyMemberQuery query = BeanUtil.toBean(pageInfo.getFilterModel(), CompanyMemberQuery.class);
        query.setCompanyId(JwtUtil.getCompanyIdByToken());
        PagingInfo<CompanyMemberQuery> pagingInfo = new PagingInfo<>();
        BeanUtil.copyProperties(pageInfo, pagingInfo, CopyOptions.create().ignoreNullValue().ignoreError());
        pagingInfo.setFilterModel(query);
        return plfCompanyMemberPlfUsrInter.companyMemberPage(pagingInfo);
    }

    /**
     * 获取平台个人关联的企业员工列表
     */
    public ResultMode<PlfUsrCompanyMemberDTO> plfUsrCompanyMemberPage(PagingInfo<ExternalPlfUsrCompanyMemberQuery> pageInfo) {
        PlfUsrCompanyMemberQuery query = BeanUtil.toBean(pageInfo.getFilterModel(), PlfUsrCompanyMemberQuery.class);
        PagingInfo<PlfUsrCompanyMemberQuery> pagingInfo = new PagingInfo<>();
        BeanUtil.copyProperties(pageInfo, pagingInfo, CopyOptions.create().ignoreNullValue().ignoreError());
        pagingInfo.setFilterModel(query);
        return plfCompanyMemberPlfUsrInter.plfUsrCompanyMemberPage(pagingInfo);
    }

    /**
     * 获取当前企业的员工绑定的个人账户信息
     */
    public ResultMode<FindPlfUserDTO> findPlfUser() {
        FindPlfUserQuery query = new FindPlfUserQuery();
        query.setUserBaseId(JwtUtil.getTokenInfo().getUserBaseId());
        log.info("findPlfUser#查询请求参数:{}", JSONUtil.toJsonStr(query));
        return plfCompanyMemberPlfUsrInter.findPlfUser(query);
    }

    /**
     * 获取当前企业的员工绑定的个人账户信息
     */
    public FindComMemberDTO findBindComMemberByPlfUserCompanyId(String plfUserId) {
        FindComMemberQuery findComMemberQuery = new FindComMemberQuery();
        findComMemberQuery.setCompanyId(JwtUtil.getTokenInfo().getCompanyId());
        findComMemberQuery.setPlfUserId(plfUserId);
        ResultMode<FindComMemberDTO> resultMode = plfCompanyMemberPlfUsrInter.findBindComMemberByPlfUserCompanyId(findComMemberQuery);
        if (ObjUtil.isNotNull(resultMode) && resultMode.isSucceed() && IterUtil.isNotEmpty(resultMode.getModel())) {
            return IterUtil.getFirst(resultMode.getModel());
        }
        return null;
    }

    /**
     * 绑定接口
     */
    public ResultMode<?> bind(ExternalPlfUsrBindCommand command) {
        PlfUsrBindCommand plfUsrBindCommand = BeanUtil.toBean(command, PlfUsrBindCommand.class);
        plfUsrBindCommand.setCompanyId(JwtUtil.getCompanyIdByToken());
        plfUsrBindCommand.setCreatorId(JwtUtil.getTokenInfo().getUserBaseId());
        plfUsrBindCommand.setCreatorName(JwtUtil.getTokenInfo().getUsername());
        return plfCompanyMemberPlfUsrInter.bind(plfUsrBindCommand);
    }

    /**
     * 解绑接口
     */
    public ResultMode<?> unbind(ExternalPlfUsrUnBindCommand command) {
        return plfCompanyMemberPlfUsrInter.unbind(BeanUtil.toBean(command, PlfUsrUnBindCommand.class));
    }

    /**
     * 同意绑定
     */
    public ResultMode<?> agreeOrRefuseBind(ExternalPlfUsrAgreeOrRefuseBindCommand command) {
        return plfCompanyMemberPlfUsrInter.agreeOrRefuseBind(BeanUtil.toBean(command, PlfUsrAgreeOrRefuseBindCommand.class));
    }

    public ResultMode<?> findCompanyOperatorListByPlfUser(FindCompanyListByPlfUserCommand command){
        log.info("findCompanyOperatorListByPlfUser#查询请求参数:{}", JSONUtil.toJsonStr(command));
        ResultMode<MdmOperatorListDTO> resultMode = plfCompanyMemberPlfUsrInter.findCompanyOperatorListByPlfUser(command);
        log.info("findCompanyOperatorListByPlfUser#查询返回结果:{}", JSONUtil.toJsonStr(resultMode));
        return resultMode;
    }


    public FindCompanyMemberDTO findCompanyMember(String loginId,String loginName,String mobile) {
        FindCompanyMemberQuery query = new FindCompanyMemberQuery();
        query.setLoginId(loginId);
        query.setLoginName(loginName);
        query.setLoginMobile(mobile);
        ResultMode<FindCompanyMemberDTO> resultMode = plfCompanyMemberPlfUsrInter.findCompanyMember(query);
        log.info("findCompanyMember#查询返回结果:{}", JSONUtil.toJsonStr(resultMode));
        if (ObjUtil.isNotNull(resultMode) && resultMode.isSucceed() && IterUtil.isNotEmpty(resultMode.getModel())) {
            return IterUtil.getFirst(resultMode.getModel());
        }
        return null;
    }

    /**
     * 条件查询company
     */
    public List<PlatformUmCompanyDTO> getCompanyListByModel(PlatformUmCompanyQuery platformUmCompanyQuery) {
        ResultMode<PlatformUmCompanyDTO> resultMode = platformUmCompanyInter.getCompanyByModel(platformUmCompanyQuery);
        log.info("getCompanyListByModel#请求参数:{},#查询返回结果:{}", JSONUtil.toJsonStr(platformUmCompanyQuery) ,JSONUtil.toJsonStr(resultMode));
        if (!resultMode.isSucceed() || IterUtil.isEmpty(resultMode.getModel())) {
            return new ArrayList<>();
        }
        return resultMode.getModel();
    }

}
