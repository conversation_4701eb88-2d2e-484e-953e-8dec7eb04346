package com.wanlianyida.user.infrastructure.exchange;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.framework.lgicommon.entity.TokenInfo;
import com.wanlianyida.framework.lgicore.utils.JwtUtil;
import com.wanlianyida.tms.api.inter.FreightReminderInter;
import com.wanlianyida.tms.api.model.command.FreightCommunicateCommand;
import com.wanlianyida.tms.api.model.dto.FreightReminderDTO;
import com.wanlianyida.tms.api.model.dto.FreightReminderLogDTO;
import com.wanlianyida.tms.api.model.dto.FreightReminderTimeDTO;
import com.wanlianyida.tms.api.model.query.FreightReminderLogQuery;
import com.wanlianyida.tms.api.model.query.FreightReminderQuery;
import com.wanlianyida.tms.api.model.query.FreightReminderTimeQuery;
import com.wanlianyida.user.application.model.command.tms.ExterFreightCommunicateCommand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

@Slf4j
@Service
public class FreightReminderExchangeService {

    @Resource
    private FreightReminderInter freightReminderInter;

    /**
     * 查询运费催缴和回复记录
     */
    public ResultMode<FreightReminderDTO> queryFreightReminder(FreightReminderQuery query) {
        return freightReminderInter.queryFreightReminder(query);
    }

    /**
     * 催付回复
     */
    public ResultMode communicateFreight(ExterFreightCommunicateCommand command) {
        FreightCommunicateCommand freightCommunicateCommand= BeanUtil.toBean(command,FreightCommunicateCommand.class);
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        freightCommunicateCommand.setUserBaseId(tokenInfo.getUserBaseId());
        return freightReminderInter.communicateFreight(freightCommunicateCommand);
    }

    /**
     * 查询运费催付时间
     */
    public FreightReminderTimeDTO getFreightReminderTime(FreightReminderTimeQuery query) {
        ResultMode<FreightReminderTimeDTO> resultMode = freightReminderInter.getFreightReminderTime(query);
        if (resultMode.isSucceed() && CollUtil.isNotEmpty(resultMode.getModel())) {
            return CollUtil.getFirst(resultMode.getModel());
        }
        return null;
    }

    /**
     * 查询运费催付日志
     */
    public List<FreightReminderLogDTO> getFreightReminderLog(FreightReminderLogQuery query) {
        ResultMode<FreightReminderLogDTO> resultMode = freightReminderInter.getFreightReminderLog(query);
        if (resultMode.isSucceed()) {
            return resultMode.getModel();
        }
        return null;
    }
}
