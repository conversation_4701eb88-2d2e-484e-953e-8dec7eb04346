package com.wanlianyida.user.infrastructure.exchange;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.fssbaselog.api.inter.LogOperationRecordInter;
import com.wanlianyida.fssbaselog.api.model.command.LogOperationRecordCommand;
import com.wanlianyida.fssbaselog.api.model.dto.LogOperationRecordDTO;
import com.wanlianyida.fssbaselog.api.model.query.LogOperationRecordQuery;
import com.wanlianyida.fssmodel.ResponseMessage;
import com.wanlianyida.user.application.model.dto.log.OperRecordDTO;
import com.wanlianyida.user.application.model.query.log.OperRecordQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

import javax.annotation.Resource;

@Slf4j
@Service
public class LogExchangeService {

    @Resource
    private LogOperationRecordInter logOperationRecordInter;

    /**
     * 查询操作记录
     * @param query
     * @return
     */
    public ResultMode<OperRecordDTO> operRecordQuery(PagingInfo<OperRecordQuery> query) {
        log.info("operRecordQuery#操作记录请求参数:{}", JSONUtil.toJsonStr(query));

        com.wanlianyida.fssmodel.PagingInfo<LogOperationRecordQuery> pagingInfo = JSONUtil.toBean(JSONUtil.toJsonStr(query), new TypeReference<com.wanlianyida.fssmodel.PagingInfo<LogOperationRecordQuery>>() {
        }, false);
        ResponseMessage<List<LogOperationRecordDTO>> responseMessage = logOperationRecordInter.queryPage(pagingInfo);
        if(ObjUtil.isNull(responseMessage) || !responseMessage.isSucceed() || CollUtil.isEmpty(responseMessage.getModel())){
            return ResultMode.success();
        }
        List<LogOperationRecordDTO> logOperationRecordDTOS = responseMessage.getModel();
        List<OperRecordDTO> operRecordDTOS = BeanUtil.copyToList(logOperationRecordDTOS, OperRecordDTO.class);

        ResultMode success = ResultMode.success(operRecordDTOS);
        success.setTotal(responseMessage.getTotal());
        return success;
    }

    /**
     * 新增操作记录
     * @param command
     */
    public void add(LogOperationRecordCommand command){
        logOperationRecordInter.add(command);
    }
}
