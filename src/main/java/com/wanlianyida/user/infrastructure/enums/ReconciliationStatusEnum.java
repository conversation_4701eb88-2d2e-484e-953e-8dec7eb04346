package com.wanlianyida.user.infrastructure.enums;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

@Getter
public enum ReconciliationStatusEnum {

    ONE("1", "已对账"),
    TWO("2", "对账退回"),
    ZERO("0", "未对账") //是否对账【0:未对账,1:已对账】
    ,
    ;
    private String desc;
    private String code;


    ReconciliationStatusEnum(String code, String desc) {
        this.desc = desc;
        this.code = code;
    }

    public static String statusMapView(String status){
        if(StrUtil.isBlank(status)){
            return null;
        }
        if(StrUtil.equals("600",status)){
            return "2";
        }
        return "1";
    }

}
