package com.wanlianyida.user.infrastructure.enums;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
public enum BusStatusEnum {

    status_99("99","未回单"),
    status_100("100","待审核"),
    status_110("110","待审核"),
    status_120("120","审核不通过"),
    status_130("130","审核通过"),
    status_200("200","待对账"),
    status_210("210","对账退回"),
    status_220("220","对账通过"),
    status_300("300","待处理"),
    status_310("310","已保存"),
    status_320("320","预审通过转结算"),
    status_330("330","待预审"),
    status_340("340","预审不通过"),
    ;
    public String status;
    public String desc;

    BusStatusEnum(String status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static List<String> allNotReconciliation(){
        return Arrays.stream(values()).filter(v -> !StrUtil.equals(v.getStatus(),"200")).map(v->v.getStatus()).collect(Collectors.toList());
    }

    public static String statusMapView(String status){
        if(StrUtil.isBlank(status)){
            return null;
        }
        if(StrUtil.equals("220",status)){
            return "1";
        }
        return "0";
    }


}
