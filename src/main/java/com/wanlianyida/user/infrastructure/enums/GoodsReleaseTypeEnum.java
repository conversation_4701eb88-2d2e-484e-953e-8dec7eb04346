package com.wanlianyida.user.infrastructure.enums;

import cn.hutool.core.util.ObjUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 发布类型枚举
 */
@Getter
@AllArgsConstructor
public enum GoodsReleaseTypeEnum {

    NORMAL(1, "普通"),
    DOWNSTREAM_INQUIRY(2, "下游询价"),
    TRANSFER_TRANSACTION(3, "转交易"),
    ;

    private final Integer type;

    private final String desc;

    /**
     * 检查type是否存在于枚举中
     */
    public static boolean isTypeExist(Integer type) {
        if(ObjUtil.isNull(type)){
            return false;
        }
        return Arrays.stream(values())
            .anyMatch(v -> ObjUtil.equals(v.getType(), type));
    }

}
