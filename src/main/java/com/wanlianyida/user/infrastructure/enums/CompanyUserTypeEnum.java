package com.wanlianyida.user.infrastructure.enums;

import lombok.Getter;

/**
 * 企业用户类型枚举
 */
@Getter
public enum CompanyUserTypeEnum {
    
    INTERNAL("10", "内部用户"),
    EXTERNAL("20", "外部用户");
    
    private final String code;
    private final String desc;
    
    CompanyUserTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public static CompanyUserTypeEnum getByCode(String code) {
        for (CompanyUserTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }
    
    public boolean equals(String code) {
        return this.code.equals(code);
    }
}