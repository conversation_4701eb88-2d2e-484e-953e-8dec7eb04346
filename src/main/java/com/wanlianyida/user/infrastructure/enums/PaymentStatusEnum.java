package com.wanlianyida.user.infrastructure.enums;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
public enum PaymentStatusEnum {
    // 付款状态：【select:100-待申请,200-待审核,300-待付款,400-审核不通过,500-付款中,600-支付成功,700-支付失败】
    ONE_HUNDRED("100", "待申请"),
    TWO_HUNDRED("200", "待审核"),
    THREE_HUNDRED("300", "待付款"),
    FOUR_HUNDRED("400", "审核不通过"),
    FIVE_HUNDRED("500", "付款中"),
    SIX_HUNDRED("600", "支付成功"),
    SEVEN_HUNDRED("700", "支付失败"),
    EIGHT_HUNDRED("800", "对账退回"),
    ;
    public String code;
    public String name;

    private PaymentStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static List<String> allPaymentStatus(){
        return Arrays.stream(values()).map(v ->v.getCode()).collect(Collectors.toList());
    }

    public static List<String> allNotPaymentStatus(){
        return Arrays.stream(values()).filter(v-> !StrUtil.equals(v.getCode(),"600")).map(v ->v.getCode()).collect(Collectors.toList());
    }

    public static String statusMapView(String status){
        if(StrUtil.isBlank(status)){
            return null;
        }
        if(StrUtil.equals("600",status)){
            return "2";
        }
        return "1";
    }

}
