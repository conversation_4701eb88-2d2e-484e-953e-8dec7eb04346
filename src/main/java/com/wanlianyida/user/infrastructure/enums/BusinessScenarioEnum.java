package com.wanlianyida.user.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * 发布货源场景[100-司机货源,110-撮合管理,120-网络货运-司机货源,130-撮合发货-司机货源,140-物流计划单-司机货源,150-场站计划单-司机货源,160-货主端-司机货源,
 * 200-企业货源,210-招标管理,220-撮合发货-企业货源,221-撮合发货-招标货源,230-场站计划单-企业货源,240-转交易,250-下游询价,260-商贸询价单,270-货主端-企业货源,271-货主端-招标货源]
 */
@Getter
@AllArgsConstructor
public enum BusinessScenarioEnum {
    DRIVER("100", "10", "10", 1, "10", "司机货源"),
    MATCHING_FREIGHT_QUOTATION("110", "10", "20", 1, "20", "撮合管理"),
    NETWORK_FREIGHT_DRIVER("120", "10", "10", 1, "50", "网络货运-司机货源"),
    MATCHING_SHIPMENT_DRIVER("130", "10", "10", 1, "60", "撮合发货-司机货源"),
    LOGISTICS_PLAN_DRIVER("140", "10", "10", 1, "70", "物流计划单-司机货源"),
    YARD_PLAN_DRIVER("150", "10", "10", 1, "80", "场站计划单-司机货源"),
    DRIVER_SHIPPER("160", "10", "10", 1, "100", "货主端-司机货源"),

    ENTERPRISE("200", "20", "30", 1, "30", "企业货源"),
    BIDDING_MANAGEMENT("210", "20", "40", 1, "40", "招标管理"),
    MATCHING_SHIPMENT_ENTERPRISE("220", "20", "30", 1, "60", "撮合发货-企业货源"),
    MATCHING_SHIPMENT_TENDER("221", "20", "40", 1, "60", "撮合发货-招标货源"),
    YARD_PLAN_ENTERPRISE("230", "20", "30", 1, "80", "场站计划单-企业货源"),
    TRANSACTION_CONVERSION("240", "20", "30", 3, "30", "转交易"),
    DOWNSTREAM_QUOTATION("250", "20", "30", 2, "30", "下游询价"),
    TRADE_QUOTATION("260", "20", "30", 1, "90", "商贸询价单"),
    ENTERPRISE_SHIPPER("270", "20", "30", 1, "100", "货主端-企业货源"),
    BIDDING_SHIPPER("271", "20", "40", 1, "100", "货主端-招标管理"),
    ;

    private String code;

    /**
     * 发布主体类型 [10-司机货源 20-企业货源]
     */
    private String publisherType;
    /**
     * 业务模式类型 [10-司机货源 20-撮合货源 30-企业货源 40-招标货源]
     */
    private String bizModelType;
    /**
     * 发布类型 [1-普通,2-下游询价,3-转交易]
     */
    private Integer releaseType;
    /**
     * 来源入口类型 [10-司机货源,20-撮合管理,30-企业货源,40-招标管理,50-网络货运,60-撮合发货,70-物流计划单,80-场站计划单,90-商贸询价单]
     */
    private String sourceEntryType;

    private String desc;

    public static Map<String, BusinessScenarioEnum> businessScenarioMap = new HashMap<>();

    static {
        for (BusinessScenarioEnum value : values()) {
            businessScenarioMap.put(value.getCode(), value);
        }
    }

}
