package com.wanlianyida.user.infrastructure.enums;

import lombok.Getter;

/**
 * 枚举定义
 * <AUTHOR>
 * @date 2025/03/04
 */
public class LgiOrchUserEnum {

    /**
     * 选择司机类型
     */
    @Getter
    public enum SelectDriverTypeEnum {
        SELF("1", "自有司机"),
        PLATFORM("2", "平台司机"),
        ;
        private final String code;
        private final String desc;
        SelectDriverTypeEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    /**
     * 查询车辆类型
     */
    @Getter
    public enum SearchCarTypeEnum {
        PLATFORM("1", "平台"),
        SELF("2", "自有"),
        SUPPLEMENT("3", "运单补录"),
        ;
        private final String code;
        private final String desc;
        SearchCarTypeEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    /**
     * 转换查询车辆类型
     */
    @Getter
    public enum ChangeCarTypeEnum {
        SELF("10", "自有"),
        PLATFORM("20", "平台"),
        ;
        private final String code;
        private final String desc;
        ChangeCarTypeEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    /**
     * 司机实名认证状态
     */
    @Getter
    public enum DriverESiginStatusEnum {
        UNVERIFIED("0", "未认证"),
        VERIFIED("1", "已认证"),
        ;
        private final String code;
        private final String desc;
        DriverESiginStatusEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    /**
     * 司机注册状态
     */
    @Getter
    public enum DriverLoginStatusEnum {
        ONLINE("11", "线上"),
        OFFLINE("21", "线下"),
        NET("12", "网络"),
        ;
        private final String code;
        private final String desc;
        DriverLoginStatusEnum(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    /**
     * 车辆状态
     */
    @Getter
    public enum ChangeCarStatusEnum {
        ENABLE("10", "1","启用"),
        DISABLE("20", "2","停用"),
        APPLY("40", "4","合作申请"),
        ;
        private final String tcsCode;
        private final String code;
        private final String desc;
        ChangeCarStatusEnum(String tcsCode, String code, String desc) {
            this.tcsCode = tcsCode;
            this.code = code;
            this.desc = desc;
        }
        public static String getCodeByTcsCode(String tcsCode) {
            for (ChangeCarStatusEnum r : ChangeCarStatusEnum.values()) {
                if (r.getTcsCode().equals(tcsCode)) {
                    return r.getCode();
                }
            }
            return "";
        }
    }
}
