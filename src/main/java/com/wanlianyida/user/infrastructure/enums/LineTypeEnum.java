package com.wanlianyida.user.infrastructure.enums;

import lombok.Getter;

/**
 * 线路类型
 */
@Getter
public enum LineTypeEnum {

    /**
     * 常用线路
     */
    COMMON_LINE("10", "常用线路"),
    /**
     * 发布货源
     */
    RELEASE_GOODS("20", "发布货源"),
    /**
     * 订单补录
     */
    ORDER_SUPPLEMENT("30", "订单补录"),
    /**
     * 批量补录
     */
    BATCH_SUPPLEMENT("40", "批量补录");

    String name;
    String code;

    LineTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

}
