package com.wanlianyida.user.infrastructure.enums;

import lombok.Getter;

/**
 * 开关状态枚举
 */
@Getter
public enum SwitchStatusEnum {
    
    ON("10", "开启"),
    OFF("20", "关闭");
    
    private final String code;
    private final String desc;
    
    SwitchStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public static SwitchStatusEnum getByCode(String code) {
        for (SwitchStatusEnum statusEnum : values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }
    
    public boolean equals(String code) {
        return this.code.equals(code);
    }
}