package com.wanlianyida.user.infrastructure.enums;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
public enum InvoicedStatusEnum {
    status_10("10","待申请"),
    status_20("20","待审核"),
    status_30("30","审核不通过"),
    status_35("35","已驳回"),
    status_40("40","审核通过"),
    status_50("50","开票成功"),
    status_60("60","已邮寄"),
    ;
    public String status;
    public String desc;

    InvoicedStatusEnum(String status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static List<String> allNotInvoicedStatus(){
        return Arrays.stream(values()).filter(v-> !StrUtil.equals(v.getStatus(),"50")).map(v ->v.getStatus()).collect(Collectors.toList());
    }

    public static String statusMapView(String status){
        if(StrUtil.isBlank(status)){
            return null;
        }
        if(StrUtil.equals("50",status)){
            return "20";
        }
        return "10";
    }
}
