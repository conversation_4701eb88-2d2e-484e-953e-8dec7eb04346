package com.wanlianyida.user.infrastructure.enums;

import lombok.Getter;

/**
 * 业务类型枚举
 */
@Getter
public enum ContractTypeEnum {
    
    FREIGHT("120", "网货业务"),
    DISPATCH("130", "承运业务");
    
    private final String code;
    private final String desc;
    
    ContractTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public static ContractTypeEnum getByCode(String code) {
        for (ContractTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }
    
    public boolean equals(String code) {
        return this.code.equals(code);
    }
}